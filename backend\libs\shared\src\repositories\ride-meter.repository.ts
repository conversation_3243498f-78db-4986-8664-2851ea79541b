import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  RideMeter,
  CreateRideMeterData,
  UpdateRideMeterData,
  RideMeterFilters,
  BulkCreateRideMeterData,
} from './models/rideMeter.model';

@Injectable()
export class RideMeterRepository extends BaseRepository<RideMeter> {
  protected readonly modelName = 'rideMeter';

  constructor(protected readonly prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new ride meter
   */
  async createRideMeter(data: CreateRideMeterData): Promise<RideMeter> {
    const rideMeter = await this.model.create({
      data: {
        rideId: data.rideId,
        name: data.name,
        value: data.value,
        unit: data.unit,
      },
      include: {
        ride: true,
      },
    });

    return this.mapToModel(rideMeter);
  }

  /**
   * Create multiple ride meters in a single transaction
   */
  async createBulkRideMeters(
    data: BulkCreateRideMeterData,
  ): Promise<RideMeter[]> {
    const rideMeters = await this.prisma.$transaction(
      data.meters.map((meter: any) =>
        this.model.create({
          data: {
            rideId: data.rideId,
            name: meter.name,
            value: meter.value,
            unit: meter.unit,
          },
          include: {
            ride: true,
          },
        }),
      ),
    );

    return rideMeters.map(
      (meter: Prisma.RideMeterGetPayload<{ include: { ride: true } }>) =>
        this.mapToModel(meter),
    );
  }

  /**
   * Find ride meters by ride ID
   */
  async findByRideId(rideId: string): Promise<RideMeter[]> {
    const rideMeters = await this.model.findMany({
      where: {
        rideId,
        deletedAt: null,
      },
      include: {
        ride: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return rideMeters.map(
      (meter: Prisma.RideMeterGetPayload<{ include: { ride: true } }>) =>
        this.mapToModel(meter),
    );
  }

  /**
   * Find ride meters by ride ID and meter name
   */
  async findByRideIdAndName(
    rideId: string,
    name: string,
  ): Promise<RideMeter | null> {
    const rideMeter = await this.model.findFirst({
      where: {
        rideId,
        name,
        deletedAt: null,
      },
      include: {
        ride: true,
      },
    });

    return rideMeter ? this.mapToModel(rideMeter) : null;
  }

  /**
   * Find ride meters with filters
   */
  async findWithFilters(filters: RideMeterFilters): Promise<RideMeter[]> {
    const whereClause: any = {
      deletedAt: null,
    };

    if (filters.rideId) {
      whereClause.rideId = filters.rideId;
    }

    if (filters.name) {
      whereClause.name = filters.name;
    }

    if (filters.names && filters.names.length > 0) {
      whereClause.name = {
        in: filters.names,
      };
    }

    if (filters.unit) {
      whereClause.unit = filters.unit;
    }

    if (filters.minValue !== undefined) {
      whereClause.value = {
        ...whereClause.value,
        gte: filters.minValue,
      };
    }

    if (filters.maxValue !== undefined) {
      whereClause.value = {
        ...whereClause.value,
        lte: filters.maxValue,
      };
    }

    if (filters.createdAfter) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        gte: filters.createdAfter,
      };
    }

    if (filters.createdBefore) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        lte: filters.createdBefore,
      };
    }

    const rideMeters = await this.model.findMany({
      where: whereClause,
      include: {
        ride: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return rideMeters.map(
      (meter: Prisma.RideMeterGetPayload<{ include: { ride: true } }>) =>
        this.mapToModel(meter),
    );
  }

  /**
   * Update ride meter by ID
   */
  async updateRideMeter(
    id: string,
    data: UpdateRideMeterData,
  ): Promise<RideMeter> {
    const rideMeter = await this.model.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.value !== undefined && { value: data.value }),
        ...(data.unit && { unit: data.unit }),
      },
      include: {
        ride: true,
      },
    });

    return this.mapToModel(rideMeter);
  }

  /**
   * Delete ride meter by ID (soft delete)
   */
  async deleteRideMeter(id: string): Promise<void> {
    await this.model.update({
      where: { id },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Delete all ride meters for a ride (soft delete)
   */
  async deleteByRideId(rideId: string): Promise<void> {
    await this.model.updateMany({
      where: { rideId },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Check if ride meter exists by ride ID and name
   */
  async existsByRideIdAndName(rideId: string, name: string): Promise<boolean> {
    const count = await this.model.count({
      where: {
        rideId,
        name,
        deletedAt: null,
      },
    });

    return count > 0;
  }

  /**
   * Map Prisma result to model
   */
  private mapToModel(data: any): RideMeter {
    return {
      id: data.id,
      rideId: data.rideId,
      name: data.name,
      value: data.value,
      unit: data.unit,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      ride: data.ride,
    };
  }
}

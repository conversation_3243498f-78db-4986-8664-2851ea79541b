'use client';

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Spinner } from '@/components/ui/spinner';

interface DeleteLanguageDialogProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
   isLoading: boolean;
}

export function DeleteLanguageDialog({
   isOpen,
   onClose,
   onConfirm,
   isLoading,
}: DeleteLanguageDialogProps) {
   return (
      <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <AlertDialogContent>
            <AlertDialogHeader>
               <AlertDialogTitle>Delete Language</AlertDialogTitle>
               <AlertDialogDescription>
                  Are you sure you want to delete this language? This action cannot be undone.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel onClick={onClose} disabled={isLoading}>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  onClick={onConfirm}
                  disabled={isLoading}
                  className='bg-red-600 hover:bg-red-700'
               >
                  {isLoading ? (
                     <>
                        <Spinner className='mr-2 h-4 w-4' />
                        Deleting...
                     </>
                  ) : (
                     'Delete'
                  )}
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
}
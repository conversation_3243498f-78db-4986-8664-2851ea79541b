import { Module } from '@nestjs/common';
import { CityController } from './city.controller';
import { CityModule as SharedCityModule } from '@shared/shared/modules/city/city.module';
import { CountryModule as SharedCountryModule } from '@shared/shared/modules/country/country.module';
import { VehicleTypeModule as SharedVehicleTypeModule } from '@shared/shared/modules/vehicle-type/vehicle-type.module';
import { ZoneTypeModule as SharedZoneTypeModule } from '@shared/shared/modules/zone-type/zone-type.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [
    SharedCityModule,
    SharedCountryModule,
    SharedVehicleTypeModule,
    SharedZoneTypeModule,
  ],
  providers: [CityAdminRepository],
  controllers: [CityController],
})
export class ApiCityModule {}

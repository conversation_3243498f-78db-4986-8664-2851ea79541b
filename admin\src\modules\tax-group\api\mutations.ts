import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateTaxGroupRequest, TaxGroupResponse, UpdateTaxGroupRequest } from '../types/tax-group';

/**
 * Hook for creating a new tax group
 */
export const useCreateTaxGroup = () => {
   return useMutation({
      mutationFn: async (data: CreateTaxGroupRequest): Promise<TaxGroupResponse> => {
         return apiClient.post('/tax-groups', data);
      },
   });
};

/**
 * Hook for updating a tax group
 */
export const useUpdateTaxGroup = () => {
   return useMutation({
      mutationFn: async (
         data: { id: string } & UpdateTaxGroupRequest
      ): Promise<TaxGroupResponse> => {
         const { id, ...payload } = data;
         return apiClient.put(`/tax-groups/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a tax group (soft delete)
 */
export const useDeleteTaxGroup = () => {
   return useMutation({
      mutationFn: async (
         id: string
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.delete(`/tax-groups/${id}`);
      },
   });
};

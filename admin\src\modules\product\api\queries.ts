import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { ProductResponse, ListProductParams, ListProductResponse } from '../types/product';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListProduct = ({
   page,
   limit,
   search,
   sortBy,
   sortOrder,
   vehicleTypeId,
   productServiceId,
   isEnabled,
   isDisabled,
}: ListProductParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: hasPermission(RBAC_PERMISSIONS.PRODUCT.LIST),
      queryKey: [
         'products',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
         vehicleTypeId,
         productServiceId,
         isEnabled,
         isDisabled,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListProductResponse> => {
         return apiClient.get('/products/paginate', {
            params: {
               page,
               limit,
               search,
               sortBy,
               sortOrder,
               vehicleTypeId,
               productServiceId,
               isEnabled,
               isDisabled,
            },
         });
      },
   });
};

export const useGetProduct = (id: string | null) => {
   return useQuery({
      queryKey: ['product', id],
      queryFn: (): Promise<ProductResponse> => {
         return apiClient.get(`/products/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

// Re-export hooks from their respective modules
export { useListVehicleCategory as useListVehicleTypes } from '@/modules/vehicle-category/api/queries';
export { useListProductService as useListProductServices } from '@/modules/product-service/api/queries';

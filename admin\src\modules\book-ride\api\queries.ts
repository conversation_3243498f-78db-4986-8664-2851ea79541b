import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { SearchRideRequest, SearchRideResponse } from '../types/book-ride';

/**
 * Hook for searching available rides (Admin)
 * Uses mutation instead of query because it's a POST endpoint
 */
export const useSearchRides = () => {
   return useMutation({
      mutationFn: async (data: SearchRideRequest): Promise<SearchRideResponse> => {
         return apiClient.post('/rides/search/admin', data);
      },
   });
};

import {
  Injectable,
  BadRequestException,
  Logger,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { LocationIngestorService } from '../location/location-ingestor.service';
import {
  GoogleRouteMatrixService,
  Location,
} from '@shared/shared/common/google/google-route-matrix.service';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';
import { DriverCityProductRepository } from '@shared/shared/repositories/driver-city-product.repository';
import { FareEngineService } from '../fare-engine/fare-engine.service';
import { CityProductRepository } from '@shared/shared/repositories/city-product.repository';
import { Zone } from '@shared/shared/repositories/models/zone.model';
import { CityProduct } from '@shared/shared/repositories/models/cityProduct.model';
import {
  FareContext,
  CityProductWithMetrics,
} from '../fare-engine/interfaces/interface';
import { FareCalculationOptions } from '../fare-engine/fare-calculator/interfaces/fare-calculation.interface';
import {
  DriverRouteData,
  NearbyDriver,
  ProductDriverMatch,
  ProductWithRelations,
  RideBookingType,
  RideSearchResult,
  RouteComputationResult,
  SearchRideParams,
  ServiceType,
} from './interface';
import { ProductService } from '../product/product.service';

@Injectable()
export class RideSearchService {
  private readonly logger = new Logger(RideSearchService.name);
  private readonly H3_RESOLUTION = 8; // Resolution 8 for zone matching
  // private readonly DEFAULT_DRIVER_LIMIT = 10;
  private readonly DEFAULT_PRICE = 100; // Dummy price for now

  constructor(
    private readonly h3UtilityService: H3UtilityService,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly locationIngestorService: LocationIngestorService,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
    private readonly driverCityProductRepository: DriverCityProductRepository,
    private readonly fareEngineService: FareEngineService,
    private readonly cityProductRepository: CityProductRepository,
  ) {}

  /**
   * Main method to search for available rides
   * 1. Convert pickup/drop coordinates to H3 indexes
   * 2. Lookup zones and determine city context
   * 3. Find nearby drivers around pickup
   * 4. Compute ETA and distance to pickup for each driver
   * 5. Fetch city products linked to these drivers (filtered by pickup city if applicable)
   * 6. Group drivers by city product
   * 7. Within each product group, find the driver with the shortest ETA
   * 8. Calculate fares and return response
   */
  async searchRides(params: SearchRideParams): Promise<RideSearchResult[]> {
    this.logger.log('Starting ride search...');

    // Step 1: Validate input parameters
    this.validatePickupTime(params);

    // Step 2: Convert pickup and drop coordinates into H3 indexes and determine zones
    const {
      pickupZone,
      destinationZone,
      pickupCityId,
      pickupZoneCityId,
      destinationZoneCityId,
    } = await this.getZonesAndCityFromCoordinates(params);

    this.logger.log(
      `Ride type: ${pickupCityId ? 'City' : 'Intercity'}, Pickup zone: ${pickupZone?.id || 'None'}, Destination zone: ${destinationZone?.id || 'None'}`,
    );

    // Step 3: Compute pickup to destination route
    const pickupToDestinationResult =
      await this.computePickupToDestinationRoute(params);
    this.logger.log(
      `Computed route from pickup to destination: ${JSON.stringify(pickupToDestinationResult)}`,
    );

    // Step 4: Handle "later" bookings (return products without driver matching)
    if (params.pickupType === RideBookingType.LATER) {
      // For later bookings, get products based on city
      const products = await this.getProductsForCity(
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
      );
      return this.mapProductsToRideSearchResults(
        products,
        pickupToDestinationResult,
        pickupZone,
        destinationZone,
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
        params.stops,
      );
    }

    // Step 5: Find nearby drivers around pickup and compute routes for "now" bookings
    const driverRouteData = await this.findNearbyDriversWithRoutes(params);
    this.logger.log(`Found ${driverRouteData.length} nearby drivers`);

    // Step 6: If no drivers found, return products without driver data
    if (driverRouteData.length === 0) {
      this.logger.log('No drivers with valid location data found');
      const products = await this.getProductsForCity(
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
      );
      return this.mapProductsToRideSearchResults(
        products,
        pickupToDestinationResult,
        pickupZone,
        destinationZone,
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
        params.stops,
      );
    }

    // Step 7: Fetch city products linked to these drivers (filtered by pickup city if applicable)
    const productDriverMatches = await this.getDriverCityProductGroups(
      driverRouteData,
      pickupCityId,
      pickupZoneCityId,
      destinationZoneCityId,
    );

    this.logger.log(
      `Found ${productDriverMatches.length} product-driver matches`,
    );

    // Step 8: If no matches found, return products without driver data
    if (productDriverMatches.length === 0) {
      this.logger.log('No product-driver matches found');
      const products = await this.getProductsForCity(
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
      );
      return this.mapProductsToRideSearchResults(
        products,
        pickupToDestinationResult,
        pickupZone,
        destinationZone,
        pickupCityId,
        pickupZoneCityId,
        destinationZoneCityId,
        params.stops,
      );
    }

    // Step 9: Calculate fares for matched products
    await this.calculateFaresForMatches(
      productDriverMatches,
      pickupToDestinationResult,
      pickupZone,
      destinationZone,
      pickupCityId,
      pickupZoneCityId,
      destinationZoneCityId,
      params.stops,
    );

    // Step 10: Build and return results
    const results = this.buildRideSearchResults(
      productDriverMatches,
      pickupToDestinationResult,
    );

    this.logger.log(`Returning ${results.length} ride options`);
    return results;
  }

  /**
   * Validates pickup time for later bookings
   */
  private validatePickupTime(params: SearchRideParams): void {
    if (params.pickupType === RideBookingType.LATER) {
      if (!params.pickupTime) {
        throw new BadRequestException(
          'Pickup time is required when pickupType is later',
        );
      }

      const pickupDate = new Date(params.pickupTime);
      const now = new Date();

      if (pickupDate <= now) {
        throw new BadRequestException('Pickup time must be in the future');
      }
    }
  }

  /**
   * Get zone information and city from coordinates
   * Returns:
   * - pickupCityId: City ID if both pickup and destination are in the same city (city ride)
   * - pickupCityId: null if different cities or no city (intercity ride)
   * - pickupZoneCityId: City ID of pickup zone (for intercity fare calculation)
   * - destinationZoneCityId: City ID of destination zone (for intercity fare calculation)
   */
  private async getZonesAndCityFromCoordinates(
    params: SearchRideParams,
  ): Promise<{
    pickupZone: Zone | null;
    destinationZone: Zone | null;
    pickupCityId: string | null;
    pickupZoneCityId: string | null;
    destinationZoneCityId: string | null;
  }> {
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );
    const destinationH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.destination.lat,
      params.destination.lng,
      this.H3_RESOLUTION,
    );

    // Get zones from H3 indexes for fare calculation
    const pickupZone = await this.h3IndexToZoneRepository.findByH3Index(
      BigInt(`0x${pickupH3Index}`),
    );
    const destinationZone = await this.h3IndexToZoneRepository.findByH3Index(
      BigInt(`0x${destinationH3Index}`),
    );

    let fareCityId = null;
    if (pickupZone?.zone) {
      fareCityId = pickupZone.zone.cityId;
    } else if (destinationZone?.zone) {
      fareCityId = destinationZone.zone.cityId;
    } else {
      fareCityId = null;
    }
    return {
      pickupZone: pickupZone?.zone || null,
      destinationZone: destinationZone?.zone || null,
      pickupCityId: fareCityId ?? null, // null for intercity, cityId for same-city rides
      pickupZoneCityId: pickupZone?.zone?.cityId ?? null, // City ID of pickup zone (or null)
      destinationZoneCityId: destinationZone?.zone?.cityId ?? null, // City ID of destination zone (or null)
    };
  }

  /**
   * Get products for a ride
   *
   * Logic:
   * 1. If pickupCityId is set (same city ride) → Fetch city products
   * 2. If pickupZoneCityId is set (intercity from city) → Fetch intercity products for that city
   * 3. If destinationZoneCityId is set (intercity to city) → Fetch intercity products for that city
   * 4. If neither (no city) → Return empty (no service available without drivers)
   */
  private async getProductsForCity(
    pickupCityId: string | null,
    pickupZoneCityId: string | null,
    destinationZoneCityId: string | null,
  ): Promise<ProductWithRelations[]> {
    if (
      pickupZoneCityId &&
      destinationZoneCityId &&
      pickupZoneCityId === destinationZoneCityId
    ) {
      this.logger.log(`Fetching city products for city: ${pickupZoneCityId}`);
      return await this.productService.findEnabledProductsByServiceIdentifiers([
        pickupZoneCityId,
      ]);
    } else if (pickupZoneCityId != destinationZoneCityId) {
      this.logger.log(`Fetching intercity products for city: ${pickupCityId}`);
      return await this.productService.findEnabledProductsByServiceIdentifiers(
        [],
      );
    }
    // if (pickupCityId) {
    //   this.logger.log(`Fetching city products for city: ${pickupCityId}`);
    //   return await this.productService.findEnabledProductsByServiceIdentifiers([
    //     pickupCityId,
    //   ]);
    // }
    else {
      // No city in pickup or destination - no service available without drivers
      this.logger.log(
        'No city in pickup/destination - returning empty (need drivers for city determination)',
      );
      return [];
    }
  }

  /**
   * Computes route from pickup to destination
   */
  private async computePickupToDestinationRoute(
    params: SearchRideParams,
  ): Promise<RouteComputationResult | null> {
    try {
      const result =
        await this.googleRouteMatrixService.computeDistanceAndDuration(
          { lat: params.pickup.lat, lng: params.pickup.lng },
          { lat: params.destination.lat, lng: params.destination.lng },
          params.stops,
        );

      if (!result) {
        this.logger.warn('No route data returned from Google service.');
        return null;
      }

      return {
        distanceMeters: result.distance,
        duration: result.duration,
      };
    } catch (error) {
      this.logger.error('Failed to compute pickup to destination route', error);
      return null;
    }
  }

  /**
   * Maps a single product to RideSearchResult
   */
  private mapProductToRideSearchResult(
    product: ProductWithRelations,
    pickupToDestinationResult: RouteComputationResult | null,
    driverData?: DriverRouteData,
    fare?: {
      amount: number;
      currency: string;
      cityProductId: string;
      cityProductFareId?: string;
    } | null,
    cityProduct?: CityProduct | null,
  ): RideSearchResult {
    // Extract pickup to destination data
    const pickupToDestinationDurationSeconds =
      pickupToDestinationResult?.duration
        ? this.parseDuration(pickupToDestinationResult.duration.toString())
        : null;
    const pickupToDestinationDistanceMeters =
      pickupToDestinationResult?.distanceMeters || null;

    // Extract driver to pickup data
    const driverToPickupDurationSeconds =
      driverData?.etaToPickupSeconds ?? null;
    const driverToPickupDistanceMeters =
      driverData?.distanceToPickupMeters ?? null;

    const driverToPickupArrivalTime =
      driverData &&
      driverToPickupDurationSeconds &&
      driverToPickupDurationSeconds > 0
        ? new Date(
            Date.now() + driverToPickupDurationSeconds * 1000,
          ).toISOString()
        : null;

    // Calculate driver to destination data
    const driverToDestinationDurationSeconds =
      (pickupToDestinationDurationSeconds ?? 0) +
      (driverToPickupDurationSeconds ?? 0);
    const driverToDestinationDistanceMeters =
      (pickupToDestinationDistanceMeters ?? 0) +
      (driverToPickupDistanceMeters ?? 0);
    const driverToDestinationArrivalTime =
      driverToDestinationDurationSeconds > 0
        ? new Date(
            Date.now() + driverToDestinationDurationSeconds * 1000,
          ).toISOString()
        : null;

    // Use calculated fare if available, otherwise use default price
    const price = fare?.amount || this.DEFAULT_PRICE;

    // Add 10% extra to the price
    const priceWithExtra = Math.round(price * 1.1);

    return {
      id: product.id,
      identifier: product.identifier ?? '',
      name: product.name,
      serviceName: product.productService?.name || 'Unknown Service',
      description: product.description || null,
      icon: product.icon || null,
      price: price,
      strikethroughPrice: priceWithExtra,
      passengerLimit: product.passengerLimit ?? 0,
      pickupToDestinationResult: {
        durationInSeconds: pickupToDestinationDurationSeconds,
        distanceMeters: pickupToDestinationDistanceMeters,
      },
      driverToPickupResults: {
        durationInSeconds: driverToPickupDurationSeconds,
        distanceMeters: driverToPickupDistanceMeters,
        estimatedArrivalTime: driverToPickupArrivalTime,
      },
      driverToDestinationResults: {
        durationInSeconds: driverToDestinationDurationSeconds,
        distanceMeters: driverToDestinationDistanceMeters,
        estimatedArrivalTime: driverToDestinationArrivalTime,
      },
      fare: fare || null,
      cityProduct: cityProduct
        ? {
            id: cityProduct.id,
            cityId: cityProduct.cityId,
            productId: cityProduct.productId,
          }
        : null,
    };
  }

  /**
   * Maps multiple products to RideSearchResults
   * Calculates fares based on city determination logic
   */
  private async mapProductsToRideSearchResults(
    products: ProductWithRelations[],
    pickupToDestinationResult: RouteComputationResult | null,
    pickupZone: Zone | null,
    destinationZone: Zone | null,
    pickupCityId: string | null,
    pickupZoneCityId: string | null,
    destinationZoneCityId: string | null,
    stops?: any[],
  ): Promise<RideSearchResult[]> {
    this.logger.log(
      `Mapping ${products.length} products to ride search results`,
    );

    let stopsZones: any[] = [];
    if (stops && stops.length > 0) {
      stopsZones = await Promise.all(
        stops.map((stop) => this.getZoneFromLocation(stop)),
      );
    }
    // Determine which city to use for fare calculation
    const fareCityId =
      pickupCityId || pickupZoneCityId || destinationZoneCityId;

    // If we have a city ID, try to calculate fares
    if (fareCityId) {
      const productIds = products.map((p) => p.id);

      // Get city products
      const cityProducts =
        await this.cityProductRepository.findCityProductsByProductIds(
          fareCityId,
          productIds,
        );

      const cityProductMap = new Map<string, CityProduct>();
      cityProducts.forEach((cp) => {
        cityProductMap.set(cp.productId, cp);
      });

      // Calculate fares for each product
      const results: RideSearchResult[] = [];
      for (const product of products) {
        const cityProduct = cityProductMap.get(product.id);
        let fare: {
          amount: number;
          currency: string;
          cityProductId: string;
          cityProductFareId?: string;
        } | null = null;

        if (cityProduct) {
          try {
            // Calculate metrics
            const metrics = this.calculateMetricsForCityProduct(
              pickupToDestinationResult,
              undefined, // No driver data for "later" bookings
            );

            this.logger.debug(
              `Metrics for later booking product ${product.name}: tripDistance=${metrics.tripDistance}km, tripDuration=${metrics.tripDuration}min`,
            );

            // Prepare city product with metrics
            const cityProductWithMetrics: CityProductWithMetrics = {
              ...cityProduct,
              metrics,
            };

            // Prepare fare context
            const fareContext: FareContext = {
              pickup: pickupZone,
              destination: destinationZone,
              stops: stopsZones,
              cityProducts: [cityProductWithMetrics],
              currency: 'INR',
              timestamp: new Date(),
              requestId: `ride-search-later-${Date.now()}-${product.id}`,
            };

            this.logger.debug(
              `Fare context for later booking product ${product.name}: pickup=${pickupZone?.name || 'None'}, destination=${destinationZone?.name || 'None'}, cityProductId=${cityProduct.id}`,
            );

            // Call fare engine with optimization options
            const fareOptions: FareCalculationOptions = {
              skipCommission: true,
              skipTaxBreakdown: false,
              skipChargeBreakdown: false,
            };

            const fareResult = await this.fareEngineService.estimateFare(
              fareContext,
              fareOptions,
            );

            this.logger.debug(
              `Fare result for later booking product ${product.name}: status=${fareResult?.status}, hasData=${!!fareResult?.data}`,
            );

            // Extract fare from result
            if (fareResult && fareResult.status && fareResult.data) {
              const fareCalculationResults =
                fareResult.data.fareCalculationResults || [];
              if (fareCalculationResults.length > 0) {
                const fareCalc = fareCalculationResults[0];
                fare = {
                  // keep to decimal points
                  amount: Number(fareCalc.passengerFare.toFixed(2)),
                  currency: fareCalc.currency,
                  cityProductId: fareCalc.cityProductId,
                  cityProductFareId: fareCalc.cityProductFareId,
                };
                this.logger.log(
                  `✓ Calculated fare for later booking product ${product.name}: ${fare.amount} ${fare.currency}`,
                );
              } else {
                this.logger.warn(
                  `No fare calculation results for later booking product ${product.name}`,
                );
              }
            } else {
              this.logger.warn(
                `Fare calculation failed for later booking product ${product.name}: status=${fareResult?.status}`,
              );
            }
          } catch (error) {
            this.logger.error(
              `Error calculating fare for product ${product.id}:`,
              error,
            );
          }
        }

        results.push(
          this.mapProductToRideSearchResult(
            product,
            pickupToDestinationResult,
            undefined,
            fare,
            cityProduct || null,
          ),
        );
      }

      return results;
    }

    // Fallback: no fare calculation for intercity
    return products.map((product) =>
      this.mapProductToRideSearchResult(
        product,
        pickupToDestinationResult,
        undefined,
        null,
        null,
      ),
    );
  }

  /**
   * Finds nearby drivers and computes route data
   */
  private async findNearbyDriversWithRoutes(
    params: SearchRideParams,
  ): Promise<DriverRouteData[]> {
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );
    this.logger.log(`Pickup H3 index for driver search: ${pickupH3Index}`);

    this.logger.log('Finding nearby drivers...');

    const nearbyDrivers: NearbyDriver[] =
      await this.locationIngestorService.findAllNearbyDrivers(
        pickupH3Index,
        2,
        50,
      );

    if (nearbyDrivers.length === 0) {
      this.logger.log('No drivers found in the area');
      return [];
    }
    this.logger.log(`Found ${nearbyDrivers.length} nearby drivers`);

    // Log driver details for debugging
    nearbyDrivers.forEach((driver, index) => {
      this.logger.log(
        `Driver ${index + 1}: ID=${driver.driverId}, hasMetadata=${!!driver.metadata}, lat=${driver.metadata?.lat}, lon=${driver.metadata?.lng}, productId=${driver.metadata?.productId}`,
      );
    });

    // Filter drivers with valid location data
    const validDrivers = nearbyDrivers.filter(
      (driver: NearbyDriver) => driver.metadata?.lat && driver.metadata?.lng,
    );

    this.logger.log(
      `Filtered to ${validDrivers.length} drivers with valid location data`,
    );

    if (validDrivers.length === 0) {
      this.logger.log('No drivers with valid location data found');
      return [];
    }

    // Get driver locations for route matrix API
    const driverLocations: Location[] = validDrivers.map(
      (driver: NearbyDriver) => ({
        lat: driver.metadata!.lat,
        lng: driver.metadata!.lng,
      }),
    );

    // Compute routes from drivers to pickup location
    this.logger.log('Computing route matrices for drivers to pickup...');

    const driversToPickupResults =
      await this.googleRouteMatrixService.computeDriversToPickup(
        driverLocations,
        { lat: params.pickup.lat, lng: params.pickup.lng },
      );

    this.logger.log(
      `Computed route matrices for ${driversToPickupResults.length} drivers`,
    );

    const driverRouteData: DriverRouteData[] = validDrivers.map(
      (driver: NearbyDriver, index: number) => {
        const routeResult = driversToPickupResults[index];

        const etaToPickupSeconds = this.parseDuration(
          (routeResult?.duration || '0').toString(),
        );

        const distanceToPickupMeters = routeResult?.distanceMeters || 0;
        const estimatedArrivalTime =
          etaToPickupSeconds > 0
            ? new Date(Date.now() + etaToPickupSeconds * 1000)
            : new Date();

        return {
          driverId: driver.driverId,
          etaToPickupSeconds,
          distanceToPickupMeters,
          estimatedArrivalTime: estimatedArrivalTime.toISOString(),
          productId: driver.metadata?.productId,
        };
      },
    );

    this.logger.log(
      `Computed route data for ${driverRouteData.length} drivers`,
    );

    return driverRouteData;
  }

  /**
   * Optimized method to get driver-city-product groups
   * Fetches city products linked to drivers and groups them by product
   *
   * City determination logic:
   * 1. If pickupCityId is set (same city ride) → Use pickupCityId
   * 2. If pickupZoneCityId is set (intercity from city) → Use pickupZoneCityId
   * 3. If destinationZoneCityId is set (intercity to city) → Use destinationZoneCityId
   * 4. If neither (no city zones) → Use driver's city from their city products
   */
  private async getDriverCityProductGroups(
    driverRouteData: DriverRouteData[],
    pickupCityId: string | null,
    pickupZoneCityId: string | null,
    destinationZoneCityId: string | null,
  ): Promise<ProductDriverMatch[]> {
    const productDriverMatches: ProductDriverMatch[] = [];

    this.logger.log(
      `Fetching city products for ${driverRouteData.length} drivers`,
    );

    // Extract driver IDs
    const driverIds = driverRouteData.map((driver) => driver.driverId);

    // Determine which city to use for intercity rides
    let intercityFareCityId: string | null = null;
    if (!pickupCityId) {
      // Intercity ride - determine city for fare calculation
      if (pickupZoneCityId) {
        intercityFareCityId = pickupZoneCityId;
        this.logger.log(
          `Intercity ride: Using pickup city ${intercityFareCityId} for fare calculation`,
        );
      } else if (destinationZoneCityId) {
        intercityFareCityId = destinationZoneCityId;
        this.logger.log(
          `Intercity ride: Using destination city ${intercityFareCityId} for fare calculation`,
        );
      } else {
        this.logger.log(
          'Intercity ride: No city in pickup/destination, will use driver city',
        );
      }
    }
    if (pickupZoneCityId != destinationZoneCityId) {
      pickupCityId = null;
    } // Get all driver city products for these drivers
    const driverCityProducts = await this.getDriverCityProducts(
      driverIds,
      pickupCityId,
      intercityFareCityId,
    );

    this.logger.log(
      `Found ${driverCityProducts.length} driver-city-product associations`,
    );

    if (driverCityProducts.length === 0) {
      return [];
    }

    // Group by product: productId -> { product, cityProduct, drivers[] }
    const productGroupMap = new Map<
      string,
      {
        product: ProductWithRelations;
        cityProduct: CityProduct;
        drivers: DriverRouteData[];
      }
    >();

    for (const dcp of driverCityProducts) {
      const productId = dcp.cityProduct.productId;

      // Find driver route data
      const driverData = driverRouteData.find(
        (d) => d.driverId === dcp.userProfileId,
      );

      if (!driverData) {
        continue;
      }

      // Get or create product group
      let productGroup = productGroupMap.get(productId);

      if (!productGroup) {
        // Create product with relations
        const product: ProductWithRelations = {
          id: dcp.cityProduct.product.id,
          name: dcp.cityProduct.product.name,
          identifier: dcp.cityProduct.product.identifier ?? null,
          description: dcp.cityProduct.product.description ?? null,
          icon: dcp.cityProduct.product.icon ?? null,
          passengerLimit: dcp.cityProduct.product.passengerLimit ?? 0,
          isEnabled: dcp.cityProduct.product.isEnabled,
          vehicleTypeId: dcp.cityProduct.product.vehicleTypeId,
          productServiceId: dcp.cityProduct.product.productServiceId ?? null,
          createdAt: dcp.cityProduct.product.createdAt,
          updatedAt: dcp.cityProduct.product.updatedAt,
          deletedAt: dcp.cityProduct.product.deletedAt ?? null,
          vehicleType: dcp.cityProduct.vehicleType,
          productService: dcp.cityProduct.product.productService,
        };

        productGroup = {
          product,
          cityProduct: dcp.cityProduct,
          drivers: [],
        };

        productGroupMap.set(productId, productGroup);
      }

      // Add driver to this product group
      productGroup.drivers.push(driverData);
    }

    this.logger.log(`Grouped into ${productGroupMap.size} unique products`);

    // For each product group, find the driver with the shortest ETA
    for (const [productId, group] of productGroupMap.entries()) {
      this.logger.log(
        `Product ${group.product.name} (ID: ${productId}): ${group.drivers.length} eligible drivers`,
      );

      // Sort drivers by ETA and pick the best one
      const bestDriver = group.drivers.sort(
        (a, b) => a.etaToPickupSeconds - b.etaToPickupSeconds,
      )[0];

      this.logger.log(
        `Best driver for product ${group.product.name}: ${bestDriver.driverId} (ETA: ${bestDriver.etaToPickupSeconds} sec)`,
      );

      productDriverMatches.push({
        product: group.product,
        driverData: bestDriver,
        cityProduct: group.cityProduct,
      });
    }

    return productDriverMatches;
  }

  /**
   * Calculate fares for product-driver matches
   * Works for both city rides and intercity rides
   *
   * City determination for fare calculation:
   * 1. If pickupCityId is set (same city ride) → Use pickupCityId
   * 2. If pickupZoneCityId is set (intercity from city) → Use pickupZoneCityId
   * 3. If destinationZoneCityId is set (intercity to city) → Use destinationZoneCityId
   * 4. If neither (no city) → Use driver's city from cityProduct
   */
  private async calculateFaresForMatches(
    productDriverMatches: ProductDriverMatch[],
    pickupToDestinationResult: RouteComputationResult | null,
    pickupZone: Zone | null,
    destinationZone: Zone | null,
    pickupCityId: string | null,
    pickupZoneCityId: string | null,
    destinationZoneCityId: string | null,
    stops?: any[],
  ): Promise<void> {
    // Determine which city to use for fare calculation
    const fareCityId =
      pickupCityId || pickupZoneCityId || destinationZoneCityId;

    this.logger.log(
      `Calculating fares for ${productDriverMatches.length} product matches (fareCityId: ${fareCityId || 'driver city'})`,
    );

    // Calculate fare for each match
    // Note: cityProduct is already set in the match from getDriverCityProductGroups
    for (const match of productDriverMatches) {
      if (!match.cityProduct) {
        this.logger.warn(
          `No city product found for product ${match.product.id}`,
        );
        continue;
      }

      try {
        // If no fareCityId, use the driver's city from their cityProduct
        const effectiveCityId = fareCityId || match.cityProduct.cityId;

        if (!effectiveCityId) {
          this.logger.warn(
            `No city ID available for fare calculation for product ${match.product.id}`,
          );
          continue;
        }

        this.logger.debug(
          `Using city ${effectiveCityId} for fare calculation (product: ${match.product.name})`,
        );

        // Calculate metrics for this city product
        const metrics = this.calculateMetricsForCityProduct(
          pickupToDestinationResult,
          match.driverData,
        );

        this.logger.debug(
          `Metrics for product ${match.product.name}: tripDistance=${metrics.tripDistance}km, tripDuration=${metrics.tripDuration}min, pickupDistance=${metrics.pickupDistance}km`,
        );

        // Prepare city product with metrics
        const cityProductWithMetrics: CityProductWithMetrics = {
          ...match.cityProduct,
          metrics,
        };

        // Get zones for stops if provided
        let stopsZones: any[] = [];
        if (stops && stops.length > 0) {
          stopsZones = await Promise.all(
            stops.map((stop) => this.getZoneFromLocation(stop)),
          );
        }
        console.log({ stops });

        // Prepare fare context
        const fareContext: FareContext = {
          pickup: pickupZone,
          destination: destinationZone,
          stops: stopsZones,
          cityProducts: [cityProductWithMetrics],
          currency: 'INR',
          timestamp: new Date(),
          requestId: `ride-search-${Date.now()}-${match.product.id}`,
        };

        this.logger.debug(
          `Fare context for product ${match.product.name}: pickup=${pickupZone?.name || 'None'}, destination=${destinationZone?.name || 'None'}, cityProductId=${match.cityProduct.id}`,
        );

        // Call fare engine with optimization options (skip commission for speed)
        const fareOptions: FareCalculationOptions = {
          skipCommission: true, // Skip commission for ride search (customer only needs total fare)
          skipTaxBreakdown: false, // Keep tax breakdown for transparency
          skipChargeBreakdown: false, // Keep charge breakdown
        };

        const fareResult = await this.fareEngineService.estimateFare(
          fareContext,
          fareOptions,
        );

        this.logger.debug(
          `Fare result for product ${match.product.name}: status=${fareResult?.status}, hasData=${!!fareResult?.data}`,
        );

        // Extract fare from result
        if (fareResult && fareResult.status && fareResult.data) {
          const fareCalculationResults =
            fareResult.data.fareCalculationResults || [];
          if (fareCalculationResults.length > 0) {
            const fareCalc = fareCalculationResults[0];
            match.fare = {
              amount: fareCalc.passengerFare, // Round to nearest integer
              currency: fareCalc.currency,
              cityProductId: fareCalc.cityProductId,
              cityProductFareId: fareCalc.cityProductFareId,
            };
            this.logger.log(
              `✓ Calculated fare for product ${match.product.name}: ${match.fare.amount} ${match.fare.currency}`,
            );
          } else {
            this.logger.warn(
              `No fare calculation results for product ${match.product.name}`,
            );
          }
        } else {
          this.logger.warn(
            `Fare calculation failed for product ${match.product.name}: status=${fareResult?.status}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error calculating fare for product ${match.product.id}:`,
          error,
        );
        // Continue with other products even if one fails
      }
    }
  }

  /**
   * Calculate metrics for a city product based on route data
   */
  private calculateMetricsForCityProduct(
    pickupToDestinationResult: RouteComputationResult | null,
    driverData?: DriverRouteData,
  ) {
    const tripDistance =
      (pickupToDestinationResult?.distanceMeters || 0) / 1000; // Convert to km
    const tripDurationSeconds = pickupToDestinationResult?.duration
      ? this.parseDuration(pickupToDestinationResult.duration.toString())
      : 0;

    const pickupDistance = driverData
      ? driverData.distanceToPickupMeters / 1000
      : 0; // Convert to km
    const pickupDurationSeconds = driverData
      ? driverData.etaToPickupSeconds
      : 0;

    return {
      pickupDistance,
      pickupDuration: pickupDurationSeconds / 60, // Convert seconds to minutes for fare engine
      pickupWaitDuration: 0, // Not applicable for ride search
      tripDuration: tripDurationSeconds / 60, // Convert seconds to minutes for fare engine
      tripWaitDuration: 0, // Not applicable for ride search
      tripDistance,
    };
  }

  /**
   * Builds the final ride search results
   */
  private buildRideSearchResults(
    productDriverMatches: ProductDriverMatch[],
    pickupToDestinationResult: RouteComputationResult | null,
  ): RideSearchResult[] {
    return productDriverMatches.map((match) =>
      this.mapProductToRideSearchResult(
        match.product,
        pickupToDestinationResult,
        match.driverData,
        match.fare,
        match.cityProduct,
      ),
    );
  }

  private parseDuration(duration: string): number {
    if (!duration) return 0;

    // Remove trailing 's' if it exists and convert to number
    return Number(duration.endsWith('s') ? duration.slice(0, -1) : duration);
  }

  /**
   * Get driver city products with full relations
   * Optimized to fetch all necessary data in one query
   *
   * Logic:
   * 1. If pickupCityId is provided (same city ride):
   *    - Filter by cityId = pickupCityId
   *
   * 2. If intercityFareCityId is provided (intercity with city):
   *    - Filter by productService.identifier = 'intercity'
   *    - Filter by cityId = intercityFareCityId (for fare calculation)
   *
   * 3. If neither (intercity without city):
   *    - Filter by productService.identifier = 'intercity'
   *    - Don't filter by city (use driver's city)
   */
  private async getDriverCityProducts(
    driverIds: string[],
    pickupCityId: string | null,
    intercityFareCityId: string | null,
  ): Promise<
    Array<{
      userProfileId: string;
      cityProduct: CityProduct & {
        product: ProductWithRelations;
        vehicleType: VehicleType;
      };
    }>
  > {
    if (driverIds.length === 0) {
      return [];
    }

    try {
      // Build where clause
      const whereClause: any = {
        userProfileId: {
          in: driverIds,
        },
        cityProduct: {
          isEnabled: true,
          product: {
            isEnabled: true,
          },
        },
      };

      // If pickupCityId is provided, filter by city (same city ride)
      if (pickupCityId) {
        whereClause.cityProduct.cityId = pickupCityId;
        //identifier not in intercity
        whereClause.cityProduct.product.productService = {
          identifier: {
            not: ServiceType.INTERCITY,
          },
        };
        this.logger.log(
          `Filtering driver city products by city: ${pickupCityId}`,
        );
      } else {
        // Intercity ride: filter by productService = 'intercity'
        whereClause.cityProduct.product.productService = {
          identifier: ServiceType.INTERCITY,
        };

        // If we have a city for fare calculation, filter by that city
        if (intercityFareCityId) {
          whereClause.cityProduct.cityId = intercityFareCityId;
          this.logger.log(
            `Filtering intercity products by city: ${intercityFareCityId}`,
          );
        } else {
          // No city - use any driver's city products
          this.logger.log(
            'Filtering intercity products (any city - will use driver city)',
          );
        }
      }

      const result = await this.driverCityProductRepository.findMany({
        where: whereClause,
        include: {
          cityProduct: {
            include: {
              product: {
                include: {
                  productService: true,
                  vehicleType: true,
                },
              },
              vehicleType: true,
            },
          },
        },
      });

      this.logger.log(
        `Found ${result.length} driver city product associations`,
      );

      return result.map((dcp) => ({
        userProfileId: dcp.userProfileId,
        cityProduct: dcp.cityProduct as CityProduct & {
          product: ProductWithRelations;
          vehicleType: VehicleType;
        },
      }));
    } catch (error) {
      this.logger.error('Failed to get driver city products:', error);
      return [];
    }
  }

  /**
   * Get zone from location coordinates
   */
  private async getZoneFromLocation(location: any): Promise<any> {
    try {
      const h3Index = this.h3UtilityService.coordinatesToH3Index(
        location.lat,
        location.lng,
        8,
      );
      const h3ToZone = await this.h3IndexToZoneRepository.findByH3Index(
        BigInt(`0x${h3Index}`),
      );
      return h3ToZone?.zone || null;
    } catch (error) {
      this.logger.warn(
        `Failed to get zone for location ${location.lat}, ${location.lng}:`,
        error,
      );
      return null;
    }
  }
}

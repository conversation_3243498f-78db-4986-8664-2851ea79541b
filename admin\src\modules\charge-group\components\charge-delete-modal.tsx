'use client';

import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';

interface ChargeDeleteModalProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
   isLoading: boolean;
   chargeName: string;
}

export function ChargeDeleteModal({
   isOpen,
   onClose,
   onConfirm,
   isLoading,
   chargeName,
}: ChargeDeleteModalProps) {
   return (
      <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
         <DialogContent className='max-w-md'>
            <DialogHeader>
               <DialogTitle>Remove Charge</DialogTitle>
               <DialogDescription>
                  Are you sure you want to remove this charge from this charge group?
               </DialogDescription>
            </DialogHeader>

            <div className='py-4'>
               <p className='text-sm text-gray-700'>
                  You are about to remove: <span className='font-semibold'>{chargeName}</span>
               </p>
            </div>

            <div className='flex gap-3'>
               <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={isLoading}
                  className='flex-1'
               >
                  Cancel
               </Button>
               <Button
                  type='button'
                  variant='destructive'
                  onClick={onConfirm}
                  disabled={isLoading}
                  className='flex-1'
               >
                  {isLoading ? (
                     <>
                        Removing...
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     'Remove'
                  )}
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
}

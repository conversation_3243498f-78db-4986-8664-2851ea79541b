import {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination';

export const CustomPagination = ({
  currentPage,
  totalPages,
  hasNext,
  hasPrev,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  onPageChange: (page: number) => void;
}) => {
  return (
    <>
      {totalPages > 1 && (
        <Pagination className="py-4">
          <PaginationContent>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (hasPrev) onPageChange(currentPage - 1);
              }}
              className={!hasPrev ? 'pointer-events-none opacity-50' : ''}
            />

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
              if (
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 1 && page <= currentPage + 1)
              ) {
                return (
                  <PaginationLink
                    key={page}
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onPageChange(page);
                    }}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                );
              } else if (
                (page === currentPage - 2 || page === currentPage + 2) &&
                ((page === currentPage - 2 && currentPage > 3) ||
                  (page === currentPage + 2 && currentPage < totalPages - 2))
              ) {
                return <PaginationEllipsis key={page} />;
              }
              return null;
            })}

            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (hasNext) onPageChange(currentPage + 1);
              }}
              className={!hasNext ? 'pointer-events-none opacity-50' : ''}
            />
          </PaginationContent>
        </Pagination>
      )}
    </>
  );
};

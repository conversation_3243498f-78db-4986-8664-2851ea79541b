import { Controller, Get, Version } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import {
  HealthStatusDto,
  SimpleHealthDto,
  ReadinessDto,
  LivenessDto,
} from './dto/health-response.dto';
import { ApiSuccessResponse } from '../../docs/swagger/swagger-decorators';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @Version('1')
  @ApiOperation({
    summary: 'Get detailed health status',
    description:
      'Returns comprehensive health information including memory usage, database status, and external services status',
  })
  @ApiResponse({
    status: 200,
    description: 'Health status retrieved successfully',
    type: HealthStatusDto,
  })
  @ApiResponse({
    status: 503,
    description: 'Service unavailable - health check failed',
  })
  async getHealth(): Promise<HealthStatusDto> {
    return await this.healthService.getHealth();
  }

  @Get('simple')
  @Version('1')
  @ApiOperation({
    summary: 'Get simple health status',
    description: 'Returns basic health status with minimal information',
  })
  @ApiSuccessResponse(SimpleHealthDto, 'Simple health status retrieved')
  async getSimpleHealth(): Promise<SimpleHealthDto> {
    return await this.healthService.getSimpleHealth();
  }

  @Get('ready')
  @Version('1')
  @ApiOperation({
    summary: 'Check application readiness',
    description:
      'Returns whether the application is ready to serve requests. Used for Kubernetes readiness probes.',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is ready',
    type: ReadinessDto,
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not ready',
    type: ReadinessDto,
  })
  async getReadiness(): Promise<ReadinessDto> {
    return await this.healthService.checkReadiness();
  }

  @Get('live')
  @Version('1')
  @ApiOperation({
    summary: 'Check application liveness',
    description:
      'Returns whether the application is alive and running. Used for Kubernetes liveness probes.',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
    type: LivenessDto,
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not alive',
    type: LivenessDto,
  })
  async getLiveness(): Promise<LivenessDto> {
    return await this.healthService.checkLiveness();
  }
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface TaxGroupDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  taxGroupName: string;
}

export const TaxGroupDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  taxGroupName,
}: TaxGroupDeleteModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className='flex items-center justify-center w-12 h-12 rounded-full bg-red-100'>
              <AlertTriangle className='w-6 h-6 text-red-600' />
            </div>
            <div>
              <DialogTitle className='text-red-900'>Delete Tax Group</DialogTitle>
              <DialogDescription className='text-red-700'>
                This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='pt-2'>
          <p className='text-sm text-gray-600'>
            Are you sure you want to delete the tax group "{taxGroupName}"? This will permanently remove
            the tax group and its subcategories from the system and cannot be undone.
          </p>
        </div>

        <div className='flex gap-3 pt-4'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            className='flex-1'
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant='destructive'
            onClick={onConfirm}
            className='flex-1'
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                Deleting...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              'Delete Tax Group'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

'use client';

import React, { useState, useEffect } from 'react';
import { Car } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useUpdateDriverVehicle } from '../api/mutations';
import { DriverVehicle } from '../types/driver';
import { toast } from 'sonner';

interface EditVehicleModalProps {
   isOpen: boolean;
   onClose: () => void;
   vehicle: DriverVehicle;
}

export function EditVehicleModal({ isOpen, onClose, vehicle }: EditVehicleModalProps) {
   const [isPrimary, setIsPrimary] = useState(vehicle.isPrimary);
   
   const updateVehicleMutation = useUpdateDriverVehicle();

   // Reset form when vehicle changes
   useEffect(() => {
      if (vehicle) {
         setIsPrimary(vehicle.isPrimary);
      }
   }, [vehicle]);

   const handleSubmit = async () => {
      try {
         // If no changes, just close the modal
         if (isPrimary === vehicle.isPrimary) {
            onClose();
            return;
         }

         await updateVehicleMutation.mutateAsync({
            vehicleId: vehicle.id,
            isPrimary: isPrimary,
         });

         toast.success('Vehicle updated successfully');
         onClose();
      } catch (error: any) {
         console.error('Failed to update vehicle:', error);
         toast.error(error?.response?.data?.message || 'Failed to update vehicle');
      }
   };

   const hasChanges = isPrimary !== vehicle.isPrimary;

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle className="flex items-center gap-2">
                  <Car className="w-5 h-5" />
                  Edit Vehicle - {vehicle.vehicleNumber}
               </DialogTitle>
            </DialogHeader>

            <div className="space-y-6 py-4">
               {/* Primary Vehicle Toggle */}
               <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-900">
                     Primary Vehicle
                  </Label>
                  <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-gray-50">
                     <div className="space-y-1">
                        <div className="text-sm font-medium text-gray-900">
                           Set as Primary Vehicle
                        </div>
                        <div className="text-xs text-gray-500">
                           Primary vehicles are used by default for ride assignments
                        </div>
                     </div>
                     <Switch
                        checked={isPrimary}
                        onCheckedChange={setIsPrimary}
                        disabled={updateVehicleMutation.isPending}
                     />
                  </div>
               </div>
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t">
               <Button
                  variant="outline"
                  onClick={onClose}
                  disabled={updateVehicleMutation.isPending}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={!hasChanges || updateVehicleMutation.isPending}
                  className="bg-blue-600 hover:bg-blue-700"
               >
                  {updateVehicleMutation.isPending ? 'Updating...' : 'Update Vehicle'}
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
}
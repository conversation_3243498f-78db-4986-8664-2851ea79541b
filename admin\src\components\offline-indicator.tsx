'use client';

import { WifiOff } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/use-network-status';

export function OfflineIndicator() {
   const isOnline = useNetworkStatus();

   if (isOnline) {
      return null;
   }

   return (
      <div className='flex items-center gap-2 px-3 py-2 bg-red-50 border-l-4 border-red-500 text-red-700'>
         <WifiOff className='w-4 h-4' />
         <span className='text-sm font-medium'>No internet connection</span>
      </div>
   );
}

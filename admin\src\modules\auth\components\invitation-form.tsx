'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';
import { toast } from '@/lib/toast';
import { useAuthStore } from '@/store/auth-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { invitationFormSchema, type InvitationFormData } from '../schema/invitation-schema';
import { useVerifyInvitationToken, useSetupPassword } from '../api/mutations';

export function InvitationForm({ className, ...props }: React.ComponentProps<'div'>) {
   const [showPassword, setShowPassword] = useState(false);
   const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);
   const [profileData, setProfileData] = useState<any>(null);
   const router = useRouter();
   const searchParams = useSearchParams();
   const authStore = useAuthStore(state => state);

   const token = searchParams.get('token');

   const {
      register,
      handleSubmit,
      formState: { errors },
   } = useForm<InvitationFormData>({
      resolver: zodResolver(invitationFormSchema),
      defaultValues: {
         password: '',
         passwordConfirmation: '',
      },
   });

   const verifyTokenMutation = useVerifyInvitationToken();
   const setupPasswordMutation = useSetupPassword();
   const loadingRef = useRef(false);

   useEffect(() => {
      if (!token) {
         toast.error('Invalid invitation link');
         router.push('/');
         return;
      }

      if (verifyTokenMutation.isPending || loadingRef.current === true) return;

      loadingRef.current = true;

      verifyTokenMutation.mutate(
         { token },
         {
            onSuccess: response => {
               setProfileData(response.data);
            },
            onError: () => {
               router.push('/');

               setTimeout(() => {
                  loadingRef.current = false;
               }, 1000);
            },
         }
      );
   }, [token, router, verifyTokenMutation]);

   const onSubmit = async (data: InvitationFormData) => {
      if (!token) {
         toast.error('Invalid invitation token');
         return;
      }

      const payload = {
         token,
         password: data.password,
         passwordConfirmation: data.passwordConfirmation,
      };

      setupPasswordMutation.mutate(payload, {
         onSuccess: response => {
            toast.success('Password setup completed successfully');

            const tokens = response.data;
            authStore.setToken(tokens.accessToken, tokens.refreshToken);
            authStore.setUserId(tokens.userId || '');

            router.push('/dashboard/drivers');
         },
         onError: (error: any) => {
            toast.error(error?.response?.data?.message || 'Failed to setup password');
         },
      });
   };

   if (verifyTokenMutation.isPending) {
      return (
         <div className={cn('flex flex-col gap-6', className)} {...props}>
            <Card>
               <CardContent className='flex items-center justify-center p-8'>
                  <div className='flex items-center gap-3'>
                     <Spinner />
                     <span>Verifying invitation...</span>
                  </div>
               </CardContent>
            </Card>
         </div>
      );
   }

   if (!profileData) {
      return null;
   }

   return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
         <Card>
            <CardHeader className='text-center'>
               <CardTitle className='text-xl'>Complete Your Registration</CardTitle>
               <p className='text-sm text-muted-foreground'>
                  Welcome! Please set up your password to complete the registration process.
               </p>
            </CardHeader>
            <CardContent>
               <form onSubmit={handleSubmit(onSubmit)}>
                  <div className='grid gap-6'>
                     <div className='grid gap-6'>
                        <div className='grid gap-3'>
                           <Label htmlFor='firstName'>First Name</Label>
                           <Input
                              id='firstName'
                              type='text'
                              value={profileData.firstName}
                              disabled
                              className='bg-muted'
                           />
                        </div>
                        <div className='grid gap-3'>
                           <Label htmlFor='lastName'>Last Name</Label>
                           <Input
                              id='lastName'
                              type='text'
                              value={profileData.lastName}
                              disabled
                              className='bg-muted'
                           />
                        </div>
                        <div className='grid gap-3'>
                           <Label htmlFor='email'>Email</Label>
                           <Input
                              id='email'
                              type='email'
                              value={profileData.user.email}
                              disabled
                              className='bg-muted'
                           />
                        </div>
                        <div className='grid gap-3'>
                           <Label htmlFor='password'>Password</Label>
                           <div className='relative'>
                              <Input
                                 id='password'
                                 type={showPassword ? 'text' : 'password'}
                                 placeholder='Enter your password'
                                 autoComplete='new-password'
                                 {...register('password')}
                              />
                              <Button
                                 type='button'
                                 variant='ghost'
                                 size='sm'
                                 className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                                 onClick={() => setShowPassword(!showPassword)}
                              >
                                 {showPassword ? (
                                    <EyeOffIcon className='h-4 w-4 text-muted-foreground' />
                                 ) : (
                                    <EyeIcon className='h-4 w-4 text-muted-foreground' />
                                 )}
                              </Button>
                           </div>
                           <ErrorMessage error={errors.password} />
                        </div>
                        <div className='grid gap-3'>
                           <Label htmlFor='passwordConfirmation'>Confirm Password</Label>
                           <div className='relative'>
                              <Input
                                 id='passwordConfirmation'
                                 type={showPasswordConfirmation ? 'text' : 'password'}
                                 placeholder='Confirm your password'
                                 autoComplete='new-password'
                                 {...register('passwordConfirmation')}
                              />
                              <Button
                                 type='button'
                                 variant='ghost'
                                 size='sm'
                                 className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                                 onClick={() =>
                                    setShowPasswordConfirmation(!showPasswordConfirmation)
                                 }
                              >
                                 {showPasswordConfirmation ? (
                                    <EyeOffIcon className='h-4 w-4 text-muted-foreground' />
                                 ) : (
                                    <EyeIcon className='h-4 w-4 text-muted-foreground' />
                                 )}
                              </Button>
                           </div>
                           <ErrorMessage error={errors.passwordConfirmation} />
                        </div>
                        <Button
                           type='submit'
                           className='w-full'
                           disabled={setupPasswordMutation.isPending}
                        >
                           {setupPasswordMutation.isPending ? (
                              <>
                                 Setting up password...
                                 <Spinner className='ml-2' />
                              </>
                           ) : (
                              'Complete Registration'
                           )}
                        </Button>
                     </div>
                  </div>
               </form>
            </CardContent>
         </Card>
         <div className='text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4'>
            By clicking continue, you agree to our <a href='#'>Terms of Service</a> and{' '}
            <a href='#'>Privacy Policy</a>.
         </div>
      </div>
   );
}

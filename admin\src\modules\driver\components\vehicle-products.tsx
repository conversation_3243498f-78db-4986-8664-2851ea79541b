'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListDriverProducts } from '../api/driver-product-queries';
// import { useUpdatePrimaryStatus } from '../api/driver-product-mutations';
import { DriverProduct } from '../types/driver-product';
import { DriverProductFilters } from './driver-product-filters';
import { DriverProductTable } from './driver-product-table';
import { RemoveDriverProductModal } from './remove-driver-product-modal';
import { UpdatePrimaryStatusModal } from './update-primary-status-modal';

interface VehicleProductsProps {
  vehicleId: string;
  driverCityId: string;
  driverCityName: string;
}

export function VehicleProducts({ vehicleId, driverCityId, driverCityName }: VehicleProductsProps) {
  const [page, setPage] = useState(1);
  const [limit] = useState(100);
  const [search, setSearch] = useState('');

  // Modal states
  const [removeModalOpen, setRemoveModalOpen] = useState(false);
  const [primaryStatusModalOpen, setPrimaryStatusModalOpen] = useState(false);
  const [selectedDriverProduct, setSelectedDriverProduct] = useState<DriverProduct | null>(null);
  const [selectedForPrimary, setSelectedForPrimary] = useState<DriverProduct | null>(null);

  // Mutations
  // const updatePrimaryMutation = useUpdatePrimaryStatus(vehicleId);

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const driverProductsQuery = useListDriverProducts(vehicleId, {
    page,
    limit,
    productName: search || undefined,
  });

  // Handle modal actions
  const handleRemoveClick = (driverProduct: DriverProduct) => {
    setSelectedDriverProduct(driverProduct);
    setRemoveModalOpen(true);
  };

  const handleMakePrimaryClick = (driverProduct: DriverProduct) => {
    setSelectedForPrimary(driverProduct);
    setPrimaryStatusModalOpen(true);
  };

  const handlePrimaryStatusModalClose = () => {
    setPrimaryStatusModalOpen(false);
    setSelectedForPrimary(null);
  };

  // const handleTogglePrimaryClick = (driverProduct: DriverProduct) => {
  //   // TODO: Implement primary status toggle when API is available
  //   console.log('Toggle primary for:', driverProduct);
  // };

  // Check if any filters are active
  const hasFilters = !!search;

  return (
    <div className='space-y-4'>
      {/* Table Card */}
      <Card className='overflow-hidden py-4 px-4 rounded-sm'>
        <DriverProductFilters
          search={search}
          onSearchChange={handleSearchChange}
          isLoading={driverProductsQuery.isFetching && !driverProductsQuery.isLoading}
          showAddButton={true}
          vehicleId={vehicleId}
          driverCityId={driverCityId}
          driverCityName={driverCityName}
        />

        <DriverProductTable
          data={driverProductsQuery.data}
          isLoading={driverProductsQuery.isLoading}
          currentPage={page}
          onPageChange={(newPage: number) => setPage(newPage)}
          hasFilters={hasFilters}
          onRemoveClick={handleRemoveClick}
          onMakePrimary={handleMakePrimaryClick}
        />
      </Card>

      {/* Modals */}
      <RemoveDriverProductModal
        open={removeModalOpen}
        onOpenChange={setRemoveModalOpen}
        driverProduct={selectedDriverProduct}
        vehicleId={vehicleId}
      />

      {selectedForPrimary && (
        <UpdatePrimaryStatusModal
          isOpen={primaryStatusModalOpen}
          onClose={handlePrimaryStatusModalClose}
          driverProduct={selectedForPrimary}
          vehicleId={vehicleId}
          currentPrimaryProduct={driverProductsQuery.data?.data?.find(p => p.isPrimary)}
        />
      )}
    </div>
  );
}
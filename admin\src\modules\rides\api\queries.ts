import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { ListRidesParams, ListRidesResponse, RideDetailsResponse } from '../types/ride';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListRides = (params: ListRidesParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: hasPermission(RBAC_PERMISSIONS.RIDES.LIST),
      queryKey: ['rides', params],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListRidesResponse> => {
         return apiClient.get('/rides/history/admin', {
            params,
         });
      },
   });
};

export const useGetRideDetails = (rideId: string | null) => {
   return useQuery({
      queryKey: ['ride-details', rideId],
      queryFn: (): Promise<RideDetailsResponse> => {
         return apiClient.get(`/rides/${rideId}/details/admin`);
      },
      enabled: !!rideId,
      refetchOnWindowFocus: false,
   });
};

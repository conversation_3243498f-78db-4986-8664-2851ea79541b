/**
 * Swagger examples and schemas for Zone and Zone Type documentation
 */

export const ZoneExamples = {
  createZoneRequest: {
    summary: 'Create Downtown Business District Zone',
    description: 'Example of creating a commercial zone in downtown area',
    value: {
      name: 'Downtown Business District',
      description:
        'A bustling commercial area in the heart of the city with high-rise buildings, corporate offices, and retail establishments',
      polygon: {
        type: 'Polygon',
        coordinates: [
          [
            [-73.9857, 40.7484], // Southwest corner
            [-73.9857, 40.7594], // Northwest corner
            [-73.9727, 40.7594], // Northeast corner
            [-73.9727, 40.7484], // Southeast corner
            [-73.9857, 40.7484], // Close polygon
          ],
        ],
      },
      meta: {
        priority: 'high',
        tags: ['commercial', 'busy', 'corporate'],
        capacity: 1000,
        features: ['parking', 'public_transport', 'restaurants', 'hotels'],
        operationalHours: {
          weekdays: { start: '06:00', end: '23:00' },
          weekends: { start: '08:00', end: '22:00' },
        },
        pricing: {
          baseMultiplier: 1.2,
          surgeThreshold: 0.8,
          maxSurge: 2.5,
        },
      },
      zoneTypeId: '123e4567-e89b-12d3-a456-426614174000',
      cityId: '987fcdeb-51a2-4d6c-8b3a-123456789abc',
    },
  },

  createAirportZoneRequest: {
    summary: 'Create Airport Zone',
    description: 'Example of creating a specialized airport zone',
    value: {
      name: 'JFK International Airport',
      description:
        'John F. Kennedy International Airport - Terminal pickup and dropoff zones with queue management',
      polygon: {
        type: 'Polygon',
        coordinates: [
          [
            [-73.7781, 40.6413],
            [-73.7781, 40.6513],
            [-73.7631, 40.6513],
            [-73.7631, 40.6413],
            [-73.7781, 40.6413],
          ],
        ],
      },
      meta: {
        priority: 'critical',
        tags: ['airport', 'queue', 'regulated'],
        terminalInfo: {
          terminals: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6'],
          pickupZones: ['A', 'B', 'C', 'D'],
          waitingTime: 45,
        },
        regulations: {
          maxWaitTime: 60,
          queueRequired: true,
          permitRequired: true,
          feeStructure: {
            entryFee: 2.5,
            waitingFee: 0.1,
          },
        },
      },
      zoneTypeId: '456e7890-e12b-34d5-a678-901234567def',
      cityId: '987fcdeb-51a2-4d6c-8b3a-123456789abc',
    },
  },

  updateZoneRequest: {
    summary: 'Update Zone Description and Metadata',
    description:
      'Example of updating zone description and operational metadata',
    value: {
      description:
        'Updated commercial district with new shopping complex and improved public transportation',
      meta: {
        priority: 'high',
        tags: ['commercial', 'busy', 'shopping', 'updated'],
        capacity: 1200,
        features: [
          'parking',
          'public_transport',
          'restaurants',
          'hotels',
          'shopping_mall',
        ],
        recentUpdates: {
          date: '2024-08-27',
          changes: [
            'Added shopping mall',
            'Increased capacity',
            'New metro station',
          ],
        },
        pricing: {
          baseMultiplier: 1.3,
          surgeThreshold: 0.75,
          maxSurge: 3.0,
        },
      },
    },
  },

  zoneResponse: {
    summary: 'Complete Zone Response',
    description:
      'Example of a complete zone response with all fields populated',
    value: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Downtown Business District',
      description: 'A bustling commercial area in the heart of the city',
      polygon: {
        type: 'Polygon',
        coordinates: [
          [
            [-73.9857, 40.7484],
            [-73.9857, 40.7594],
            [-73.9727, 40.7594],
            [-73.9727, 40.7484],
            [-73.9857, 40.7484],
          ],
        ],
      },
      meta: {
        priority: 'high',
        tags: ['commercial', 'busy'],
        capacity: 1000,
        features: ['parking', 'public_transport'],
      },
      zoneTypeId: '456e7890-e12b-34d5-a678-901234567def',
      cityId: '987fcdeb-51a2-4d6c-8b3a-123456789abc',
      isCity: false,
      h3Indexes: [
        '8a2a1072b59ffff',
        '8a2a1072b5bffff',
        '8a2a1072b43ffff',
        '8a2a1072b47ffff',
      ],
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-08-27T14:45:00.000Z',
      deletedAt: null,
      zoneType: {
        id: '456e7890-e12b-34d5-a678-901234567def',
        name: 'Commercial City Zone',
        algorithm: 'CITY',
      },
    },
  },
};

export const ZoneTypeExamples = {
  createZoneTypeRequest: {
    summary: 'Create Commercial Zone Type',
    description: 'Example of creating a zone type for commercial areas',
    value: {
      name: 'Commercial City Zone',
      description:
        'Algorithm configuration for busy commercial districts with high driver demand and dynamic pricing',
      algorithm: 'CITY',
      config: {
        driverDistribution: {
          maxDriversPerKm2: 50,
          minDriversPerKm2: 10,
          optimalDriversPerKm2: 30,
        },
        pricingAlgorithm: {
          baseMultiplier: 1.2,
          surgeThreshold: 0.8,
          maxSurgeMultiplier: 3.0,
          surgeCooldown: 300, // seconds
        },
        matching: {
          maxPickupDistance: 2000, // meters
          maxWaitTime: 600, // seconds
          priorityScore: {
            eta: 0.4,
            idleTime: 0.3,
            rating: 0.2,
            proximity: 0.1,
          },
        },
        operational: {
          allowPickupAnywhere: true,
          requireDestination: false,
          supportScheduledRides: true,
          peakHours: {
            morning: { start: '07:00', end: '10:00' },
            evening: { start: '17:00', end: '20:00' },
          },
        },
      },
      isActive: true,
    },
  },

  createAirportZoneTypeRequest: {
    summary: 'Create Airport Zone Type',
    description: 'Example of creating a specialized airport zone type',
    value: {
      name: 'International Airport Zone',
      description:
        'Specialized algorithm for airport operations with queue management and regulatory compliance',
      algorithm: 'AIRPORT',
      config: {
        queueManagement: {
          enabled: true,
          maxQueueLength: 100,
          averageWaitTime: 45,
          priorityLevels: ['premium', 'standard', 'economy'],
        },
        regulations: {
          permitRequired: true,
          entryFee: 2.5,
          waitingFee: 0.1,
          maxWaitTime: 60,
        },
        zones: {
          terminals: ['T1', 'T2', 'T3', 'T4'],
          pickupAreas: ['domestic', 'international', 'charter'],
          dropoffAreas: ['departures', 'arrivals'],
        },
        pricing: {
          flatRate: true,
          minimumFare: 15.0,
          airportSurcharge: 5.0,
        },
        matching: {
          queueBased: true,
          maxPickupWalk: 200, // meters
          terminalAssignment: true,
        },
      },
      isActive: true,
    },
  },

  zoneTypeResponse: {
    summary: 'Complete Zone Type Response',
    description: 'Example of a complete zone type response with configuration',
    value: {
      id: '456e7890-e12b-34d5-a678-901234567def',
      name: 'Commercial City Zone',
      description: 'Algorithm configuration for busy commercial districts',
      algorithm: 'CITY',
      config: {
        driverDistribution: {
          maxDriversPerKm2: 50,
          minDriversPerKm2: 10,
        },
        pricingAlgorithm: {
          baseMultiplier: 1.2,
          surgeThreshold: 0.8,
        },
      },
      isActive: true,
      createdAt: '2024-01-10T08:00:00.000Z',
      updatedAt: '2024-08-27T12:00:00.000Z',
      deletedAt: null,
      zonesCount: 25,
    },
  },
};

export const GeographicExamples = {
  manhattanPolygon: {
    summary: 'Manhattan District Polygon',
    description: 'GeoJSON polygon covering a section of Manhattan',
    value: {
      type: 'Polygon',
      coordinates: [
        [
          [-73.9857, 40.7484], // Times Square area
          [-73.9857, 40.7594],
          [-73.9727, 40.7594], // Near Central Park
          [-73.9727, 40.7484],
          [-73.9857, 40.7484],
        ],
      ],
    },
  },

  h3IndexesExample: {
    summary: 'H3 Index Array',
    description: 'Example H3 indexes generated for a zone at resolution 9',
    value: [
      '8a2a1072b59ffff',
      '8a2a1072b5bffff',
      '8a2a1072b43ffff',
      '8a2a1072b47ffff',
      '8a2a1072b4bffff',
      '8a2a1072b53ffff',
    ],
  },
};

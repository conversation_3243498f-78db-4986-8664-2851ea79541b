// Vehicle Category interface for API responses
export interface VehicleCategory {
   id: string;
   name: string;
   description?: string | null;
   image?: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// API response structure for listing vehicle categories
export interface ListVehicleCategoryResponse {
   success: boolean;
   message: string;
   data: VehicleCategory[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
   };
   timestamp: number;
}

// API response structure for single vehicle category
export interface VehicleCategoryResponse {
   success: boolean;
   message: string;
   data: VehicleCategory;
   timestamp: number;
}

// Request for creating vehicle category
export interface CreateVehicleCategoryRequest {
   name: string;
   description?: string;
   image?: string;
}

// Request for updating vehicle category
export interface UpdateVehicleCategoryRequest {
   name?: string;
   description?: string;
   image?: string;
}

// Parameters for listing vehicle categories with pagination
export interface ListVehicleCategoryParams {
   page?: number;
   limit?: number;
   search?: string;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
}
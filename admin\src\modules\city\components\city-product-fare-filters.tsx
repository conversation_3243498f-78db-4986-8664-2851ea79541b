'use client';

import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface CityProductFareFiltersProps {
  onAddFare?: () => void;
}

export function CityProductFareFilters({
  onAddFare,
}: CityProductFareFiltersProps) {
  return (
    <div className='flex justify-end items-center gap-4 mb-4'>
      <div className='flex gap-2'>
        {onAddFare && (
          <Button
            className='cursor-pointer'
            variant='outline'
            onClick={onAddFare}
          >
            <Plus className='h-4 w-4' />
            Add Fare
          </Button>
        )}
      </div>
    </div>
  );
}

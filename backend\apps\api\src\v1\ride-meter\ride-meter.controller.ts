import {
  Controller,
  Post,
  Body,
  HttpStatus,
  BadRequestException,
  Get,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { RideMeterLogService } from '@shared/shared/modules/ride/services/ride-meter-log.service';
import { RideMeterType } from '@shared/shared/repositories/models/rideMeter.model';
import { StartWaitingDto, WaitingMeterType } from './dto/start-waiting.dto';
import { EndWaitingDto } from './dto/end-waiting.dto';
import { RideMeterService } from '@shared/shared/modules/ride-meter/ride-meter.service';

@ApiTags('Ride Meter')
@ApiBearerAuth()
@Controller('ride-meter')
export class RideMeterController {
  constructor(
    private readonly rideMeterLogService: RideMeterLogService,
    private readonly rideMeterService: RideMeterService,
  ) {}

  /**
   * Start a waiting period for a ride
   */
  @Post('start-waiting')
  @ApiOperation({
    summary: 'Start a waiting period',
    description: 'Start tracking waiting time for pickup or trip',
  })
  @ApiBody({ type: StartWaitingDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Waiting period started successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Waiting period started successfully',
        },
        data: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: '123e4567-e89b-12d3-a456-426614174000',
            },
            rideId: {
              type: 'string',
              example: '123e4567-e89b-12d3-a456-426614174001',
            },
            rideMeterId: {
              type: 'string',
              example: '123e4567-e89b-12d3-a456-426614174002',
            },
            meterType: { type: 'string', example: 'pickup_wait_time' },
            startedAt: { type: 'string', example: '2024-01-15T10:30:00.000Z' },
          },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input or ride not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Active waiting period already exists',
  })
  async startWaiting(@Body() startWaitingDto: StartWaitingDto) {
    // Convert DTO enum to RideMeterType enum
    const meterType = this.convertToRideMeterType(startWaitingDto.meterType);

    // Start the waiting period
    const meterLog = await this.rideMeterLogService.startWaiting(
      startWaitingDto.rideId,
      meterType,
    );

    return {
      success: true,
      message: 'Waiting period started successfully',
      data: {
        id: meterLog.id,
        rideId: meterLog.rideId,
        rideMeterId: meterLog.rideMeterId,
        meterType: meterLog.name,
        startedAt: meterLog.fromTime,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * End a waiting period for a ride
   */
  @Post('end-waiting')
  @ApiOperation({
    summary: 'End a waiting period',
    description: 'Stop tracking waiting time and update the ride meter',
  })
  @ApiBody({ type: EndWaitingDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Waiting period ended successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Waiting period ended successfully',
        },
        data: {
          type: 'object',
          properties: {
            meterLog: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  example: '123e4567-e89b-12d3-a456-426614174000',
                },
                rideId: {
                  type: 'string',
                  example: '123e4567-e89b-12d3-a456-426614174001',
                },
                meterType: { type: 'string', example: 'pickup_wait_time' },
                startedAt: {
                  type: 'string',
                  example: '2024-01-15T10:30:00.000Z',
                },
                endedAt: {
                  type: 'string',
                  example: '2024-01-15T10:35:00.000Z',
                },
                duration: { type: 'number', example: 300 },
              },
            },
            rideMeter: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  example: '123e4567-e89b-12d3-a456-426614174002',
                },
                rideId: {
                  type: 'string',
                  example: '123e4567-e89b-12d3-a456-426614174001',
                },
                name: { type: 'string', example: 'pickup_wait_time' },
                value: { type: 'number', example: 600 },
                unit: { type: 'string', example: 'seconds' },
              },
            },
          },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input, meter log not found, or already ended',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Ride or meter log not found',
  })
  async endWaiting(@Body() endWaitingDto: EndWaitingDto) {
    const meterType = this.convertToRideMeterType(endWaitingDto.meterType);

    // End the waiting period
    const result = await this.rideMeterLogService.endWaiting(
      endWaitingDto.meterLogId,
      endWaitingDto.rideId,
      meterType,
    );

    // Calculate duration for response
    const duration = result.meterLog.toTime
      ? Math.floor(
          (result.meterLog.toTime.getTime() -
            result.meterLog.fromTime.getTime()) /
            1000,
        )
      : 0;

    return {
      success: true,
      message: 'Waiting period ended successfully',
      data: {
        meterLog: {
          id: result.meterLog.id,
          rideId: result.meterLog.rideId,
          meterType: result.meterLog.name,
          startedAt: result.meterLog.fromTime,
          endedAt: result.meterLog.toTime,
          duration,
        },
        rideMeter: {
          id: result.rideMeter.id,
          rideId: result.rideMeter.rideId,
          name: result.rideMeter.name,
          value: result.rideMeter.value,
          unit: result.rideMeter.unit,
        },
      },
      timestamp: Date.now(),
    };
  }

  /**
   * Convert DTO enum to RideMeterType enum
   */
  private convertToRideMeterType(
    waitingMeterType: WaitingMeterType,
  ): RideMeterType.PICKUP_WAIT_TIME | RideMeterType.TRIP_WAIT_TIME {
    switch (waitingMeterType) {
      case WaitingMeterType.PICKUP_WAIT_TIME:
        return RideMeterType.PICKUP_WAIT_TIME;
      case WaitingMeterType.TRIP_WAIT_TIME:
        return RideMeterType.TRIP_WAIT_TIME;
      default:
        throw new BadRequestException(
          `Unsupported meter type: ${waitingMeterType}`,
        );
    }
  }

  //fetch meters of a ride
  @Get('rides/:rideId/get-ride-meters')
  async getRideMeters(@Param('rideId') rideId: string) {
    const meters = await this.rideMeterService.getRideMetersByRideId(rideId);
    return {
      success: true,
      message: 'Ride meters fetched successfully',
      data: meters,
      timestamp: Date.now(),
    };
  }
}

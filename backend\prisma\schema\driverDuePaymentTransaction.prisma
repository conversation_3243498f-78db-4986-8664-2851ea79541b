model DriverDuePaymentTransaction {
  id            String    @id @default(uuid()) @map("id") @db.Uuid
  driverId      String    @map("driver_id") @db.Uuid
  cityId        String    @map("city_id") @db.Uuid
  transactionId String    @unique @map("transaction_id")
  amount        Decimal   @map("amount") @db.Decimal(10, 2)
  paymentMethod String    @map("payment_method")
  paymentStatus String    @map("payment_status")
  balanceBefore Decimal   @map("balance_before") @db.Decimal(10, 2)
  balanceAfter  Decimal   @map("balance_after") @db.Decimal(10, 2)
  metadata      Json?     @map("metadata") @db.JsonB
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  driver UserProfile @relation("DriverDuePaymentTransaction", fields: [driverId], references: [id], onDelete: Cascade)
  city   City        @relation("DriverDuePaymentTransaction", fields: [cityId], references: [id], onDelete: Cascade)

  @@index([driverId], name: "idx_driver_due_payment_transaction_driver_id")
  @@index([cityId], name: "idx_driver_due_payment_transaction_city_id")
  @@index([transactionId], name: "idx_driver_due_payment_transaction_id")
  @@index([paymentStatus], name: "idx_driver_due_payment_transaction_status")
  @@index([createdAt], name: "idx_driver_due_payment_transaction_created_at")
  @@map("driver_due_payment_transactions")
}

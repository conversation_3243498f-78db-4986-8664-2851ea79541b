import { ApiProperty } from '@nestjs/swagger';

export class CityAdminsMetaDto {
  @ApiProperty({
    description: 'Current page number in the pagination',
    example: 1,
    type: Number,
    required: true,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    type: Number,
    required: true,
  })
  limit!: number;

  @ApiProperty({
    description: 'Total number of admins matching the query',
    example: 50,
    type: Number,
    required: true,
  })
  total!: number;

  @ApiProperty({
    description: 'Total number of pages available',
    example: 5,
    type: Number,
    required: true,
  })
  totalPages!: number;
}

export class CityAdminsResponseDto {
  @ApiProperty({
    description: 'Indicates if the request was successful',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Human-readable message describing the result',
    example: 'City admins retrieved successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description:
      'Array of city admin objects (includes user profile, role, city details)',
    type: [Object],
    required: true,
  })
  data!: any[];

  @ApiProperty({
    description: 'Pagination metadata for the response',
    type: CityAdminsMetaDto,
    required: true,
  })
  @ApiProperty({ type: CityAdminsMetaDto })
  meta!: CityAdminsMetaDto;

  @ApiProperty({
    description: 'Timestamp of the response (Unix milliseconds)',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

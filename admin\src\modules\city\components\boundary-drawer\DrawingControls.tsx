'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Maximize, Minimize } from 'lucide-react';
import { DrawingMode } from '../../types/drawing';

interface DrawingControlsProps {
   mode: DrawingMode;
   onStartDrawing: () => void;
   onCancelDrawing: () => void;
   onEditPolygon: () => void;
   onSavePolygon: () => void;
   onDeletePolygon: () => void;
   onFullscreen: () => void;
   isSaving: boolean;
   hasPolygon: boolean;
   cityName?: string;
   isFullscreen?: boolean;
}

export function DrawingControls({
   mode,
   onStartDrawing,
   onCancelDrawing,
   onEditPolygon,
   onSavePolygon,
   onDeletePolygon,
   onFullscreen,
   isSaving,
   hasPolygon,
   cityName,
   isFullscreen = false,
}: DrawingControlsProps) {
   return (
      <div className='absolute top-4 right-4 bg-white rounded-lg shadow-lg px-4 py-1 flex items-center gap-4 border'>
         {cityName && (
            <div className='flex items-center pr-4 border-r border-gray-200'>
               <span className='text-sm font-medium text-gray-900'>{cityName}</span>
            </div>
         )}

         <div className='flex items-center'>
            <span
               className={`px-3 py-1 rounded-full text-xs font-medium ${
                  mode === DrawingMode.VIEWING
                     ? 'bg-blue-100 text-blue-700'
                     : mode === DrawingMode.DRAWING
                     ? 'bg-orange-100 text-orange-700'
                     : mode === DrawingMode.REDRAWING
                     ? 'bg-purple-100 text-purple-700'
                     : 'bg-green-100 text-green-700'
               }`}
            >
               {mode === DrawingMode.VIEWING ? 'View' : mode === DrawingMode.DRAWING ? 'Drawing' : mode === DrawingMode.REDRAWING ? 'Redrawing' : 'Edit'}
            </span>
         </div>

         <div className='flex items-center gap-2'>
            {mode === DrawingMode.VIEWING && (
               <>
                  <Button
                     onClick={onStartDrawing}
                     size='sm'
                     className='px-4 py-2 h-8 text-sm'
                     variant={hasPolygon ? 'outline' : 'default'}
                  >
                     {hasPolygon ? 'Redraw' : 'Start Drawing'}
                  </Button>

                  {hasPolygon && (
                     <>
                        <Button
                           onClick={onEditPolygon}
                           variant='outline'
                           size='sm'
                           className='px-4 py-2 h-8 text-sm'
                        >
                           Edit
                        </Button>

                        <Button
                           onClick={onDeletePolygon}
                           variant='outline'
                           size='sm'
                           className='px-4 py-2 h-8 text-sm text-red-600 hover:text-red-700 hover:bg-red-50'
                        >
                           Delete
                        </Button>
                     </>
                  )}
               </>
            )}

            {mode === DrawingMode.DRAWING && (
               <Button
                  onClick={onCancelDrawing}
                  variant='outline'
                  size='sm'
                  className='px-4 py-2 h-8 text-sm'
               >
                  Cancel
               </Button>
            )}

            {mode === DrawingMode.REDRAWING && (
               <>
                  <Button
                     onClick={onSavePolygon}
                     size='sm'
                     className='px-4 py-2 h-8 text-sm'
                     disabled={isSaving}
                  >
                     {isSaving ? 'Saving...' : 'Save'}
                  </Button>

                  <Button
                     onClick={onCancelDrawing}
                     variant='outline'
                     size='sm'
                     className='px-4 py-2 h-8 text-sm'
                  >
                     Cancel
                  </Button>
               </>
            )}

            {mode === DrawingMode.EDITING && (
               <>
                  <Button
                     onClick={onSavePolygon}
                     size='sm'
                     className='px-4 py-2 h-8 text-sm'
                     disabled={isSaving}
                  >
                     {isSaving ? 'Saving...' : 'Save'}
                  </Button>

                  <Button
                     onClick={onCancelDrawing}
                     variant='outline'
                     size='sm'
                     className='px-4 py-2 h-8 text-sm'
                  >
                     Cancel
                  </Button>
               </>
            )}

            {/* Fullscreen Toggle Button */}
            <Button
               onClick={onFullscreen}
               variant='outline'
               size='sm'
               className='px-3 py-2 h-9'
               title={isFullscreen ? 'Exit fullscreen' : 'Open in fullscreen'}
            >
               {isFullscreen ? <Minimize className='h-4 w-4' /> : <Maximize className='h-4 w-4' />}
            </Button>
         </div>
      </div>
   );
}

import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export enum DriverVehicleStatus {
  active = 'active',
  inactive = 'inactive',
  rejected = 'rejected',
}

export class ChangeDriverVehicleStatusDto {
  @ApiProperty({
    enum: DriverVehicleStatus,
    example: DriverVehicleStatus.active,
    description: 'New status for the driver vehicle',
  })
  @IsNotEmpty()
  @IsEnum(DriverVehicleStatus)
  status!: DriverVehicleStatus;
}

import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  Req,
  Res,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { DriverEarningsService } from '@shared/shared/modules/driver-earnings/driver-earnings.service';
import { PaymentService } from '@shared/shared/modules/payment/payment.service';
import { DriverAccountService } from '@shared/shared/modules/driver-account/driver-account.service';
import { DriverDueService } from '@shared/shared/modules/driver-due/driver-due-service';
import { CashfreeDuePaymentService } from '@shared/shared/modules/payment/cashfree-due-payment.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ApiRequest } from '@shared/shared/modules/auth/interfaces';
import { TransactionType } from '@shared/shared/repositories/models/driverAccount.model';
import { ApiErrorResponseDto } from '../../docs/swagger';
import {
  DriverEarningsQueryDto,
  PaymentHistoryQueryDto,
  AggregatedDriverEarningsQueryDto,
  DailyDriverEarningsQueryDto,
} from './dto/driver-earnings-query.dto';
import {
  DriverEarningsResponseDto,
  DriverAccountResponseDto,
  PaymentHistoryResponseDto,
  DailyEarningsDto,
  EarningsSummaryDto,
  DriverAccountDto,
  PaymentTransactionDto,
  AggregatedDriverEarningsResponseDto,
  DailyDriverEarningsResponseDto,
} from './dto/driver-earnings-response.dto';
import {
  InitiateDuePaymentDto,
  InitiateDuePaymentResponseDto,
  DueTransactionHistoryResponseDto,
  DriverBalanceWithDueStatusResponseDto,
  DriverDueStatusDto,
} from './dto/driver-due-payment.dto';
import { UserProfileService } from '@shared/shared/modules/user-profile/user-profile.service';
import { DriverEarningsReportService } from './services/driver-earnings-report.service';

@ApiTags('Driver Earnings')
@Controller('driver-earnings')
export class DriverEarningsController {
  constructor(
    private readonly driverEarningsService: DriverEarningsService,
    private readonly paymentService: PaymentService,
    private readonly driverAccountService: DriverAccountService,
    private readonly driverDueService: DriverDueService,
    private readonly cashfreePaymentService: CashfreeDuePaymentService,
    private readonly userProfileService: UserProfileService,
    private readonly driverEarningsReportService: DriverEarningsReportService,
  ) {}

  @Get('earnings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver earnings',
    description:
      'Get paginated driver earnings data with optional date range filtering. Returns daily earnings grouped by date with summary statistics.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter for earnings (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter for earnings (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['today', 'week', 'month', 'year'],
    description: 'Predefined period for filtering',
    example: 'month',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver earnings retrieved successfully',
    type: DriverEarningsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async getDriverEarnings(
    @Req() req: ApiRequest,
    @Query() query: DriverEarningsQueryDto,
  ): Promise<DriverEarningsResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    let { fromDate, toDate } = query;
    const { page = 1, limit = 10, period } = query;

    // Handle predefined periods
    if (period) {
      const dateRange =
        this.driverEarningsService.getDateRangeForPeriod(period);
      fromDate = dateRange.fromDate;
      toDate = dateRange.toDate;
    }

    // Get earnings data and summary
    const [earningsResult, summary] = await Promise.all([
      this.driverEarningsService.getDriverEarnings(driverId, {
        fromDate,
        toDate,
        page,
        limit,
      }),
      this.driverEarningsService.getDriverEarningsStats(
        driverId,
        fromDate,
        toDate,
      ),
    ]);

    // Transform earnings data
    const dailyEarnings: DailyEarningsDto[] = earningsResult.earnings.map(
      (earning) => ({
        date: earning.date.toISOString().split('T')[0],
        totalFareAmount: earning.totalFareAmount,
        completedRides: earning.completedRides,
        averageEarningsPerRide: earning.averageEarningsPerRide,
      }),
    );

    // Transform summary data
    const summaryDto: EarningsSummaryDto = {
      totalEarnings: summary.totalEarnings,
      totalRides: summary.totalRides,
      averageEarningsPerRide: summary.averageEarningsPerRide,
      averageEarningsPerDay: summary.averageEarningsPerDay,
      daysWorked: summary.daysWorked,
      bestDay: summary.bestDay
        ? {
            date: summary.bestDay.date.toISOString().split('T')[0],
            earnings: summary.bestDay.earnings,
          }
        : null,
      worstDay: summary.worstDay
        ? {
            date: summary.worstDay.date.toISOString().split('T')[0],
            earnings: summary.worstDay.earnings,
          }
        : null,
    };

    return {
      success: true,
      message: 'Driver earnings retrieved successfully',
      data: dailyEarnings,
      meta: {
        page: earningsResult.page,
        limit: earningsResult.limit,
        total: earningsResult.total,
        totalPages: earningsResult.totalPages,
        summary: summaryDto,
      },
      timestamp: Date.now(),
    };
  }

  @Get('account')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver account',
    description:
      'Get driver account information including current balance, account details, and due status. Returns restriction status if driver has exceeded due limit.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver account retrieved successfully',
    type: DriverAccountResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Driver profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver account not found',
    type: ApiErrorResponseDto,
  })
  async getDriverAccount(
    @Req() req: ApiRequest,
  ): Promise<DriverAccountResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const account =
      await this.driverAccountService.getOrCreateDriverAccount(driverId);

    // Get due status if driver has a city
    let dueStatus: DriverDueStatusDto | undefined;
    const cityId = (req.user as any)?.cityId;

    if (cityId) {
      const status = await this.driverDueService.getDriverDueStatus(
        driverId,
        cityId,
      );

      dueStatus = {
        isDueExceeded: status.isDueExceeded,
        currentBalance: status.currentBalance,
        dueLimit: status.dueLimit,
        remainingDue: status.remainingDue,
        message: status.isDueExceeded
          ? 'Please clear your due amount to continue receiving rides.'
          : 'Your account is in good standing',
      };
    }

    const accountDto: DriverAccountDto = {
      id: account.id,
      driverId: account.driverId,
      availableBalance: Number(account.availableBalance),
      createdAt: account.createdAt.toISOString(),
      updatedAt: account.updatedAt.toISOString(),
      driver: (account as any).driver
        ? {
            id: (account as any).driver.id,
            firstName: (account as any).driver.firstName,
            lastName: (account as any).driver.lastName,
          }
        : undefined,
    };

    return {
      success: true,
      message: 'Driver account retrieved successfully',
      data: {
        ...accountDto,
        dueStatus,
      } as any,
      timestamp: Date.now(),
    };
  }

  @Get('payments')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver payment history',
    description:
      'Get paginated list of payment transactions for the authenticated driver with optional filtering by date range and payment type.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter for payments (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter for payments (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'paymentType',
    required: false,
    enum: ['CASH', 'ONLINE'],
    description: 'Filter by payment type',
    example: 'CASH',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
    type: PaymentHistoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  async getDriverPayments(
    @Req() req: ApiRequest,
    @Query() query: PaymentHistoryQueryDto,
  ): Promise<PaymentHistoryResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const { page = 1, limit = 10 } = query;

    const result = await this.paymentService.getDriverPayments(
      driverId,
      page,
      limit,
      query,
    );

    // Transform payment data
    const payments: PaymentTransactionDto[] = result.payments.map(
      (payment) => ({
        id: payment.id,
        rideId: payment.rideId,
        amount: Number(payment.amount),
        paymentType: payment.paymentType || 'PENDING',
        receivedAt: payment.receivedAt?.toISOString() || null,
        createdAt: payment.createdAt.toISOString(),
        ride: (payment as any).ride
          ? {
              id: (payment as any).ride.id,
              pickupLocation: (payment as any).ride.pickupLocation,
              destinationLocation: (payment as any).ride.destinationLocation,
              completedAt: (payment as any).ride.completedAt?.toISOString(),
            }
          : undefined,
      }),
    );

    return {
      success: true,
      message: 'Payment history retrieved successfully',
      data: payments,
      meta: {
        page,
        limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('aggregated')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get aggregated driver earnings',
    description:
      'Get aggregated earnings for all drivers with optional filtering by date range, driver ID, or city. Returns total fare, commissions, taxes, and net earnings per driver.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'driverId',
    required: false,
    type: 'string',
    description: 'Filter by specific driver ID',
  })
  @ApiQuery({
    name: 'cityId',
    required: false,
    type: 'string',
    description: 'Filter by city ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Aggregated driver earnings retrieved successfully',
    type: AggregatedDriverEarningsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  async getAggregatedDriverEarnings(
    @Query() query: AggregatedDriverEarningsQueryDto,
  ): Promise<AggregatedDriverEarningsResponseDto> {
    const {
      page = 1,
      limit = 10,
      fromDate,
      toDate,
      driverId,
      cityId,
      phoneNumber,
    } = query;

    const result = await this.driverEarningsService.getAggregatedEarnings(
      page,
      limit,
      fromDate,
      toDate,
      driverId,
      cityId,
      phoneNumber,
    );

    return {
      success: true,
      message: 'Aggregated driver earnings retrieved successfully',
      data: result.data,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('daily')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get daily driver earnings',
    description:
      'Get day-wise earnings breakdown for a specific driver. Returns daily totals for fare, commissions, taxes, and net earnings.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Daily driver earnings retrieved successfully',
    type: DailyDriverEarningsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  async getDailyDriverEarnings(
    @Query() query: DailyDriverEarningsQueryDto,
  ): Promise<DailyDriverEarningsResponseDto> {
    const {
      page = 1,
      limit = 10,
      fromDate,
      toDate,
      driverId: queryDriverId,
    } = query;

    // Use driverId from path parameter if available, otherwise from query
    const driverId = queryDriverId;
    if (!driverId) {
      throw new BadRequestException('Driver ID is required');
    }

    const result = await this.driverEarningsService.getDailyEarningsForDriver(
      driverId,
      page,
      limit,
      fromDate,
      toDate,
    );

    return {
      success: true,
      message: 'Daily driver earnings retrieved successfully',
      data: result.data,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Post('payment/initiate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Initiate due payment',
    description:
      'Initiate a payment via Cashfree gateway to clear driver dues. Returns a payment link for the driver to complete the payment.',
  })
  @ApiResponse({
    status: 201,
    description: 'Payment initiated successfully',
    type: InitiateDuePaymentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or payment initiation failed',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async initiateDuePayment(
    @Req() req: ApiRequest,
    @Body() dto: InitiateDuePaymentDto,
  ): Promise<InitiateDuePaymentResponseDto> {
    const driverId = req.user?.profileId;

    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    // Generate unique transaction ID
    const transactionId = `DUE_${driverId}_${Date.now()}`;
    const driverProfile = await this.userProfileService.findUserProfileById(
      driverId,
      {
        include: {
          city: true,
          user: true,
        },
      },
    );

    if (!driverProfile.cityId) {
      throw new BadRequestException(`Driver does not have a city assigned`);
    }

    // Initiate payment with Cashfree
    const paymentResponse =
      await this.cashfreePaymentService.initiateDuePayment({
        transactionId,
        driverId,
        amount: dto.amount,
        customerEmail: driverProfile.user?.email || '',
        customerPhone: driverProfile.user?.phoneNumber || '',
      });

    const account =
      await this.driverAccountService.getOrCreateDriverAccount(driverId);
    await this.driverDueService.recordDuePayment({
      driverId,
      cityId: driverProfile.cityId,
      transactionId,
      amount: dto.amount,
      paymentMethod: 'CASHFREE',
      paymentStatus: 'PENDING',
      balanceBefore: Number(account.availableBalance),
      balanceAfter: Number(account.availableBalance),
    });

    return {
      success: true,
      message: 'Payment initiated successfully',
      data: {
        transactionId,
        paymentLink: paymentResponse.paymentLink || undefined,
        amount: dto.amount,
        ...paymentResponse.data,
      },
      timestamp: Date.now(),
    };
  }

  @Get('due-transactions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver due transaction history',
    description:
      'Retrieve paginated list of all due payment transactions made by the driver.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Transaction history retrieved successfully',
    type: DueTransactionHistoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Driver profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async getDriverDueTransactions(
    @Req() req: ApiRequest,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ): Promise<DueTransactionHistoryResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const result = await this.driverDueService.getDriverDueTransactions(
      driverId,
      parseInt(page, 10),
      parseInt(limit, 10),
    );

    const transactions = result.transactions.map((txn) => ({
      id: txn.id,
      driverId: txn.driverId,
      cityId: txn.cityId,
      transactionId: txn.transactionId,
      amount: Number(txn.amount),
      paymentMethod: txn.paymentMethod,
      paymentStatus: txn.paymentStatus,
      balanceBefore: Number(txn.balanceBefore),
      balanceAfter: Number(txn.balanceAfter),
      createdAt: txn.createdAt.toISOString(),
      updatedAt: txn.updatedAt.toISOString(),
    }));

    return {
      success: true,
      message: 'Transaction history retrieved successfully',
      data: transactions,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('balance-with-due-status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver balance with due status',
    description:
      'Get current driver balance along with due status information. Includes whether driver is restricted from going online due to exceeded dues.',
  })
  @ApiResponse({
    status: 200,
    description: 'Balance and due status retrieved successfully',
    type: DriverBalanceWithDueStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Driver profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async getBalanceWithDueStatus(
    @Req() req: ApiRequest,
  ): Promise<DriverBalanceWithDueStatusResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    // Get driver account
    const account =
      await this.driverAccountService.getOrCreateDriverAccount(driverId);
    const balance = Number(account.availableBalance);

    // Get driver's city to check due status
    const driverProfile =
      await this.userProfileService.findUserProfileById(driverId);

    if (!driverProfile.cityId) {
      throw new BadRequestException(`Driver does not have a city assigned`);
    }

    let dueStatus: DriverDueStatusDto = {
      isDueExceeded: false,
      currentBalance: balance,
      dueLimit: 0,
      remainingDue: 0,
      message: 'Your account is in good standing',
      transactions:
        account?.transactions && account.transactions.length > 0
          ? account.transactions
          : [],
    };

    if (driverProfile.cityId) {
      const status = await this.driverDueService.getDriverDueStatus(
        driverId,
        driverProfile.cityId,
      );

      dueStatus = {
        isDueExceeded: status.isDueExceeded,
        currentBalance: status.currentBalance,
        dueLimit: status.dueLimit,
        remainingDue: status.remainingDue,
        message: status.isDueExceeded
          ? 'Please clear your due amount to continue receiving rides.'
          : 'Your account is in good standing',
        transactions:
          account?.transactions && account.transactions.length > 0
            ? account.transactions
            : [],
      };
    }

    return {
      success: true,
      message: 'Balance and due status retrieved successfully',
      data: {
        balance,
        dueStatus,
      },
      timestamp: Date.now(),
    };
  }

  @Post('reports/aggregated/csv')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Download aggregated driver earnings CSV report',
    description:
      'Generate and download a CSV report of aggregated driver earnings with optional filtering by date range, driver ID, or city.',
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter (ISO 8601)',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter (ISO 8601)',
  })
  @ApiQuery({
    name: 'driverId',
    required: false,
    type: 'string',
    description: 'Filter by specific driver ID',
  })
  @ApiQuery({
    name: 'cityId',
    required: false,
    type: 'string',
    description: 'Filter by city ID',
  })
  @ApiResponse({
    status: 200,
    description: 'CSV report generated successfully',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadAggregatedEarningsReport(
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
    @Query('driverId') driverId?: string,
    @Query('cityId') cityId?: string,
    @Res() res?: Response,
  ): Promise<void> {
    const parsedFromDate = fromDate ? new Date(fromDate) : undefined;
    const parsedToDate = toDate ? new Date(toDate) : undefined;

    const report =
      await this.driverEarningsReportService.generateAggregatedEarningsReport(
        parsedFromDate,
        parsedToDate,
        driverId,
        cityId,
      );

    res!.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res!.setHeader(
      'Content-Disposition',
      `attachment; filename="${report.fileName}"`,
    );
    res!.send(report.content);
  }

  @Post('reports/daily/csv/:driverId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Download daily driver earnings CSV report',
    description:
      'Generate and download a CSV report of daily earnings for a specific driver with optional date range filtering.',
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter (ISO 8601)',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter (ISO 8601)',
  })
  @ApiResponse({
    status: 200,
    description: 'CSV report generated successfully',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadDailyEarningsReport(
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
    @Query('driverId') queryDriverId?: string,
    @Res() res?: Response,
  ): Promise<void> {
    const parsedFromDate = fromDate ? new Date(fromDate) : undefined;
    const parsedToDate = toDate ? new Date(toDate) : undefined;

    // Use driverId from path parameter if available
    const driverId = queryDriverId;
    if (!driverId) {
      throw new BadRequestException('Driver ID is required');
    }

    const report =
      await this.driverEarningsReportService.generateDailyEarningsReport(
        driverId,
        parsedFromDate,
        parsedToDate,
      );

    res!.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res!.setHeader(
      'Content-Disposition',
      `attachment; filename="${report.fileName}"`,
    );
    res!.send(report.content);
  }

  @Post('payment/callback')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle payment callback',
    description:
      'Webhook endpoint to handle payment status updates from Cashfree gateway. Updates driver balance and transaction status.',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment callback processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid callback data',
    type: ApiErrorResponseDto,
  })
  async handlePaymentCallback(
    @Body() payload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Extract data from Cashfree webhook payload
      const { data } = payload;

      if (!data || !data['order'] || !data['payment']) {
        throw new BadRequestException('Invalid webhook payload structure');
      }

      const transactionId = data['order']['order_id'];
      const paymentStatus = data['payment']['payment_status'];

      if (!transactionId || !paymentStatus) {
        throw new BadRequestException(
          'Missing required fields: transactionId or paymentStatus',
        );
      }

      // Verify payment with Cashfree
      await this.cashfreePaymentService.verifyPaymentStatus(transactionId);

      // Update transaction status
      await this.driverDueService.updateTransactionStatus(
        transactionId,
        paymentStatus,
      );

      // If payment successful, update driver balance
      if (paymentStatus == 'SUCCESS' || paymentStatus == 'SETTLED') {
        // Get transaction details
        const transaction =
          await this.driverDueService.getDriverDueTransactionByid(
            transactionId,
          );

        if (transaction) {
          // Calculate new balance (add payment amount to current balance)
          const newBalance =
            Number(transaction.balanceBefore) + Number(transaction.amount);

          // Log transaction in driver account
          await this.driverAccountService.logTransaction(
            {
              driverId: transaction.driverId,
              amount: Number(transaction.amount),
              transactionType: TransactionType.CREDIT,
              reason: `Due payment cleared - Transaction ${transactionId}`,
            },
            newBalance,
          );

          await this.driverAccountService.updateDriverBalanceDirect(
            transaction.driverId,
            newBalance,
          );

          // Update due payment transaction with new balance
          await this.driverDueService.updateTransactionStatus(
            transactionId,
            'SUCCESS',
            newBalance,
          );
        }
      }

      return {
        success: true,
        message: 'Payment callback processed successfully',
      };
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to process payment callback: ${error?.message}`,
      );
    }
  }
}

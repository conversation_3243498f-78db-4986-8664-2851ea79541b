'use client';

import React from 'react';
import { Marker } from '@react-google-maps/api';
import { RadarDriver } from '../types/radar';
import { getStatusColor } from '../utils/map-utils';

interface DriverMarkerProps {
  driver: RadarDriver;
  onClick: (driver: RadarDriver) => void;
}

export const DriverMarker = React.memo(({ driver, onClick }: DriverMarkerProps) => {
  const statusColor = getStatusColor(driver.status);

  // Create custom marker icon with product icon or initials
  const createMarkerIcon = (): google.maps.Icon | google.maps.Symbol => {
    if (driver.productIcon) {
      // Use product icon if available
      return {
        url: driver.productIcon,
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 20),
      };
    }

    // Fallback to colored circle with initials
    // Note: For text rendering, we need to use AdvancedMarker or HTML overlay
    // Using symbol path for now
    return {
      path: google.maps.SymbolPath.CIRCLE,
      scale: 20,
      fillColor: '#FFFFFF',
      fillOpacity: 1,
      strokeColor: statusColor,
      strokeWeight: 4,
    };
  };

  return (
    <Marker
      position={{ lat: driver.lat, lng: driver.lng }}
      icon={createMarkerIcon()}
      title={`${driver.productName} - ${driver.status}`}
      onClick={() => onClick(driver)}
      animation={google.maps.Animation.DROP}
    />
  );
});

DriverMarker.displayName = 'DriverMarker';

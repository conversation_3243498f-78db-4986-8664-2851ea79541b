'use client';

import CarIcon from '@/assets/car.svg';
import { Marker } from '@react-google-maps/api';
import React from 'react';
import { RadarDriver } from '../types/radar';
import { StatusCircleOverlay } from './status-circle-overlay';

interface DriverMarkerProps {
   driver: RadarDriver;
   onClick: (driver: RadarDriver) => void;
   map: google.maps.Map | null;
}

export const DriverMarker = React.memo(({ driver, onClick, map }: DriverMarkerProps) => {
   // Create custom marker icon with product icon or initials
   const createMarkerIcon = (): google.maps.Icon | google.maps.Symbol => {
      if (driver.productIcon) {
         // Use product icon if available (compact size for high-density areas)
         return {
            url: driver.productIcon as string,
            scaledSize: new google.maps.Size(28, 28),
            anchor: new google.maps.Point(14, 14),
         };
      }

      // Fallback to car icon
      return {
         url: typeof CarIcon === 'string' ? CarIcon : CarIcon.src,
         scaledSize: new google.maps.Size(32, 32),
         anchor: new google.maps.Point(16, 16),
      };
   };

   return (
      <>
         {/* Status circle overlay - renders behind the marker */}
         <StatusCircleOverlay
            position={{ lat: driver.lat, lng: driver.lng }}
            status={driver.status}
            map={map}
         />

         {/* Driver marker */}
         <Marker
            position={{ lat: driver.lat, lng: driver.lng }}
            icon={createMarkerIcon()}
            title={`${driver.productName} - ${driver.status}`}
            onClick={() => onClick(driver)}
         />
      </>
   );
});

DriverMarker.displayName = 'DriverMarker';

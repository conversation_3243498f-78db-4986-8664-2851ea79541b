-- CreateTable
CREATE TABLE "driver_due_configs" (
    "id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "max_due_limit" DECIMAL(10,2) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_due_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "driver_due_payment_transactions" (
    "id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "transaction_id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "payment_method" TEXT NOT NULL,
    "payment_status" TEXT NOT NULL,
    "balance_before" DECIMAL(10,2) NOT NULL,
    "balance_after" DECIMAL(10,2) NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_due_payment_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "driver_due_configs_city_id_key" ON "driver_due_configs"("city_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_config_city_id" ON "driver_due_configs"("city_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_config_is_active" ON "driver_due_configs"("is_active");

-- CreateIndex
CREATE UNIQUE INDEX "driver_due_payment_transactions_transaction_id_key" ON "driver_due_payment_transactions"("transaction_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_payment_transaction_driver_id" ON "driver_due_payment_transactions"("driver_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_payment_transaction_city_id" ON "driver_due_payment_transactions"("city_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_payment_transaction_id" ON "driver_due_payment_transactions"("transaction_id");

-- CreateIndex
CREATE INDEX "idx_driver_due_payment_transaction_status" ON "driver_due_payment_transactions"("payment_status");

-- CreateIndex
CREATE INDEX "idx_driver_due_payment_transaction_created_at" ON "driver_due_payment_transactions"("created_at");

-- AddForeignKey
ALTER TABLE "driver_due_configs" ADD CONSTRAINT "driver_due_configs_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_due_payment_transactions" ADD CONSTRAINT "driver_due_payment_transactions_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_due_payment_transactions" ADD CONSTRAINT "driver_due_payment_transactions_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

// Language interface for API responses
export interface Language {
   id: string;
   code: string;
   name: string;
   nameInNative: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// API response structure for listing languages
export interface ListLanguageResponse {
   success: boolean;
   message: string;
   data: Language[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
   };
   timestamp: number;
}

// API response structure for single language
export interface LanguageResponse {
   success: boolean;
   message: string;
   data: Language;
   timestamp: number;
}

// Request for creating language
export interface CreateLanguageRequest {
   code: string;
   name: string;
   nameInNative: string;
}

// Request for updating language
export interface UpdateLanguageRequest {
   code?: string;
   name?: string;
   nameInNative?: string;
}

// Parameters for listing languages with pagination
export interface ListLanguageParams {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
}
// Enums matching backend
export enum ChargeType {
  FLAT = 'flat',
  METERED = 'metered',
}

export enum ChargeMeter {
  PICKUP_DISTANCE = 'pickup_distance',
  PICKUP_DURATION = 'pickup_duration',
  PICKUP_WAIT_DURATION = 'pickup_wait_duration',
  TRIP_DURATION = 'trip_duration',
  TRIP_WAIT_DURATION = 'trip_wait_duration',
  TRIP_DISTANCE = 'trip_distance',
}

export enum PriceModel {
  FLAT_AMOUNT = 'flat_amount',
  LINEAR_RATE = 'linear_rate',
  TIERED = 'tiered',
  PERCENTAGE_OF_CHARGE = 'percentage_of_charge',
  FORMULA = 'formula',
}

export enum ConditionType {
  BOOKING_TIME = 'booking_time',
  // Future condition types can be added here:
  // DAY_OF_WEEK = 'day_of_week',
  // LOCATION = 'location',
  // CUSTOMER_TYPE = 'customer_type',
}

// Condition configuration types
export interface TimeRangeConfig {
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  operator: 'or' | 'and'; // How to combine with the next range within this condition
}

// Each condition block is self-contained with its own type and configuration
export interface ConditionBlock {
  type: ConditionType;
  config: TimeRangeConfig[]; // For BOOKING_TIME type (can be union type in future)
  operator: 'and' | 'or'; // How to combine with the next condition block
}

// Price configuration types
export interface TierConfig {
  From: number;
  To: number | 'inf';
  Flat_fee?: number;
  Rate?: number;
  currency: string;
}

export interface PriceConfig {
  amount?: number;
  currency?: string;
  rate?: number;
  tiers?: TierConfig[];
  formula?: string;
}

// Charge interface for API responses
export interface Charge {
  id: string;
  name: string;
  identifier: string;
  chargeType: ChargeType;
  condition?: any | null;
  meter?: ChargeMeter | null;
  priceModel: PriceModel;
  price: PriceConfig;
  percentage?: number | null;
  percentageOfChargeId?: string | null;
  chargeGroupId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  // Relations
  percentageOfCharge?: Charge | null;
  // Priority data from ChargeGroupCharge relationship
  priority?: number;
  chargeGroupChargeId?: string; // ID of the ChargeGroupCharge relationship for priority updates
}

// ChargeGroupCharge response from the backend (relationship with nested charge)
export interface ChargeGroupChargeResponse {
  id: string; // ChargeGroupCharge relationship ID
  chargeGroupId: string;
  chargeId: string;
  priority: number;
  createdAt: string;
  updatedAt: string;
  charge: Charge; // Nested charge object
}

// Pagination metadata
export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Paginated data response
export interface PaginatedData<T> {
  data: T[];
  meta: PaginationMeta;
}

// API response structure for listing charges
export interface ListChargesResponse {
  success: boolean;
  message: string;
  data: Charge[];
  timestamp: number;
}

// API response structure for paginated charges
export interface PaginatedChargesResponse {
  success: boolean;
  message: string;
  data: PaginatedData<Charge>;
  timestamp: number;
}

// API response structure for single charge
export interface ChargeResponse {
  success: boolean;
  message: string;
  data: Charge;
  timestamp: number;
}

// Request for creating charge
export interface CreateChargeRequest {
  name: string;
  identifier?: string;
  chargeType: ChargeType;
  condition?: any;
  meter?: ChargeMeter;
  priceModel: PriceModel;
  price?: PriceConfig;
  percentage?: number;
  percentageOfChargeId?: string;
}

// Request for updating charge
export interface UpdateChargeRequest {
  name?: string;
  identifier?: string;
  chargeType?: ChargeType;
  condition?: any;
  meter?: ChargeMeter;
  priceModel?: PriceModel;
  price?: PriceConfig;
  percentage?: number;
  percentageOfChargeId?: string;
}

// Human-readable labels
export const CHARGE_TYPE_LABELS: Record<ChargeType, string> = {
  [ChargeType.FLAT]: 'Flat',
  [ChargeType.METERED]: 'Metered',
};

export const CHARGE_METER_LABELS: Record<ChargeMeter, string> = {
  [ChargeMeter.PICKUP_DISTANCE]: 'Pickup Distance',
  [ChargeMeter.PICKUP_DURATION]: 'Pickup Duration',
  [ChargeMeter.PICKUP_WAIT_DURATION]: 'Pickup Wait Duration',
  [ChargeMeter.TRIP_DURATION]: 'Trip Duration',
  [ChargeMeter.TRIP_WAIT_DURATION]: 'Trip Wait Duration',
  [ChargeMeter.TRIP_DISTANCE]: 'Trip Distance',
};

export const PRICE_MODEL_LABELS: Record<PriceModel, string> = {
  [PriceModel.FLAT_AMOUNT]: 'Flat Amount',
  [PriceModel.LINEAR_RATE]: 'Linear Rate',
  [PriceModel.TIERED]: 'Tiered',
  [PriceModel.PERCENTAGE_OF_CHARGE]: 'Percentage of Charge',
  [PriceModel.FORMULA]: 'Formula',
};

export const CONDITION_TYPE_LABELS: Record<ConditionType, string> = {
  [ConditionType.BOOKING_TIME]: 'Booking Time',
};

// Request for attaching existing charge to charge group
export interface AttachChargeRequest {
  chargeId: string;
}
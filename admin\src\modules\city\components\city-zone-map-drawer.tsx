'use client';

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { Zone } from '../types/city-zone';
import { CityZoneMapDrawerContent } from './city-zone-map-drawer-content';
import { useGetZone } from '../api/city-zone-queries';

interface CityZoneMapDrawerProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   zone: Zone | null;
}

export function CityZoneMapDrawer({ open, onOpenChange, zone }: CityZoneMapDrawerProps) {
   const { data: selectedZoneData, isLoading } = useGetZone(zone?.id || null);

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            showCloseButton={false}
            className='sm:max-w-[90vw] sm:max-h-[90vh] h-[80vh] p-0'
         >
            <VisuallyHidden>
               <DialogTitle>Zone Boundary Map - {zone?.name}</DialogTitle>
            </VisuallyHidden>

            {isLoading && (
               <div className='h-full w-full bg-gray-100 flex items-center justify-center'>
                  <div className='text-center'>
                     <p className='text-gray-600'>Loading map...</p>
                  </div>
               </div>
            )}

            {!isLoading && selectedZoneData?.data && (
               <CityZoneMapDrawerContent
                  zone={selectedZoneData?.data}
                  onClose={() => onOpenChange(false)}
               />
            )}
         </DialogContent>
      </Dialog>
   );
}

import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   CreateDriverRequest,
   DriverOtpVerificationRequest,
   DriverRegistrationRequest,
   DriverRegistrationResponse,
   DriverResendOtpRequest,
   DriverResponse,
   UpdateDriverRequest,
   AddKycDocumentRequest,
   AddKycDocumentResponse,
   CreateDriverVehicleRequest,
   CreateDriverVehicleResponse,
   VehicleVerificationResponse,
   UploadNocRequest,
   ApproveRejectVehicleDocumentRequest,
   ChangeVehicleStatusRequest,
   UploadVehicleDocumentRequest,
   UpdateDriverVehicleRequest,
   UpdateDriverStatusRequest,
   SendEmailVerificationRequest,
   VerifyEmailOtpRequest,
} from '../types/driver';

/**
 * Hook for creating a new driver profile (after OTP verification)
 */
export const useCreateDriver = () => {
   return useMutation({
      mutationFn: async (data: CreateDriverRequest): Promise<DriverResponse> => {
         return apiClient.post('/drivers/admin', data);
      },
   });
};

/**
 * Hook for updating a driver profile
 */
export const useUpdateDriver = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateDriverRequest): Promise<DriverResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/drivers/admin/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a driver
 */
export const useDeleteDriver = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/drivers/admin/${id}`);
      },
   });
};

/**
 * Hook for registering a new driver with phone number
 */
export const useRegisterDriver = () => {
   return useMutation({
      mutationFn: async (data: DriverRegistrationRequest): Promise<DriverRegistrationResponse> => {
         return apiClient.post('/drivers/admin/register', data);
      },
   });
};

/**
 * Hook for verifying driver OTP
 */
export const useVerifyDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverOtpVerificationRequest
      ): Promise<{ success: boolean; message: string; data?: any; timestamp: number }> => {
         return apiClient.post('/drivers/admin/verify-otp', data);
      },
   });
};

/**
 * Hook for resending driver OTP
 */
export const useResendDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverResendOtpRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/resend-otp', data);
      },
   });
};

/**
 * Hook for adding KYC document
 */
export const useAddKycDocument = () => {
   return useMutation({
      mutationFn: async (data: AddKycDocumentRequest): Promise<AddKycDocumentResponse> => {
         return apiClient.post('/driver-kyc/admin/add-kyc-document', data);
      },
   });
};

/**
 * Hook for deleting KYC document
 */
export const useDeleteKycDocument = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.delete(`/driver-kyc/${id}`);
      },
   });
};

/**
 * Hook for approving KYC document
 */
export const useApproveKycDocument = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.patch(`/driver-kyc/${id}/accept`);
      },
   });
};

/**
 * Hook for rejecting KYC document
 */
export const useRejectKycDocument = () => {
   return useMutation({
      mutationFn: async (data: {
         id: string;
         rejectionNote: string;
      }): Promise<{ success: boolean; message: string }> => {
         const { id, rejectionNote } = data;
         return apiClient.patch(`/driver-kyc/${id}/reject`, { rejectionNote });
      },
   });
};

/**
 * Hook for editing KYC document (re-upload)
 */
export const useEditKycDocument = () => {
   return useMutation({
      mutationFn: async (data: {
         id: string;
         documentUrl: string;
         documentNumber?: string;
         expiryDate?: string;
         documentFields?: Record<string, any>;
      }): Promise<{ success: boolean; message: string }> => {
         const { id, documentUrl, documentNumber, expiryDate, documentFields } = data;
         const payload: any = { documentUrl };

         if (documentNumber) payload.documentNumber = documentNumber;
         if (expiryDate) payload.expiryDate = expiryDate;
         if (documentFields) payload.documentFields = documentFields;

         return apiClient.patch(`/driver-kyc/admin/edit-kyc-document/${id}`, payload);
      },
   });
};

/**
 * Hook for creating a new driver vehicle
 */
export const useCreateDriverVehicle = () => {
   return useMutation({
      mutationFn: async (
         data: CreateDriverVehicleRequest
      ): Promise<CreateDriverVehicleResponse> => {
         return apiClient.post('/driver-vehicles/admin/create', data);
      },
   });
};

/**
 * Hook for verifying driver vehicle RC
 */
export const useVerifyDriverVehicle = () => {
   return useMutation({
      mutationFn: async (driverVehicleId: string): Promise<VehicleVerificationResponse> => {
         return apiClient.post(`/driver-vehicles/${driverVehicleId}/admin/verify-rc`);
      },
   });
};

/**
 * Hook for uploading NOC document
 */
export const useUploadNocDocument = () => {
   return useMutation({
      mutationFn: async (
         data: { driverVehicleId: string } & UploadNocRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { driverVehicleId, documentUrl } = data;
         return apiClient.post(`/driver-vehicles/${driverVehicleId}/admin/upload-noc`, {
            documentUrl,
         });
      },
   });
};

/**
 * Hook for approving or rejecting vehicle documents
 */
export const useApproveRejectVehicleDocument = () => {
   return useMutation({
      mutationFn: async (
         data: { documentId: string } & ApproveRejectVehicleDocumentRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { documentId, ...payload } = data;
         return apiClient.post(
            `/admin/driver-vehicle-documents/${documentId}/approve-reject`,
            payload
         );
      },
   });
};

/**
 * Hook for changing vehicle status (active/inactive)
 */
export const useChangeVehicleStatus = () => {
   return useMutation({
      mutationFn: async (
         data: { vehicleId: string } & ChangeVehicleStatusRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { vehicleId, status } = data;
         return apiClient.patch(`/driver-vehicles/admin/${vehicleId}/status`, { status });
      },
   });
};

/**
 * Hook for uploading vehicle documents (including NOC re-upload)
 */
export const useUploadVehicleDocument = () => {
   return useMutation({
      mutationFn: async (
         data: { vehicleId: string } & UploadVehicleDocumentRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { vehicleId, documentUrl } = data;
         return apiClient.post(`/driver-vehicles/${vehicleId}/admin/upload-noc`, { documentUrl });
      },
   });
};

/**
 * Hook for updating driver vehicle (type and primary status)
 */
export const useUpdateDriverVehicle = () => {
   return useMutation({
      mutationFn: async (
         data: { vehicleId: string } & UpdateDriverVehicleRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { vehicleId, ...payload } = data;
         return apiClient.patch(`/driver-vehicles/admin/update/${vehicleId}`, payload);
      },
   });
};

/**
 * Hook for updating driver status (active/inactive)
 */
export const useUpdateDriverStatus = () => {
   return useMutation({
      mutationFn: async (
         data: { id: string } & UpdateDriverStatusRequest
      ): Promise<{ success: boolean; message: string }> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/drivers/admin/${id}/status`, payload);
      },
   });
};

/**
 * Hook for sending email verification OTP
 */
export const useSendEmailVerification = () => {
   return useMutation({
      mutationFn: async (
         data: SendEmailVerificationRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/send-email-verification', data);
      },
   });
};

/**
 * Hook for verifying email OTP
 */
export const useVerifyEmailOtp = () => {
   return useMutation({
      mutationFn: async (
         data: VerifyEmailOtpRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/verify-email-otp', data);
      },
   });
};

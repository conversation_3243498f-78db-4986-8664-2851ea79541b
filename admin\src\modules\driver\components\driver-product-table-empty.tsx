import { Package, Search } from 'lucide-react';

interface DriverProductTableEmptyProps {
  hasFilters: boolean;
}

export function DriverProductTableEmpty({ hasFilters }: DriverProductTableEmptyProps) {
  return (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4'>
        {hasFilters ? (
          <Search className='w-6 h-6 text-gray-400' />
        ) : (
          <Package className='w-6 h-6 text-gray-400' />
        )}
      </div>
      <h3 className='text-lg font-medium text-gray-900 mb-2'>
        {hasFilters ? 'No products found' : 'No driver products'}
      </h3>
      <p className='text-gray-500 max-w-sm'>
        {hasFilters
          ? 'Try adjusting your search criteria to find what you are looking for.'
          : 'This driver does not have any products assigned yet. Add some city products to get started.'}
      </p>
    </div>
  );
}
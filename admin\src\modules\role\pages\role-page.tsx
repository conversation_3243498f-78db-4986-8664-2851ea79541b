'use client';

import { useState } from 'react';
import { useListRole } from '../api/queries';
// import { RoleModal } from '../components/role-modal';
import { RoleTable } from '../components/role-table';

export function RolePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);

   const listRole = useListRole({
      page,
      limit,
   });

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Roles</h2>
            <div className='flex gap-3'>
               {/* <RoleModal mode='create' /> */}
            </div>
         </div>

         <RoleTable
            data={listRole.data}
            isLoading={listRole.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />
      </div>
   );
}

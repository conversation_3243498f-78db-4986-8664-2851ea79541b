import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PaymentRepository } from '../../repositories/payment.repository';
import { Payment, PaymentType } from '../../repositories/models/payment.model';

export interface CreatePaymentData {
  rideId: string;
  riderId: string;
  driverId: string;
  amount: number;
}

export interface ConfirmPaymentData {
  rideId: string;
  paymentType: PaymentType;
  amount: number;
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(private readonly paymentRepository: PaymentRepository) {}

  /**
   * Create a payment record when ride ends
   */
  async createPayment(data: CreatePaymentData): Promise<Payment> {
    this.logger.log(`Creating payment record for ride ${data.rideId}`);

    // Check if payment already exists for this ride
    const existingPayment = await this.paymentRepository.findByRideId(
      data.rideId,
    );
    if (existingPayment) {
      throw new BadRequestException(
        `Payment already exists for ride ${data.rideId}`,
      );
    }

    return this.paymentRepository.createPayment({
      rideId: data.rideId,
      riderId: data.riderId,
      driverId: data.driverId,
      amount: data.amount,
      paymentType: null, // Initially null until payment is confirmed
      receivedAt: null,
    });
  }

  /**
   * Confirm payment with type and timestamp
   */
  async confirmPayment(data: ConfirmPaymentData): Promise<Payment> {
    this.logger.log(
      `Confirming payment for ride ${data.rideId} as ${data.paymentType}`,
    );

    const payment = await this.paymentRepository.findByRideId(data.rideId);
    if (!payment) {
      throw new NotFoundException(`Payment not found for ride ${data.rideId}`);
    }

    if (payment.paymentType) {
      throw new BadRequestException(
        `Payment for ride ${data.rideId} is already confirmed as ${payment.paymentType}`,
      );
    }

    return this.paymentRepository.confirmPayment(
      data.rideId,
      data.paymentType,
      data.amount,
    );
  }

  /**
   * Get payment by ride ID
   */
  async getPaymentByRideId(rideId: string): Promise<Payment | null> {
    return this.paymentRepository.findByRideId(rideId);
  }

  /**
   * Get payments for a driver with pagination and optional filtering
   */
  async getDriverPayments(
    driverId: string,
    page: number = 1,
    limit: number = 10,
    query?: {
      fromDate?: Date;
      toDate?: Date;
      paymentType?: 'CASH' | 'ONLINE';
    },
  ): Promise<{ payments: Payment[]; total: number; totalPages: number }> {
    const { payments, total } =
      await this.paymentRepository.findPaymentsByDriverId(
        driverId,
        page,
        limit,
        query?.fromDate,
        query?.toDate,
        query?.paymentType,
      );
    const totalPages = Math.ceil(total / limit);

    return { payments, total, totalPages };
  }

  /**
   * Get payments for a rider with pagination
   */
  async getRiderPayments(
    riderId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ payments: Payment[]; total: number; totalPages: number }> {
    const { payments, total } =
      await this.paymentRepository.findPaymentsByRiderId(riderId, page, limit);
    const totalPages = Math.ceil(total / limit);

    return { payments, total, totalPages };
  }

  /**
   * Get payment statistics for a driver
   */
  async getDriverPaymentStats(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    totalEarnings: number;
    cashPayments: number;
    onlinePayments: number;
    pendingPayments: number;
    totalRides: number;
  }> {
    // This would need to be implemented with proper aggregation queries
    // For now, returning a basic structure
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    // This is a simplified implementation - in production, you'd use aggregation
    const payments = await this.paymentRepository.findMany({
      where: whereClause,
    });

    const stats = payments.reduce(
      (acc, payment) => {
        acc.totalRides++;
        if (payment.paymentType === PaymentType.CASH) {
          acc.cashPayments += Number(payment.amount);
        } else if (payment.paymentType === PaymentType.ONLINE) {
          acc.onlinePayments += Number(payment.amount);
        } else {
          acc.pendingPayments += Number(payment.amount);
        }
        acc.totalEarnings += Number(payment.amount);
        return acc;
      },
      {
        totalEarnings: 0,
        cashPayments: 0,
        onlinePayments: 0,
        pendingPayments: 0,
        totalRides: 0,
      },
    );

    return stats;
  }
}

import { ApiProperty } from '@nestjs/swagger';

export class CityGeoResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '<PERSON><PERSON>' })
  name!: string;

  @ApiProperty({
    example: 'Kerala',
    required: false,
    nullable: true,
  })
  state?: string | null;

  @ApiProperty({
    example: 'India',
    required: false,
    nullable: true,
  })
  country?: string | null;

  @ApiProperty({
    example: {
      type: 'Polygon',
      coordinates: [
        [
          [77.5946, 12.9716],
          [77.6046, 12.9816],
          [77.5946, 12.9716],
        ],
      ],
    },
    description: 'GeoJSON polygon representing city boundaries',
    required: false,
    nullable: true,
  })
  polygon?: any | null;

  @ApiProperty({
    example: ['8c2a1072b1fffff', '8c2a1072b3fffff'],
    description: 'Array of H3 indexes covering the city',
  })
  h3Indexes!: string[];

  @ApiProperty({
    example: { colour: '#6668' },
    description: 'Additional metadata for the city as JSON object',
    required: false,
    nullable: true,
  })
  meta?: Record<string, any> | null;

  @ApiProperty({
    example: 'active',
    enum: ['active', 'inactive'],
  })
  status!: string;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;
}

export class CityGeoListApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Cities fetched successfully by geo coordinates' })
  message!: string;

  @ApiProperty({
    type: [CityGeoResponseDto],
    description: 'Array of cities within the specified radius',
  })
  data!: CityGeoResponseDto[];

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

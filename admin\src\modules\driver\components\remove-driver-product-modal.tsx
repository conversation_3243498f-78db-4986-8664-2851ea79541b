'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { AlertTriangle } from 'lucide-react';
import { useRemoveCityProductsFromDriver } from '../api/driver-product-mutations';
import { DriverProduct } from '../types/driver-product';
import { useParams } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';

interface RemoveDriverProductModalProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   driverProduct: DriverProduct | null;
   vehicleId: string;
}

export function RemoveDriverProductModal({
   open,
   onOpenChange,
   driverProduct,
   vehicleId,
}: RemoveDriverProductModalProps) {
   const params = useParams<{ id: string }>();
   const driverId = params.id;
   const removeProductsMutation = useRemoveCityProductsFromDriver(vehicleId);
   const queryClient = useQueryClient();

   const handleRemove = () => {
      if (!driverProduct) return;

      const cityProductIds = [driverProduct.cityProductId];

      removeProductsMutation.mutate(
         {
            driverVehicleId: vehicleId,
            cityProductIds,
         },
         {
            onSuccess: () => {
               queryClient.invalidateQueries({ queryKey: ['driver-vehicles', driverId] });
               queryClient.invalidateQueries({ queryKey: ['vehicle-documents', vehicleId] });
               queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
               onOpenChange(false);
            },
         }
      );
   };

   const isSubmitting = removeProductsMutation.isPending;
   const product = driverProduct?.cityProduct?.product;
   const city = driverProduct?.cityProduct?.city;

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='sm:max-w-[400px]'>
            <DialogHeader>
               <div className='flex items-center gap-3'>
                  <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
                     <AlertTriangle className='w-5 h-5 text-red-600' />
                  </div>
                  <div>
                     <DialogTitle className='text-red-900'>Remove City Product</DialogTitle>
                     <DialogDescription className='text-red-700'>
                        This action cannot be undone.
                     </DialogDescription>
                  </div>
               </div>
            </DialogHeader>

            <div className='py-4'>
               <p className='text-sm text-gray-600'>
                  Are you sure you want to remove{' '}
                  <span className='font-medium text-gray-900'>
                     {product?.name || 'this product'}
                  </span>{' '}
                  from{' '}
                  <span className='font-medium text-gray-900'>{city?.name || 'this city'}</span> for
                  this driver?
               </p>
               <p className='text-sm text-gray-500 mt-2'>
                  The city product will no longer be available for this driver's vehicle.
               </p>
            </div>

            <DialogFooter>
               <Button
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
               >
                  Cancel
               </Button>
               <Button
                  variant='destructive'
                  onClick={handleRemove}
                  disabled={isSubmitting}
                  className='min-w-[100px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Removing...
                     </div>
                  ) : (
                     'Remove Product'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

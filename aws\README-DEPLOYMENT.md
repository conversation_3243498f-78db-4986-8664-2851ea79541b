# ECS Deployment Pipeline

This document describes the updated Bitbucket Pipeline for automatic ECS deployment with database migrations and seeding.

## 🚀 **Pipeline Overview**

The pipeline is configured to trigger on the `staging-server` branch and performs the following operations:

### **Phase 1: Build and Push (Parallel)**
- **API Service**: Builds and pushes to ECR with tags `$BITBUCKET_BUILD_NUMBER`, `latest`, `staging`
- **Kafka Consumer**: Builds and pushes to ECR with tags `$BITBUCKET_BUILD_NUMBER`, `latest`, `staging`
- **RabbitMQ Consumer**: Builds and pushes to ECR with tags `$BITBUCKET_BUILD_NUMBER`, `latest`, `staging`

### **Phase 2: Deploy and Migrate (Sequential)**
1. **Updates ECS Task Definitions** with new image tags using `$BITBUCKET_BUILD_NUMBER`
2. **Runs Database Migrations** using `pnpx prisma migrate deploy`
3. **Runs Database Seeding** using `pnpm run prisma:seed`
4. **Updates ECS Services** to use new task definitions
5. **Waits for deployment completion**

## 📁 **File Structure**

```
aws/
├── task-definitions/
│   ├── api-task-definition-template.json         # API service template
│   ├── kafka-consumer-task-definition-template.json  # Kafka consumer template
│   ├── rabbitmq-consumer-task-definition-template.json # RabbitMQ consumer template
│   └── migration-task-definition-template.json  # Migration/seeding template
└── README-DEPLOYMENT.md                         # This file
```

## ⚙️ **Key Features**

### **Database Migration & Seeding**
- Uses your existing API Docker image to run migrations
- Executes `pnpx prisma migrate deploy` for database schema updates
- Executes `pnpm run prisma:seed` for database seeding
- Runs as a one-time ECS task before service deployment
- Includes error handling and logging

### **ECS Task Definition Management**
- Template-based approach using `BUILD_NUMBER_PLACEHOLDER`
- Automatic image tag replacement with build numbers
- Maintains your existing task definition structure
- Uses your S3 environment file configuration

### **Safe Deployment**
- Checks if ECS services exist before attempting updates
- Waits for all services to reach stable state
- Comprehensive error handling with detailed logging
- Graceful handling of non-existent services

## 🔧 **Required Setup**

### **1. Bitbucket Repository Variables**
Set these in your Bitbucket repository settings:
- `AWS_ACCESS_KEY_ID`: AWS access key with ECS permissions
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key

### **2. AWS Resources Required**
- **ECS Cluster**: `tukxi-staging-cluster`
- **ECS Services** (optional, pipeline handles if they don't exist):
  - `api`
  - `kafka-consumer` 
  - `rabbitmq-consumer`
- **S3 Environment File**: `arn:aws:s3:::tuxi-staging-bucket/server-variables/.env`
- **CloudWatch Log Groups**: Created automatically by ECS
- **IAM Role**: `ecsTaskExecutionRole` with appropriate permissions

### **3. Environment Configuration**
Your `.env` file in S3 should contain all necessary database and service configurations needed for:
- Database connections (for migrations/seeding)
- Redis connections
- Kafka broker configurations
- RabbitMQ configurations
- Any other environment variables your applications need

## 🔄 **Deployment Process**

When you push to the `staging-server` branch:

1. **Build Phase**: All three services are built and pushed to ECR in parallel
2. **Task Definition Update**: Templates are processed with the new build number
3. **Migration Execution**: Database migrations and seeding run in a temporary ECS task
4. **Service Deployment**: ECS services are updated with new task definitions
5. **Verification**: Pipeline waits for all services to reach steady state

## 📊 **Monitoring and Debugging**

### **CloudWatch Logs**
Each service logs to its own CloudWatch log group:
- `/ecs/api` - API service logs
- `/ecs/kafka-consumer` - Kafka consumer logs
- `/ecs/rabbitmq-consumer` - RabbitMQ consumer logs
- `/ecs/api-migration` - Migration and seeding logs

### **Pipeline Output**
The pipeline provides detailed output including:
- Build numbers and image tags
- Migration and seeding status
- Service deployment status
- Error messages with log references

### **Troubleshooting**
- **Migration Failures**: Check `/ecs/api-migration` CloudWatch logs
- **Service Update Failures**: Verify ECS service exists and has correct permissions
- **Task Definition Failures**: Check JSON syntax and IAM permissions

## 🚨 **Important Notes**

1. **Database Safety**: Migrations run before service deployment to ensure schema compatibility
2. **Zero Downtime**: ECS rolling updates ensure continuous service availability
3. **Build Numbers**: Each deployment uses a unique build number for image tags
4. **Environment Variables**: All sensitive data is loaded from your S3 environment file
5. **Service Discovery**: Pipeline automatically detects which services exist

## 🔄 **Rollback Strategy**

If a deployment fails:
1. Check CloudWatch logs for error details
2. Use AWS Console to revert to previous task definition revision
3. Or trigger a new deployment with a known-good commit

## 🛠 **Customization**

To modify the deployment:
- **Add new services**: Create new task definition templates and add build/deploy steps
- **Change environments**: Update cluster names and environment file paths
- **Modify resources**: Adjust CPU/memory limits in task definition templates
- **Update commands**: Modify migration commands in the migration task definition template

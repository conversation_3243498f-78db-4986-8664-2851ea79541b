import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  CreateChargeGroupRequest,
  ChargeGroupResponse,
  UpdateChargeGroupRequest,
} from '../types/charge-group';

/**
 * Hook for creating a new charge group
 */
export const useCreateChargeGroup = () => {
  return useMutation({
    mutationFn: async (data: CreateChargeGroupRequest): Promise<ChargeGroupResponse> => {
      return apiClient.post('/charge-groups', data);
    },
  });
};

/**
 * Hook for updating a charge group
 */
export const useUpdateChargeGroup = () => {
  return useMutation({
    mutationFn: async (
      data: { id: string } & UpdateChargeGroupRequest
    ): Promise<ChargeGroupResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/charge-groups/${id}`, payload);
    },
  });
};

/**
 * Hook for deleting a charge group (soft delete)
 */
export const useDeleteChargeGroup = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ success: boolean; message: string; timestamp: number }> => {
      return apiClient.delete(`/charge-groups/${id}`);
    },
  });
};
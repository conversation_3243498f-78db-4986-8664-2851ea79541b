import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { RideDetailsResponse } from '../types/ride';

/**
 * Hook for canceling a ride by admin
 */
export const useCancelRide = () => {
  return useMutation({
    mutationFn: async (data: {
      rideId: string;
      reason?: string;
    }): Promise<RideDetailsResponse> => {
      return apiClient.post(`/rides/${data.rideId}/cancel/admin`, {
        reason: data.reason,
      });
    },
  });
};

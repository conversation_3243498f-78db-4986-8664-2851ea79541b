import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  ValidateNested,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  LocationPointDto,
  FavoriteLocationMetaDto,
} from './create-favorite-location.dto';

export class UpdateFavoriteLocationDto {
  @ApiProperty({
    description: 'Name of the favorite location',
    example: 'Home',
    minLength: 1,
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Description of the favorite location',
    example: 'My home address in Bangalore',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: 'Location coordinates and address',
    type: LocationPointDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => LocationPointDto)
  location?: LocationPointDto;

  @ApiProperty({
    description: 'Additional metadata for the favorite location',
    type: FavoriteLocationMetaDto,
    required: false,
    example: {
      address: '123 Main Street, Bangalore, Karnataka, India',
    },
  })
  @IsOptional()
  @IsObject()
  meta?: any;
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { Search, X } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { AddDriverProductsModal } from './add-driver-products-modal';
import { cn } from '@/lib/utils';

export interface DriverProductFiltersProps {
   onSearchChange: (search: string) => void;
   search: string;
   showAddButton?: boolean;
   vehicleId?: string;
   driverCityId?: string;
   driverCityName?: string;
}

export function DriverProductFilters({
   onSearchChange,
   search,
   isLoading,
   showAddButton = false,
   vehicleId,
   driverCityId,
   driverCityName,
}: DriverProductFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [isSearching, setIsSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear search
   const handleClearSearch = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }

      setIsSearching(false);
      setSearchValue('');
      onSearchChange('');
   };

   // Check if search is active
   const hasActiveSearch = !!search;

   return (
      <div
         className={`flex flex-col sm:flex-row p-4 bg-gray-50/50 border-b ${
            showAddButton ? 'flex-row justify-end gap-2' : 'gap-4'
         }`}
      >
         {/* Search Input */}
         <div className={cn('flex-1 relative', showAddButton && 'hidden')}>
            <div className='relative'>
               <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
               <Input
                  placeholder='Search by product name...'
                  value={searchValue}
                  onChange={handleSearchChange}
                  className='pl-10 pr-10'
               />
               {(isSearching || isLoading) && (
                  <div className='absolute right-8 top-1/2 transform -translate-y-1/2'>
                     <Spinner size='sm' />
                  </div>
               )}
               {searchValue && !isSearching && (
                  <button
                     onClick={handleClearSearch}
                     className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                  >
                     <X className='h-4 w-4' />
                  </button>
               )}
            </div>
         </div>

         {/* Action Buttons */}
         <div className='flex items-center gap-2'>
            {/* Clear Search Button */}
            {hasActiveSearch && (
               <Button
                  variant='outline'
                  size='sm'
                  onClick={handleClearSearch}
                  className={cn('flex items-center gap-2 whitespace-nowrap')}
               >
                  <X className='h-4 w-4' />
                  Clear Search
               </Button>
            )}

            {/* Add Products Button */}
            {showAddButton && vehicleId && driverCityId && driverCityName && (
               <AddDriverProductsModal
                  vehicleId={vehicleId}
                  driverCityId={driverCityId}
                  driverCityName={driverCityName}
               />
            )}
         </div>
      </div>
   );
}

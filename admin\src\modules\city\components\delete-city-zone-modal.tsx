'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';
import { useDeleteZone } from '../api/city-zone-mutations';
import { Zone } from '../types/city-zone';

interface DeleteCityZoneModalProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   zone: Zone | null;
}

export function DeleteCityZoneModal({ open, onOpenChange, zone }: DeleteCityZoneModalProps) {
   const deleteZoneMutation = useDeleteZone();

   const handleDelete = async () => {
      if (!zone) return;

      deleteZoneMutation.mutate(zone.id, {
         onSuccess: () => {
            onOpenChange(false);
         },
      });
   };

   if (!zone) return null;

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='sm:max-w-[400px]'>
            <DialogHeader>
               <div className='flex items-center gap-3'>
                  <div className='flex-shrink-0'>
                     <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
                        <AlertTriangle className='w-5 h-5 text-red-600' />
                     </div>
                  </div>
                  <div>
                     <DialogTitle>Delete Zone</DialogTitle>
                     <DialogDescription className='mt-1'>
                        This action cannot be undone.
                     </DialogDescription>
                  </div>
               </div>
            </DialogHeader>

            <div className='py-4'>
               <p className='text-sm text-gray-600 mb-4'>
                  Are you sure you want to delete the zone &quot;<strong>{zone.name}</strong>&quot;? 
                  This will permanently remove the zone and all its associated data.
               </p>

               {zone.polygon && zone.polygon.length > 0 && (
                  <div className='p-3 bg-amber-50 border border-amber-200 rounded-md'>
                     <p className='text-sm text-amber-800'>
                        <strong>Warning:</strong> This zone has polygon boundaries that will also be deleted.
                     </p>
                  </div>
               )}
            </div>

            <DialogFooter className='flex gap-2'>
               <Button
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                  disabled={deleteZoneMutation.isPending}
               >
                  Cancel
               </Button>
               <Button
                  variant='destructive'
                  onClick={handleDelete}
                  disabled={deleteZoneMutation.isPending}
               >
                  {deleteZoneMutation.isPending ? 'Deleting...' : 'Delete Zone'}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
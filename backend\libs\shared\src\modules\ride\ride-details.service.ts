import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { RideLifecycleRepository } from '@shared/shared/repositories/ride-lifecycle.repository';
// import { UserMetaDataService } from '../user-meta-data/user-meta-data.service';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import { ReviewRepository } from '@shared/shared/repositories';

export interface RideHistoryFilters {
  status?: string; // comma-separated statuses
  page?: number;
  limit?: number;
  fromDate?: Date;
  toDate?: Date;
}

export interface AdminRideHistoryFilters {
  status?: string;
  riderName?: string;
  driverName?: string;
  riderId?: string;
  driverId?: string;
  riderPhoneNumber?: string;
  driverPhoneNumber?: string;
  vehicleNumber?: string;
  productId?: string;
  page?: number;
  limit?: number;
  fromDate?: Date;
  toDate?: Date;
  createdByMe?: boolean;
  createdBy?: string;
}

export interface PaginatedRideHistory {
  rides: any[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class RideDetailsService {
  private readonly logger = new Logger(RideDetailsService.name);

  constructor(
    private readonly rideRepository: RideRepository,
    private readonly rideLifecycleRepository: RideLifecycleRepository,
    // private readonly userMetaDataService: UserMetaDataService,
    private readonly fileUploadService: FileUploadService,
    private readonly reviewRepository: ReviewRepository,
  ) {}

  /**
   * Get ride history for rider with pagination and filtering
   */
  async getRiderRideHistory(
    riderId: string,
    filters: RideHistoryFilters = {},
  ): Promise<PaginatedRideHistory> {
    const { status, page = 1, limit = 10, fromDate, toDate } = filters;

    if (fromDate && toDate && fromDate > toDate) {
      throw new BadRequestException('fromDate cannot be after toDate');
    }

    let statuses: RideStatus[] | undefined;
    if (status) {
      statuses = status.split(',').map((s) => s.trim() as RideStatus);
    }

    const result = await this.rideRepository.findRidesByRiderIdPaginated(
      riderId,
      statuses,
      page,
      limit,
      fromDate,
      toDate,
    );
    // console.log(result);
    // Process rides to include required details and S3 URLs
    const processedRides = await Promise.all(
      result.rides.map(async (ride) => {
        const processedRide = {
          id: ride.id,
          status: ride.status,
          pickupLocation: ride.pickupLocation,
          destinationLocation: ride.destinationLocation,
          stops: ride.stops,
          createdAt: ride.createdAt,
          completedAt: ride.completedAt,
          createdBy: ride.createdBy,
          duration: ride.duration,
          distance: ride.distance,
          riderMeta: ride.riderMeta,
          fareSpec: ride.fareSpec,
          actualDuration: ride.actualDuration,
          bookFor: ride.bookFor,
          pickupType: ride.pickupType,
          pickupTime: ride.pickupTime,
          otpVerifiedAt: ride.otpVerifiedAt,
          product: {
            name: ride.product?.name,
            icon: ride.product?.icon
              ? await this.convertToS3Url(ride.product.icon)
              : null,
            passengerLimit: ride.product?.passengerLimit ?? 0,
          },
          driver: ride.driver
            ? {
                id: ride.driver.id,
                firstName: ride.driver.firstName,
                lastName: ride.driver.lastName,
              }
            : null,
          driverVehicle: ride.driverVehicle
            ? {
                vehicleNumber: ride.driverVehicle.vehicleNumber,
                vehicleType: {
                  name: ride.driverVehicle.vehicleType?.name,
                },
              }
            : null,
        };

        return processedRide;
      }),
    );

    return {
      rides: processedRides,
      total: result.total,
      page,
      limit,
      totalPages: Math.ceil(result.total / limit),
    };
  }

  /**
   * Get ride history for driver with pagination and filtering
   */
  async getDriverRideHistory(
    driverId: string,
    filters: RideHistoryFilters = {},
  ): Promise<PaginatedRideHistory> {
    const { status, page = 1, limit = 10, fromDate, toDate } = filters;

    if (fromDate && toDate && fromDate > toDate) {
      throw new BadRequestException('fromDate cannot be after toDate');
    }

    // Parse status filter
    let statuses: RideStatus[] | undefined;
    if (status) {
      statuses = status.split(',').map((s) => s.trim() as RideStatus);
    }

    const result = await this.rideRepository.findRidesByDriverIdPaginated(
      driverId,
      statuses,
      page,
      limit,
      fromDate,
      toDate,
    );

    // Process rides to include required details and S3 URLs
    const processedRides = await Promise.all(
      result.rides.map(async (ride) => {
        const processedRide = {
          id: ride.id,
          status: ride.status,
          pickupLocation: ride.pickupLocation,
          destinationLocation: ride.destinationLocation,
          tripStartedAt: ride.otpVerifiedAt,
          stops: ride.stops,
          createdAt: ride.createdAt,
          completedAt: ride.completedAt,
          duration: ride.duration,
          distance: ride.distance,
          fareSpec: ride.fareSpec,
          actualDuration: ride.actualDuration,
          bookFor: ride.bookFor,
          pickupType: ride.pickupType,
          pickupTime: ride.pickupTime,
          riderMeta: ride.riderMeta,
          otpVerifiedAt: ride.otpVerifiedAt,
          product: {
            name: ride.product?.name,
            icon: ride.product?.icon
              ? await this.convertToS3Url(ride.product.icon)
              : null,
            passengerLimit: ride.product?.passengerLimit ?? 0,
          },
          rider: ride.rider
            ? {
                firstName: ride.rider.firstName,
                lastName: ride.rider.lastName,
              }
            : null,
        };

        return processedRide;
      }),
    );

    return {
      rides: processedRides,
      total: result.total,
      page,
      limit,
      totalPages: Math.ceil(result.total / limit),
    };
  }

  /**
   * Get detailed ride information for rider view
   */
  async getRideDetailsForRider(
    rideId: string,
    profileId: string,
  ): Promise<any> {
    const ride = await this.rideRepository.findRideByIdWithDetailsRider(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }
    return {
      id: ride.id,
      status: ride.status,
      pickupLocation: ride.pickupLocation,
      destinationLocation: ride.destinationLocation,
      stops: ride.stops,
      createdAt: ride.createdAt,
      completedAt: ride.completedAt,
      duration: ride.duration,
      actualDuration: ride.actualDuration,
      distance: ride.distance,
      rating: null,
      fareSpec: ride.fareSpec,
      bookFor: ride.bookFor,
      pickupType: ride.pickupType,
      pickupTime: ride.pickupTime,
      riderMeta: ride.riderMeta,
      otpVerifiedAt: ride.otpVerifiedAt,
      product: {
        name: ride.product?.name,
        icon: ride.product?.icon
          ? await this.convertToS3Url(ride.product.icon)
          : null,
        passengerLimit: ride.product?.passengerLimit ?? 0,
      },
      driver: ride.driver
        ? {
            id: ride.driver.id,
            firstName: ride.driver.firstName,
            lastName: ride.driver.lastName,
            profilePictureUrl: ride.driver.profilePictureUrl
              ? await this.convertToS3Url(ride.driver.profilePictureUrl)
              : null,
            metaData: ride?.driver?.metaData,
          }
        : null,
      driverVehicle: ride.driverVehicle
        ? {
            vehicleNumber: ride.driverVehicle.vehicleNumber,
            vehicleType: {
              name: ride.driverVehicle.vehicleType?.name,
            },
          }
        : null,
      rideLifecycles: ride.rideLifecycles
        ? ride.rideLifecycles.map((lifecycle) => ({
            id: lifecycle.id,
            status: lifecycle.status,
            location: lifecycle.location,
            createdAt: lifecycle.createdAt,
            meta: lifecycle.meta,
          }))
        : [],
      //if review.reviewBy==userProfile id then add a colum myReview =true
      reviews: ride.reviews
        ? ride.reviews.map((review) => ({
            id: review.id,
            rating: Number(review.rating),
            review: review.review,
            createdAt: review.createdAt,
            myReview: review.reviewById === profileId ? true : false,
          }))
        : [],
    };
  }

  /**
   * Get detailed ride information for driver view
   */
  async getRideDetailsForDriver(rideId: string): Promise<any> {
    const ride =
      await this.rideRepository.findRideByIdWithDriverDetails(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // const ratingObj =
    //   (await this.reviewRepository.findOne({
    //     where: { rideId: ride.id, reviewById: ride.riderId },
    //   })) || null;

    return {
      id: ride.id,
      status: ride.status,
      pickupLocation: ride.pickupLocation,
      destinationLocation: ride.destinationLocation,
      stops: ride.stops,
      rating: null,
      createdAt: ride.createdAt,
      actualDuration: ride.actualDuration,
      duration: ride.duration,
      distance: ride.distance,
      completedAt: ride.completedAt,
      fareSpec: ride.fareSpec,
      bookFor: ride.bookFor,
      pickupType: ride.pickupType,
      pickupTime: ride.pickupTime,
      riderMeta: ride.riderMeta,
      otpVerifiedAt: ride.otpVerifiedAt,
      product: {
        name: ride.product?.name,
        icon: ride.product?.icon
          ? await this.convertToS3Url(ride.product.icon)
          : null,
        passengerLimit: ride.product?.passengerLimit ?? 0,
      },
      rider: ride.rider
        ? {
            id: ride.rider.id,
            firstName: ride.rider.firstName,
            lastName: ride.rider.lastName,
            profilePictureUrl: ride.rider.profilePictureUrl
              ? await this.convertToS3Url(ride.rider.profilePictureUrl)
              : null,
          }
        : null,
      driverVehicle: ride.driverVehicle
        ? {
            vehicleNumber: ride.driverVehicle.vehicleNumber,
            vehicleType: {
              name: ride.driverVehicle.vehicleType?.name,
            },
          }
        : null,
      rideLifecycles: ride.rideLifecycles
        ? ride.rideLifecycles.map((lifecycle) => ({
            id: lifecycle.id,
            status: lifecycle.status,
            location: lifecycle.location,
            createdAt: lifecycle.createdAt,
            meta: lifecycle.meta,
          }))
        : [],
      reviews: ride.reviews
        ? ride.reviews.map((review) => ({
            id: review.id,
            rating: Number(review.rating),
            review: review.review,
            createdAt: review.createdAt,
            myReview: review.reviewById === ride.driverId ? true : false,
          }))
        : [],
    };
  }

  /**
   * Get ride lifecycle history
   */
  async getRideLifecycle(rideId: string): Promise<any[]> {
    const lifecycles =
      await this.rideLifecycleRepository.findLifecyclesByRideId(rideId);

    return lifecycles.map((lifecycle) => ({
      id: lifecycle.id,
      status: lifecycle.status,
      meta: lifecycle.meta,
      location: lifecycle.location,
      createdAt: lifecycle.createdAt,
    }));
  }

  /**
   * Get paginated ride history for admin with comprehensive filters
   */
  async getAdminRideHistory(
    filters: AdminRideHistoryFilters = {},
  ): Promise<PaginatedRideHistory> {
    const {
      status,
      riderName,
      driverName,
      riderId,
      driverId,
      productId,
      page = 1,
      limit = 10,
      fromDate,
      toDate,
      vehicleNumber,
      riderPhoneNumber,
      driverPhoneNumber,
      createdBy,
    } = filters;

    if (fromDate && toDate && fromDate > toDate) {
      throw new BadRequestException('fromDate cannot be after toDate');
    }

    const result = await this.rideRepository.findRidesForAdminPaginated({
      status: status || undefined,
      riderName: riderName || undefined,
      driverName: driverName || undefined,
      riderId: riderId || undefined,
      driverId: driverId || undefined,
      productId: productId || undefined,
      page,
      limit,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      vehicleNumber: vehicleNumber || undefined,
      riderPhoneNumber: riderPhoneNumber || undefined,
      driverPhoneNumber: driverPhoneNumber || undefined,
      createdBy: createdBy || undefined,
    });

    // Process rides to include required details and S3 URLs
    const processedRides = await Promise.all(
      result.rides.map(async (ride) => ({
        id: ride.id,
        status: ride.status,
        createdAt: ride.createdAt,
        completedAt: ride.completedAt,
        duration: ride.duration,
        distance: ride.distance,
        actualDuration: ride.actualDuration,
        pickupLocation: ride.pickupLocation,
        destinationLocation: ride.destinationLocation,
        bookFor: ride.bookFor,
        pickupType: ride.pickupType,
        pickupTime: ride.pickupTime,
        riderMeta: ride.riderMeta,
        otpVerifiedAt: ride.otpVerifiedAt,
        rider: {
          id: ride.rider.id,
          firstName: ride.rider.firstName,
          lastName: ride.rider.lastName,
        },
        driver: ride.driver
          ? {
              id: ride.driver.id,
              firstName: ride.driver.firstName,
              lastName: ride.driver.lastName,
            }
          : null,
        product: {
          id: ride.product.id,
          name: ride.product.name,
          icon: ride.product.icon
            ? await this.convertToS3Url(ride.product.icon)
            : null,
          passengerLimit: ride.product?.passengerLimit ?? 0,
        },
        createdByUser: {
          id: ride.createdByUser.id,
          firstName: ride.createdByUser.firstName,
          lastName: ride.createdByUser.lastName,
        },
      })),
    );

    return {
      rides: processedRides,
      total: result.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(result.total / Number(limit)),
    };
  }

  /**
   * Get detailed ride information for admin
   */
  async getAdminRideDetails(rideId: string): Promise<any> {
    const ride = await this.rideRepository.findRideByIdForAdmin(rideId);

    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Get ride lifecycles
    const lifecycles = await this.rideLifecycleRepository.findMany({
      where: { rideId },
      orderBy: { createdAt: 'asc' },
    });

    // Get reviews for this ride
    const reviews = (await this.reviewRepository.findMany({
      where: { rideId },
      include: {
        reviewBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })) as any[];

    // Process the ride data
    const processedRide = {
      id: ride.id,
      status: ride.status,
      pickupLocation: ride.pickupLocation,
      destinationLocation: ride.destinationLocation,
      stops: ride.stops,
      verificationCode: ride.verificationCode,
      createdAt: ride.createdAt,
      completedAt: ride.completedAt,
      otpVerifiedAt: ride.otpVerifiedAt,
      duration: ride.duration,
      actualDuration: ride.actualDuration,
      distance: ride.distance,
      fareSpec: ride.fareSpec,
      bookFor: ride.bookFor,
      pickupType: ride.pickupType,
      pickupTime: ride.pickupTime,
      riderMeta: ride.riderMeta,
      rider: {
        id: ride.rider.id,
        firstName: ride.rider.firstName,
        lastName: ride.rider.lastName,
        profilePictureUrl: ride.rider.profilePictureUrl
          ? await this.convertToS3Url(ride.rider.profilePictureUrl)
          : null,
        email: ride.rider.user?.email ?? null,
        phoneNumber: ride.rider.user?.phoneNumber ?? null,
      },
      driver: ride.driver
        ? {
            id: ride.driver.id,
            firstName: ride.driver.firstName,
            lastName: ride.driver.lastName,
            profilePictureUrl: ride.driver.profilePictureUrl
              ? await this.convertToS3Url(ride.driver.profilePictureUrl)
              : null,
            email: ride.driver.user?.email ?? null,
            phoneNumber: ride.driver.user?.phoneNumber ?? null,
          }
        : null,
      product: {
        id: ride.product.id,
        name: ride.product.name,
        description: ride.product.description,
        icon: ride.product.icon
          ? await this.convertToS3Url(ride.product.icon)
          : null,
        passengerLimit: ride.product?.passengerLimit ?? 0,
      },
      driverVehicle: ride.driverVehicle
        ? {
            id: ride.driverVehicle.id,
            vehicleNumber: ride.driverVehicle.vehicleNumber,
            vehicleType: {
              id: ride.driverVehicle.vehicleType.id,
              name: ride.driverVehicle.vehicleType.name,
            },
          }
        : null,
      rideLifecycles: lifecycles.map((lifecycle) => ({
        id: lifecycle.id,
        status: lifecycle.status,
        location: lifecycle.location,
        createdAt: lifecycle.createdAt,
        meta: lifecycle.meta,
      })),
      reviews: reviews.map((review) => ({
        id: review.id,
        rating: Number(review.rating),
        review: review.review,
        createdAt: review.createdAt,
        reviewBy: {
          id: review.reviewBy.id,
          firstName: review.reviewBy.firstName,
          lastName: review.reviewBy.lastName,
        },
      })),
      createdByUser: {
        id: ride.createdByUser.id,
        firstName: ride.createdByUser.firstName,
        lastName: ride.createdByUser.lastName,
      },
    };

    return processedRide;
  }

  /**
   * Convert URL to S3 signed URL if needed
   */
  private async convertToS3Url(url: string): Promise<string> {
    if (!url) return url;

    // If already a full URL, return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    try {
      // Convert to signed S3 URL
      return await this.fileUploadService.getSignedUrl(url, 3600); // 1 hour expiry
    } catch (error) {
      this.logger.warn(`Failed to convert URL to S3 signed URL: ${url}`, error);
      return url; // Return original URL if conversion fails
    }
  }
}

'use client';

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Spinner } from '@/components/ui/spinner';

interface DeleteRoleDialogProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
   isLoading: boolean;
}

export function DeleteRoleDialog({
   isOpen,
   onClose,
   onConfirm,
   isLoading,
}: DeleteRoleDialogProps) {
   return (
      <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <AlertDialogContent>
            <AlertDialogHeader>
               <AlertDialogTitle>Delete Role</AlertDialogTitle>
               <AlertDialogDescription>
                  Are you sure you want to delete this role? This action cannot be undone.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel onClick={onClose} disabled={isLoading}>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  onClick={onConfirm}
                  disabled={isLoading}
                  className='bg-red-600 hover:bg-red-700'
               >
                  {isLoading ? (
                     <>
                        <Spinner className='mr-2 h-4 w-4' />
                        Deleting...
                     </>
                  ) : (
                     'Delete'
                  )}
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
}
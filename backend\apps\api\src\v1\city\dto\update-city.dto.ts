import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LatLngDto } from './create-city.dto';

export enum CityStatusDto {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export class UpdateCityDto {
  @ApiProperty({ example: 'Kochi', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 'uploads/cities/kochi-icon.png',
    description: 'Icon file path for the city',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    example: 'Kerala',
    description: 'State where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    example: 'India',
    description: 'Country where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    example: [
      { lat: 9.9312, lng: 76.2673 },
      { lat: 9.9412, lng: 76.2773 },
      { lat: 9.9512, lng: 76.2873 },
      { lat: 9.9312, lng: 76.2673 },
    ],
    description:
      'Array of lat/lng coordinates defining the city polygon. Must be closed (first and last coordinates should be the same).',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LatLngDto)
  polygon?: LatLngDto[];

  @ApiProperty({
    example: 'active',
    enum: CityStatusDto,
    description: 'Status of the city',
    required: false,
  })
  @IsOptional()
  @IsEnum(CityStatusDto)
  status?: CityStatusDto;

  @ApiProperty({
    example: { colour: '#6668' },
    description: 'Additional metadata for the city as JSON object',
    required: false,
  })
  @IsOptional()
  @IsObject()
  meta?: Record<string, any>;
}

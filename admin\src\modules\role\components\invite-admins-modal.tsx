'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Sheet,
   SheetContent,
   SheetDescription,
   SheetFooter,
   SheetHeader,
   SheetTitle,
   SheetTrigger,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { UserPlus, X, Plus } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useListRole } from '../api/queries';
import { useAllCities } from '@/modules/city/api/queries';

const createEmailSchema = (existingEmails: string[]) =>
   z.object({
      firstName: z
         .string()
         .min(1, 'First name is required')
         .regex(/^[a-zA-Z]+$/, 'First name can only contain letters'),
      lastName: z
         .string()
         .min(1, 'Last name is required')
         .regex(/^[a-zA-Z]+$/, 'Last name can only contain letters'),
      email: z
         .string()
         .min(1, 'Email is required')
         .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address')
         .refine(email => !existingEmails.includes(email), 'This email has already been added'),
      roleId: z.string().min(1, 'Please select a role'),
      cityId: z.string().optional(),
   });

type EmailFormValues = z.infer<ReturnType<typeof createEmailSchema>>;

interface InviteMember {
   firstName: string;
   lastName: string;
   email: string;
   roleId: string;
   roleName: string;
   cityId?: string;
   cityName?: string;
}

export const InviteAdminsModal = () => {
   const [open, setOpen] = useState(false);
   const [members, setMembers] = useState<InviteMember[]>([]);
   const [addError, setAddError] = useState<string>('');
   const [inviteError, setInviteError] = useState<string>('');

   const rolesQuery = useListRole({ limit: 100 });
   const citiesQuery = useAllCities();

   // Get existing emails from members
   const existingEmails = useMemo(() => members.map(member => member.email), [members]);
   const emailSchema = useMemo(() => createEmailSchema(existingEmails), [existingEmails]);

   const form = useForm<EmailFormValues>({
      resolver: zodResolver(emailSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         email: '',
         roleId: '',
         cityId: '',
      },
   });

   const {
      formState: { errors },
      reset: resetForm,
      control,
      watch,
      setValue,
   } = form;

   const watchedFirstName = watch('firstName');
   const watchedLastName = watch('lastName');
   const watchedEmail = watch('email');
   const watchedRoleId = watch('roleId');
   const watchedCityId = watch('cityId');

   const roles = useMemo(() => rolesQuery.data?.data || [], [rolesQuery.data?.data]);
   const cities = useMemo(() => citiesQuery.data?.data || [], [citiesQuery.data?.data]);

   // Set default role when roles are loaded
   useEffect(() => {
      if (roles.length > 0 && !watchedRoleId) {
         setValue('roleId', roles[0].id);
      }
   }, [roles, watchedRoleId, setValue]);

   // Clear add error when user starts typing
   useEffect(() => {
      if (
         addError &&
         (watchedFirstName || watchedLastName || watchedEmail || watchedRoleId || watchedCityId)
      ) {
         setAddError('');
      }
   }, [watchedFirstName, watchedLastName, watchedEmail, watchedRoleId, watchedCityId, addError]);

   // reset all errors
   useEffect(() => {
      setAddError('');
      setInviteError('');
      form.clearErrors();
   }, [form, open]);

   const handleAddMemberValidation = (data: EmailFormValues) => {
      setAddError('');
      setInviteError(''); // Clear invite error when adding members

      const selectedRole = roles.find(role => role.id === data.roleId);
      if (!selectedRole) {
         setAddError('Please select a valid role');
         return;
      }

      // Check if city is required for city_admins
      if (selectedRole.name === 'city_admins' && !data.cityId) {
         setAddError('City is required for city admins');
         return;
      }

      const selectedCity = cities.find(city => city.id === data.cityId);

      const newMember: InviteMember = {
         firstName: data.firstName,
         lastName: data.lastName,
         email: data.email,
         roleId: data.roleId,
         roleName: selectedRole.name,
         cityId: data.cityId,
         cityName: selectedCity?.name,
      };

      setMembers(prev => [...prev, newMember]);
      resetForm();
      setAddError('');
      // Reset to first role again
      if (roles.length > 0) {
         setValue('roleId', roles[0].id);
      }
      setValue('cityId', '');
   };

   const handleAddMemberError = () => {
      setAddError('Please fix the validation errors above');
   };

   const handleRemoveMember = (index: number) => {
      setMembers(prev => prev.filter((_, i) => i !== index));
   };

   const handleInvite = () => {
      setInviteError('');

      if (members.length === 0) {
         setInviteError('Please add some emails you want to invite');
         return;
      }

      console.log(
         'Inviting members:',
         members.map(member => ({
            firstName: member.firstName,
            lastName: member.lastName,
            email: member.email,
            role: member.roleName,
            roleId: member.roleId,
            cityId: member.cityId,
            cityName: member.cityName,
         }))
      );

      setMembers([]);
      setInviteError('');
      setOpen(false);
   };

   const handleClose = () => {
      setOpen(false);
      setMembers([]);
      setAddError('');
      setInviteError('');
      resetForm();
      // Reset to first role again
      if (roles.length > 0) {
         setValue('roleId', roles[0].id);
      }
      setValue('cityId', '');
   };

   return (
      <Sheet open={open} onOpenChange={setOpen}>
         <SheetTrigger asChild>
            <Button className='cursor-pointer' variant='outline'>
               <UserPlus />
               Invite Admins
            </Button>
         </SheetTrigger>
         <SheetContent preventOutsideClickClose={true} className='w-[441px] sm:max-w-[441px]'>
            <SheetHeader className='pb-0'>
               <SheetTitle>Invite Admins</SheetTitle>
               <SheetDescription>
                  Fill in staff details and send them an invite to join as administrators
               </SheetDescription>
            </SheetHeader>

            <div className='flex flex-col h-full'>
               <ScrollArea
                  scrollThumbClassName='bg-gray-400'
                  type='always'
                  className='flex-1 px-6'
                  scrollViewPortClassName='h-[77vh]'
               >
                  <div className='space-y-6 pb-4'>
                     <div className='grid grid-cols-2 gap-4'>
                        <div>
                           <Label className='text-sm font-medium'>First Name</Label>
                           <Controller
                              control={control}
                              name='firstName'
                              render={({ field }) => (
                                 <Input placeholder='John' {...field} className='mt-1' />
                              )}
                           />
                           {errors.firstName && <ErrorMessage error={errors.firstName} />}
                        </div>
                        <div>
                           <Label className='text-sm font-medium'>Last Name</Label>
                           <Controller
                              control={control}
                              name='lastName'
                              render={({ field }) => (
                                 <Input placeholder='Doe' {...field} className='mt-1' />
                              )}
                           />
                           {errors.lastName && <ErrorMessage error={errors.lastName} />}
                        </div>
                     </div>

                     <div className='space-y-4'>
                        <div>
                           <Label className='text-sm font-medium'>Email address</Label>
                           <Controller
                              control={control}
                              name='email'
                              render={({ field }) => (
                                 <Input
                                    placeholder='<EMAIL>'
                                    {...field}
                                    className='mt-1'
                                 />
                              )}
                           />
                           {errors.email && <ErrorMessage error={errors.email} />}
                        </div>

                        <div>
                           <Label className='text-sm font-medium'>Type of Admin</Label>
                           <Controller
                              control={control}
                              name='roleId'
                              render={({ field }) => (
                                 <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger className='mt-1 w-full'>
                                       <SelectValue placeholder='Select Admin Type' />
                                    </SelectTrigger>
                                    <SelectContent>
                                       {roles.map(role => (
                                          <SelectItem key={role.id} value={role.id}>
                                             {role.name}
                                          </SelectItem>
                                       ))}
                                    </SelectContent>
                                 </Select>
                              )}
                           />
                           {errors.roleId && <ErrorMessage error={errors.roleId} />}
                        </div>

                        {watchedRoleId &&
                           roles.find(role => role.id === watchedRoleId)?.name ===
                              'city_admins' && (
                              <div>
                                 <Label className='text-sm font-medium'>City</Label>
                                 <Controller
                                    control={control}
                                    name='cityId'
                                    render={({ field }) => (
                                       <Select value={field.value} onValueChange={field.onChange}>
                                          <SelectTrigger className='mt-1 w-full'>
                                             <SelectValue placeholder='Select City' />
                                          </SelectTrigger>
                                          <SelectContent>
                                             {cities.map(city => (
                                                <SelectItem key={city.id} value={city.id}>
                                                   {city.name}
                                                </SelectItem>
                                             ))}
                                          </SelectContent>
                                       </Select>
                                    )}
                                 />
                                 {errors.cityId && <ErrorMessage error={errors.cityId} />}
                              </div>
                           )}
                     </div>

                     {/* Add Button */}
                     <div>
                        <Button
                           type='button'
                           onClick={form.handleSubmit(
                              handleAddMemberValidation,
                              handleAddMemberError
                           )}
                           variant='outline'
                           className='w-full'
                        >
                           <Plus className='h-4 w-4 mr-2' />
                           Add Member
                        </Button>
                     </div>

                     {addError && (
                        <div className='text-sm text-red-600 p-3 bg-red-50 rounded-md border border-red-200'>
                           {addError}
                        </div>
                     )}

                     {/* Members List Section */}
                     <div className='mt-6'>
                        {members.length > 0 && (
                           <div>
                              <div className='mb-3'>
                                 <Label className='text-sm font-medium text-gray-600'>
                                    Invited Members ({members.length})
                                 </Label>
                              </div>
                              <div className='space-y-2'>
                                 {members.map((member, index) => (
                                    <div
                                       key={index}
                                       className='flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 transition-colors'
                                    >
                                       <div className='flex items-center gap-3'>
                                          <div className='w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center'>
                                             <span className='text-xs font-semibold text-orange-600'>
                                                {member.firstName.charAt(0).toUpperCase()}
                                                {member.lastName.charAt(0).toUpperCase()}
                                             </span>
                                          </div>
                                          <div>
                                             <div className='flex items-center gap-2 mb-1'>
                                                <p className='text-sm font-medium text-gray-900'>
                                                   {member.firstName} {member.lastName}
                                                </p>
                                                <span className='inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                                                   {member.roleName}
                                                </span>
                                                {member.cityName && (
                                                   <span className='inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'>
                                                      {member.cityName}
                                                   </span>
                                                )}
                                             </div>
                                             <p className='text-xs text-gray-600'>{member.email}</p>
                                          </div>
                                       </div>
                                       <Button
                                          variant='ghost'
                                          size='icon'
                                          onClick={() => handleRemoveMember(index)}
                                          className='h-6 w-6 text-gray-400 hover:text-red-500'
                                       >
                                          <X className='h-3 w-3' />
                                       </Button>
                                    </div>
                                 ))}
                              </div>
                           </div>
                        )}

                        {members.length === 0 && (
                           <div className='flex items-center justify-center py-12'>
                              <div className='text-center'>
                                 <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                                    <UserPlus className='h-8 w-8 text-gray-400' />
                                 </div>
                                 <p className='text-gray-500 text-sm'>No members added yet</p>
                                 <p className='text-gray-400 text-xs mt-1'>
                                    Fill out the form above and click "Add Member" to get started
                                 </p>
                              </div>
                           </div>
                        )}
                     </div>
                  </div>
               </ScrollArea>

               {inviteError && (
                  <div className='px-6 pb-2'>
                     <div className='text-sm text-red-600 p-3 bg-red-50 rounded-md border border-red-200'>
                        {inviteError}
                     </div>
                  </div>
               )}
            </div>

            <SheetFooter className='border-t pt-4 px-6'>
               <div className='flex gap-3 w-full'>
                  <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                     Cancel
                  </Button>
                  <Button
                     type='button'
                     onClick={handleInvite}
                     disabled={members.length === 0}
                     className='flex-1'
                  >
                     Send {members.length > 0 ? `${members.length} ` : ''}Invite
                     {members.length !== 1 ? 's' : ''}
                  </Button>
               </div>
            </SheetFooter>
         </SheetContent>
      </Sheet>
   );
};

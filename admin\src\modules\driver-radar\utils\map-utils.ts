import { Coordinate, RadarMapBounds, MAP_CONSTANTS, STATUS_COLORS, DriverStatus } from '../types/radar';

/**
 * Calculate bounds for a given center point and radius in kilometers
 * @param center - Center coordinate (lat, lng)
 * @param radiusKm - Radius in kilometers
 * @returns Bounds object with northeast and southwest coordinates
 */
export function calculateBoundsFromCenter(center: Coordinate, radiusKm: number): RadarMapBounds {
  // Earth's radius in kilometers
  const EARTH_RADIUS_KM = 6371;

  // Convert radius to degrees
  const latDelta = (radiusKm / EARTH_RADIUS_KM) * (180 / Math.PI);
  const lngDelta = (radiusKm / (EARTH_RADIUS_KM * Math.cos((center.lat * Math.PI) / 180))) * (180 / Math.PI);

  return {
    northeast: {
      lat: center.lat + latDelta,
      lng: center.lng + lngDelta,
    },
    southwest: {
      lat: center.lat - latDelta,
      lng: center.lng - lngDelta,
    },
  };
}

/**
 * Extract bounds from Google Maps instance
 * @param map - Google Maps instance
 * @returns Bounds object or null if map not loaded
 */
export function getBoundsFromMap(map: google.maps.Map | null): RadarMapBounds | null {
  if (!map) return null;

  const bounds = map.getBounds();
  if (!bounds) return null;

  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();

  return {
    northeast: { lat: ne.lat(), lng: ne.lng() },
    southwest: { lat: sw.lat(), lng: sw.lng() },
  };
}

/**
 * Get center coordinate from Google Maps instance
 * @param map - Google Maps instance
 * @returns Center coordinate or null if map not loaded
 */
export function getCenterFromMap(map: google.maps.Map | null): Coordinate | null {
  if (!map) return null;

  const center = map.getCenter();
  if (!center) return null;

  return {
    lat: center.lat(),
    lng: center.lng(),
  };
}

/**
 * Calculate approximate area of bounds in square kilometers
 * @param bounds - Map bounds
 * @returns Approximate area in km²
 */
export function calculateBoundsArea(bounds: RadarMapBounds): number {
  const latDiff = bounds.northeast.lat - bounds.southwest.lat;
  const lngDiff = bounds.northeast.lng - bounds.southwest.lng;

  // Approximate conversion: 1 degree ≈ 111 km
  const area = latDiff * lngDiff * 111 * 111;

  return Math.abs(area);
}

/**
 * Check if bounds are within maximum allowed area
 * @param bounds - Map bounds
 * @param maxRadiusKm - Maximum allowed radius in kilometers
 * @returns True if within limits, false otherwise
 */
export function isWithinMaxArea(bounds: RadarMapBounds, maxRadiusKm: number = MAP_CONSTANTS.MAX_RADIUS_KM): boolean {
  const area = calculateBoundsArea(bounds);
  const maxArea = maxRadiusKm * maxRadiusKm * Math.PI; // Approximate circle area

  return area <= maxArea;
}

/**
 * Get product initials from product name
 * Fallback when product icon is not available
 * @param productName - Full product name
 * @returns Product initials (max 2 characters)
 */
export function getProductInitials(productName: string): string {
  if (!productName) return '?';

  const words = productName.trim().split(/\s+/);

  if (words.length === 1) {
    // Single word: take first letter
    return words[0].charAt(0).toUpperCase();
  }

  // Multiple words: take first letter of first two words
  return words
    .slice(0, 2)
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}

/**
 * Get color code for driver status
 * @param status - Driver status
 * @returns Hex color code
 */
export function getStatusColor(status: DriverStatus): string {
  return STATUS_COLORS[status] || STATUS_COLORS.offline;
}

/**
 * Format timestamp to relative time
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted relative time string
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  const seconds = Math.floor(diff / 1000);

  if (seconds < 10) return 'Just now';
  if (seconds < 60) return `${seconds}s ago`;

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) return `${minutes}m ago`;

  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h ago`;

  return 'More than a day ago';
}

/**
 * Format countdown timer
 * @param seconds - Remaining seconds
 * @returns Formatted countdown string
 */
export function formatCountdown(seconds: number): string {
  if (seconds <= 0) return '0s';
  return `${seconds}s`;
}

/**
 * Calculate distance between two coordinates in kilometers
 * Uses Haversine formula
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in kilometers
 */
export function calculateDistance(coord1: Coordinate, coord2: Coordinate): number {
  const EARTH_RADIUS_KM = 6371;

  const lat1Rad = (coord1.lat * Math.PI) / 180;
  const lat2Rad = (coord2.lat * Math.PI) / 180;
  const deltaLat = ((coord2.lat - coord1.lat) * Math.PI) / 180;
  const deltaLng = ((coord2.lng - coord1.lng) * Math.PI) / 180;

  const a =
    Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return EARTH_RADIUS_KM * c;
}

/**
 * Check if a coordinate is within bounds
 * @param coord - Coordinate to check
 * @param bounds - Bounds to check against
 * @returns True if coordinate is within bounds
 */
export function isCoordinateInBounds(coord: Coordinate, bounds: RadarMapBounds): boolean {
  return (
    coord.lat >= bounds.southwest.lat &&
    coord.lat <= bounds.northeast.lat &&
    coord.lng >= bounds.southwest.lng &&
    coord.lng <= bounds.northeast.lng
  );
}

model RideMeterLog {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  rideId      String    @map("ride_id") @db.Uuid
  rideMeterId String    @map("ride_meter_id") @db.Uuid
  name        String    @map("name")
  fromTime    DateTime  @default(now()) @map("from_time") // The measured value
  toTime      DateTime? @map("to_time") @db.Timestamptz

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  ride      Ride      @relation(fields: [rideId], references: [id], onDelete: Cascade)
  rideMeter RideMeter @relation(fields: [rideMeterId], references: [id], onDelete: Cascade)

  //index
  @@index([rideId], name: "idx_ride_meter_log_ride_id")
  @@index([rideMeterId], name: "idx_ride_meter_log_ride_meter_id")
  @@map("ride_meter_logs")
}

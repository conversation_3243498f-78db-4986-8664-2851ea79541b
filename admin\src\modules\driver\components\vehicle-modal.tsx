'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useEffect } from 'react';
import { Car } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useVehicleTypes } from '../api/queries';
import {
   useCreateDriverVehicle,
   useVerifyDriverVehicle,
   useUpdateDriverVehicle,
} from '../api/mutations';
import {
   CreateDriverVehicleRequest,
   UpdateDriverVehicleRequest,
   DriverVehicle,
} from '../types/driver';
import { toast } from 'sonner';
import { queryClient } from '@/lib/react-query';

const vehicleSchema = z.object({
   vehicleTypeId: z.string().min(1, 'Vehicle type is required'),
   vehicleNumber: z
      .string()
      .min(1, 'Vehicle number is required')
      .regex(
         /^[A-Z]{2}\d{1,2}[A-Z]{1,3}\d{4}$/,
         'Invalid vehicle number format. Use format like: MH12AB1234'
      ),
   isPrimary: z.boolean(),
});

type VehicleForm = z.infer<typeof vehicleSchema>;

interface VehicleModalProps {
   isOpen: boolean;
   onClose: () => void;
   profileId: string;
   vehicle?: DriverVehicle | null; // null or undefined for create mode, DriverVehicle for edit mode
   onVehicleCreated?: (vehicleId: string) => void;
   onVehicleUpdated?: (shouldShowDocuments?: boolean) => void;
}

export function VehicleModal({
   isOpen,
   onClose,
   profileId,
   vehicle,
   onVehicleCreated,
   onVehicleUpdated,
}: VehicleModalProps) {
   const isEditMode = !!vehicle;
   const isCreateMode = !vehicle;

   // Determine if we can edit all fields or just primary status
   // Note: According to the DriverVehicle type, status can be 'pending' | 'active' | 'inactive'
   // Vehicle number is editable for pending/inactive, read-only for active
   const canEditVehicleNumber =
      isCreateMode || (isEditMode && vehicle && vehicle.status !== 'active');
   const canOnlyEditPrimary = isEditMode && vehicle && vehicle.status === 'active';

   const { data: vehicleTypesResponse } = useVehicleTypes();
   const vehicleTypes = vehicleTypesResponse?.data || [];

   const createVehicleMutation = useCreateDriverVehicle();
   const verifyVehicleMutation = useVerifyDriverVehicle();
   const updateVehicleMutation = useUpdateDriverVehicle();

   const {
      register,
      handleSubmit,
      formState: { errors },
      setValue,
      watch,
      reset,
   } = useForm<VehicleForm>({
      resolver: zodResolver(vehicleSchema),
      defaultValues: {
         vehicleTypeId: '',
         vehicleNumber: '',
         isPrimary: false,
      },
   });

   const selectedVehicleTypeId = watch('vehicleTypeId');
   const vehicleNumber = watch('vehicleNumber');
   const isPrimary = watch('isPrimary');

   // Check if vehicle number changed (for edit mode)
   const vehicleNumberChanged = isEditMode && vehicle && vehicleNumber !== vehicle.vehicleNumber;

   const isLoading =
      createVehicleMutation.isPending ||
      verifyVehicleMutation.isPending ||
      updateVehicleMutation.isPending;

   // Reset form when modal opens or vehicle changes
   useEffect(() => {
      if (isOpen) {
         if (isEditMode && vehicle) {
            // Pre-fill form with vehicle data in edit mode
            setValue('vehicleTypeId', vehicle.vehicleTypeId);
            setValue('vehicleNumber', vehicle.vehicleNumber);
            setValue('isPrimary', vehicle.isPrimary);
         } else {
            // Reset form in create mode
            reset({
               vehicleTypeId: '',
               vehicleNumber: '',
               isPrimary: false,
            });
         }
      }
   }, [isOpen, isEditMode, vehicle, setValue, reset]);

   const onSubmit = (data: VehicleForm) => {
      if (isCreateMode) {
         // Create new vehicle
         const payload: CreateDriverVehicleRequest = {
            profileId,
            vehicleTypeId: data.vehicleTypeId,
            vehicleNumber: data.vehicleNumber.toUpperCase().trim(),
            isPrimary: data.isPrimary,
         };

         createVehicleMutation.mutate(payload, {
            onSuccess: response => {
               if (response.data?.id) {
                  verifyVehicleMutation.mutate(response.data.id, {
                     onSuccess: () => {
                        toast.success('Vehicle added and verified successfully');
                        reset();
                        onClose();
                        if (onVehicleCreated) {
                           onVehicleCreated(response.data.id);
                        }
                     },
                     onSettled: () => {
                        if (!verifyVehicleMutation.isSuccess) {
                           toast.success('Vehicle added successfully');
                           reset();
                           onClose();
                           if (onVehicleCreated) {
                              onVehicleCreated(response.data.id);
                           }
                        }
                     },
                  });
               } else {
                  toast.success('Vehicle added successfully');
                  reset();
                  onClose();
               }
            },
         });
      } else if (isEditMode && vehicle) {
         // Update existing vehicle
         const payload: UpdateDriverVehicleRequest = {};

         // Only include changed fields
         if (data.vehicleTypeId !== vehicle.vehicleTypeId) {
            payload.vehicleTypeId = data.vehicleTypeId;
         }

         if (canEditVehicleNumber && data.vehicleNumber !== vehicle.vehicleNumber) {
            payload.vehicleNumber = data.vehicleNumber.toUpperCase().trim();
         }

         if (data.isPrimary !== vehicle.isPrimary) {
            payload.isPrimary = data.isPrimary;
         }

         // If no changes were made, just close the modal
         if (Object.keys(payload).length === 0) {
            onClose();
            return;
         }

         updateVehicleMutation.mutate(
            { vehicleId: vehicle.id, ...payload },
            {
               onSuccess: () => {
                  // If vehicle number changed, trigger verification to show documents
                  if (vehicleNumberChanged) {
                     verifyVehicleMutation.mutate(vehicle.id, {
                        onSuccess: () => {
                           queryClient.invalidateQueries({
                              queryKey: ['vehicle-documents', vehicle.id],
                           });
                           toast.success('Vehicle updated and re-verified successfully');
                           reset();
                           onClose();
                           if (onVehicleUpdated) onVehicleUpdated(true);
                        },
                        onSettled: () => {
                           reset();
                           onClose();
                           if (onVehicleUpdated) onVehicleUpdated(false);
                        },
                     });
                  } else {
                     toast.success('Vehicle updated successfully');
                     reset();
                     onClose();
                     if (onVehicleUpdated) onVehicleUpdated(false);
                  }
               },
            }
         );
      }
   };

   const handleClose = () => {
      reset();
      onClose();
   };

   // Check if form has changes
   const hasChanges =
      isCreateMode ||
      (isEditMode &&
         vehicle &&
         (selectedVehicleTypeId !== vehicle.vehicleTypeId ||
            (canEditVehicleNumber && vehicleNumber !== vehicle.vehicleNumber) ||
            isPrimary !== vehicle.isPrimary));

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='sm:max-w-md'
         >
            <DialogHeader>
               <DialogTitle className='flex items-center gap-2'>
                  <Car className='w-5 h-5' />
                  {isCreateMode ? 'Add New Vehicle' : `Edit Vehicle - ${vehicle?.vehicleNumber}`}
               </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
               {/* Vehicle Type Selection */}
               <div className='space-y-2'>
                  <Label htmlFor='vehicleTypeId'>
                     Vehicle Type *
                     {canOnlyEditPrimary && (
                        <span className='text-xs text-gray-500 ml-2'>
                           (Read-only - vehicle is active)
                        </span>
                     )}
                  </Label>
                  <Select
                     value={selectedVehicleTypeId}
                     onValueChange={value => setValue('vehicleTypeId', value)}
                     disabled={canOnlyEditPrimary}
                  >
                     <SelectTrigger className={`w-full ${canOnlyEditPrimary ? 'bg-gray-100' : ''}`}>
                        <SelectValue placeholder='Select vehicle type'>
                           {selectedVehicleTypeId &&
                              (() => {
                                 const selectedType = vehicleTypes.find(
                                    type => type.id === selectedVehicleTypeId
                                 );
                                 return selectedType
                                    ? `${selectedType.name}${
                                         selectedType.description
                                            ? ` (${selectedType.description})`
                                            : ''
                                      }`
                                    : 'Select vehicle type';
                              })()}
                        </SelectValue>
                     </SelectTrigger>
                     <SelectContent>
                        {vehicleTypes.map(type => (
                           <SelectItem key={type.id} value={type.id}>
                              <div>
                                 <div className='font-medium'>{type.name}</div>
                                 {type.description && (
                                    <div className='text-sm text-gray-500'>{type.description}</div>
                                 )}
                              </div>
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
                  {errors.vehicleTypeId && (
                     <p className='text-sm text-red-600'>{errors.vehicleTypeId.message}</p>
                  )}
               </div>

               {/* Vehicle Number */}
               <div className='space-y-2'>
                  <Label htmlFor='vehicleNumber'>
                     Vehicle Number *
                     {!canEditVehicleNumber && (
                        <span className='text-xs text-gray-500 ml-2'>
                           (Read-only - active vehicle)
                        </span>
                     )}
                  </Label>
                  <Input
                     id='vehicleNumber'
                     {...register('vehicleNumber')}
                     placeholder='e.g., MH12AB1234'
                     className={`uppercase ${!canEditVehicleNumber ? 'bg-gray-100' : ''}`}
                     disabled={!canEditVehicleNumber}
                     onChange={e => {
                        if (canEditVehicleNumber) {
                           const value = e.target.value.toUpperCase();
                           setValue('vehicleNumber', value);
                        }
                     }}
                  />
                  {errors.vehicleNumber && (
                     <p className='text-sm text-red-600'>{errors.vehicleNumber.message}</p>
                  )}
                  {canEditVehicleNumber && (
                     <p className='text-xs text-gray-500'>
                        Format: State code (2 letters) + RTO code (1-2 digits) + Series (1-3
                        letters) + Number (4 digits)
                     </p>
                  )}
                  {vehicleNumberChanged && (
                     <p className='text-xs text-blue-600'>
                        ⚠️ Changing vehicle number will trigger document re-verification
                     </p>
                  )}
               </div>

               {/* Primary Vehicle Checkbox */}
               <div className='flex items-center space-x-2'>
                  <Checkbox
                     id='isPrimary'
                     checked={isPrimary}
                     onCheckedChange={checked => setValue('isPrimary', !!checked)}
                  />
                  <Label htmlFor='isPrimary' className='text-sm'>
                     Set as primary vehicle
                  </Label>
               </div>

               {/* Conditional message for active vehicles */}
               {canOnlyEditPrimary && (
                  <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
                     <p className='text-sm text-blue-800'>
                        ℹ️ This vehicle is active. Only vehicle type and primary status can be
                        changed. Vehicle number is read-only for active vehicles.
                     </p>
                  </div>
               )}

               {/* Conditional message for pending/inactive vehicles */}
               {isEditMode && !canOnlyEditPrimary && (
                  <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-3'>
                     <p className='text-sm text-yellow-800'>
                        ℹ️ This vehicle is {vehicle?.status}. You can edit vehicle type, vehicle
                        number, and primary status. Changing the vehicle number will trigger
                        document re-verification.
                     </p>
                  </div>
               )}

               {/* Action Buttons */}
               <div className='flex justify-end space-x-2 pt-4'>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={handleClose}
                     disabled={isLoading}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={isLoading || !selectedVehicleTypeId || (isEditMode && !hasChanges)}
                     className='min-w-[100px]'
                  >
                     {isLoading
                        ? isCreateMode
                           ? 'Adding...'
                           : 'Updating...'
                        : isCreateMode
                        ? 'Add Vehicle'
                        : 'Update Vehicle'}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
}

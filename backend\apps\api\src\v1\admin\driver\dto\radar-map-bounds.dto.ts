import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, IsNotEmpty, ValidateNested } from 'class-validator';

export class CoordinateDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
  })
  @IsNotEmpty()
  @IsNumber()
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
  })
  @IsNotEmpty()
  @IsNumber()
  lng!: number;
}

export class RadarMapBoundsDto {
  @ApiProperty({
    description: 'Northeast corner of the bounding box (top-right)',
    type: CoordinateDto,
    example: { lat: 12.9816, lng: 77.6046 },
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CoordinateDto)
  northeast!: CoordinateDto;

  @ApiProperty({
    description: 'Southwest corner of the bounding box (bottom-left)',
    type: CoordinateDto,
    example: { lat: 12.9616, lng: 77.5846 },
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CoordinateDto)
  southwest!: CoordinateDto;
}

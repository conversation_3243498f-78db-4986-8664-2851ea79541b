'use client';

import { CustomPagination } from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { Trash2, Package, CircleAlert } from 'lucide-react';
import Image from 'next/image';
import { DriverProduct, ListVehicleProductResponse } from '../types/driver-product';
import { DriverProductTableEmpty } from './driver-product-table-empty';
import { DriverProductTableLoading } from './driver-product-table-loading';
import { ErrorBoundary } from 'react-error-boundary';

interface DriverProductTableProps {
   data?: ListVehicleProductResponse;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   onRemoveClick: (driverProduct: DriverProduct) => void;
   onMakePrimary?: (driverProduct: DriverProduct) => void;
}

const getColumns = ({
   onRemoveClick,
}: // onMakePrimary,
{
   onRemoveClick: (driverProduct: DriverProduct) => void;
   onMakePrimary?: (driverProduct: DriverProduct) => void;
}): ColumnDef<DriverProduct>[] => [
   {
      accessorKey: 'product',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Product</div>,
      cell: ({ row }) => {
         const driverProduct = row.original as DriverProduct;
         const product = driverProduct.cityProduct?.product;
         return (
            <div className='flex items-center gap-3'>
               {product?.icon ? (
                  <ErrorBoundary fallback={<CircleAlert className='scale-75 text-red-500' />}>
                     <div className='w-10 h-10 relative rounded-lg overflow-hidden bg-gray-100 flex-shrink-0'>
                        <Image
                           src={product.icon}
                           alt={product?.name || 'Product'}
                           fill
                           className='object-cover'
                           sizes='40px'
                        />
                     </div>
                  </ErrorBoundary>
               ) : (
                  <div className='w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0'>
                     <Package className='w-5 h-5 text-gray-400' />
                  </div>
               )}
               <div className='min-w-0 flex-1'>
                  <div className='font-medium text-sm text-gray-900 truncate'>
                     {product?.name || 'Unknown Product'}
                  </div>
                  {product?.description && (
                     <div className='text-xs text-gray-500 truncate'>{product.description}</div>
                  )}
               </div>
            </div>
         );
      },
      size: 300,
   },
   {
      accessorKey: 'city',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
      cell: ({ row }) => {
         const driverProduct = row.original as DriverProduct;
         const city = driverProduct.cityProduct?.city;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-900'>{city?.name || 'Unknown City'}</div>
               {city?.state && <div className='text-xs text-gray-500'>{city.state}</div>}
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'vehicleType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Vehicle Type</div>
      ),
      cell: ({ row }) => {
         const driverProduct = row.original as DriverProduct;
         const vehicleType = driverProduct.cityProduct?.vehicleType;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{vehicleType?.name || 'Unknown Type'}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const driverProduct = row.original as DriverProduct;
         return (
            <Badge
               variant='secondary'
               className={`text-xs ${
                  driverProduct.cityProduct?.isEnabled
                     ? 'bg-green-100 text-green-700'
                     : 'bg-red-100 text-red-700'
               }`}
            >
               {driverProduct.cityProduct?.isEnabled ? 'Active' : 'Inactive'}
            </Badge>
         );
      },
      size: 120,
   },
   // {
   //    accessorKey: 'primary',
   //    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Primary</div>,
   //    cell: ({ row }) => {
   //       const driverProduct = row.original as DriverProduct;
   //       return (
   //          <div className='flex items-center'>
   //             {driverProduct.isPrimary ? (
   //                <Badge variant='secondary' className='bg-blue-100 text-blue-700 text-xs'>
   //                   Primary
   //                </Badge>
   //             ) : onMakePrimary ? (
   //                <Button
   //                   variant='outline'
   //                   size='sm'
   //                   onClick={() => onMakePrimary(driverProduct)}
   //                   className='text-blue-600 hover:text-blue-700 text-xs px-2 py-1'
   //                >
   //                   Make Primary
   //                </Button>
   //             ) : null}
   //          </div>
   //       );
   //    },
   //    size: 120,
   // },
   {
      accessorKey: 'actions',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const driverProduct = row.original as DriverProduct;
         return (
            <div className='flex items-center justify-end'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onRemoveClick(driverProduct)}
                  className='text-red-600 hover:text-red-700'
               >
                  <Trash2 className='w-3 h-3' />
               </Button>
            </div>
         );
      },
      size: 100,
   },
];

export function DriverProductTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   onRemoveClick,
   onMakePrimary,
}: DriverProductTableProps) {
   const columns = getColumns({
      onRemoveClick,
      onMakePrimary: onMakePrimary,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <DriverProductTableLoading />;
   }

   if (!data?.data?.length) {
      return <DriverProductTableEmpty hasFilters={hasFilters} />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPreviousPage}
            />
         )}
      </div>
   );
}

import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty } from 'class-validator';

export class AttachTaxGroupDto {
  @ApiProperty({
    description: 'ID of the charge to attach tax group to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  chargeId!: string;

  @ApiProperty({
    description: 'ID of the tax group to attach',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @IsUUID()
  @IsNotEmpty()
  taxGroupId!: string;
}

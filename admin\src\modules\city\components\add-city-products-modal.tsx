'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Spinner } from '@/components/ui/spinner';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useAvailableProducts, useListCityProducts } from '../api/city-product-queries';
import { useAddProductsToCity } from '../api/city-product-mutations';

interface AddCityProductsModalProps {
   cityId: string;
}

export function AddCityProductsModal({ cityId }: AddCityProductsModalProps) {
   const [open, setOpen] = useState(false);
   const [selectedOptions, setSelectedOptions] = useState<Option[]>([]);

   const availableProductsQuery = useAvailableProducts({
      page: 1,
      limit: 100,
      isEnabled: 'true',
   });

   // Get current city products to filter out already assigned ones
   const cityProductsQuery = useListCityProducts(cityId, {
      page: 1,
      limit: 100,
   });

   const addProductsMutation = useAddProductsToCity(cityId);

   // Get currently assigned product-vehicleType combinations for this city
   const assignedProductVehicleCombos = new Set(
      cityProductsQuery.data?.data?.map(
         cityProduct => `${cityProduct.productId}-${cityProduct.vehicleTypeId}`
      ) || []
   );

   // Convert Product to MultipleSelector Option format, filtering out already assigned ones
   const productOptions: Option[] = (availableProductsQuery.data?.data || [])
      .filter(
         product => !assignedProductVehicleCombos.has(`${product.id}-${product.vehicleTypeId}`)
      )
      .map(product => ({
         value: `${product.id}-${product.vehicleTypeId}`,
         label: `${product.name} (${product.vehicleType?.name || 'Unknown'})`,
         productId: product.id,
         vehicleTypeId: product.vehicleTypeId,
      }));

   const handleSubmit = () => {
      if (selectedOptions.length === 0) return;

      const products = selectedOptions.map((option: any) => ({
         productId: option.productId,
         vehicleTypeId: option.vehicleTypeId,
      }));

      addProductsMutation.mutate(
         { products },
         {
            onSuccess: () => {
               setOpen(false);
               setSelectedOptions([]);
            },
         }
      );
   };

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         setSelectedOptions([]);
      }
   };

   const isLoading = availableProductsQuery.isLoading || cityProductsQuery.isLoading;
   const isSubmitting = addProductsMutation.isPending;
   const hasAvailableProducts = productOptions.length > 0;

   return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
         <DialogTrigger asChild>
            <Button variant={'outline'} className='flex items-center gap-2'>
               <Plus className='w-4 h-4' />
               Add Products
            </Button>
         </DialogTrigger>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Add Products to City</DialogTitle>
               <DialogDescription>
                  Select multiple products to add to this city. Each product will be associated with
                  its respective vehicle type.
               </DialogDescription>
            </DialogHeader>

            <div className='py-4'>
               {isLoading ? (
                  <div className='flex items-center justify-center py-8'>
                     <Spinner className='mr-2' />
                     <span className='text-sm text-gray-600'>Loading available products...</span>
                  </div>
               ) : (
                  <div className='space-y-2'>
                     <Label htmlFor='products'>Select Products</Label>
                     <MultipleSelector
                        value={selectedOptions}
                        onChange={setSelectedOptions}
                        defaultOptions={productOptions}
                        placeholder='Select products to add...'
                        emptyIndicator={
                           <p className='text-center text-sm text-gray-500'>No products found</p>
                        }
                        commandProps={{
                           label: 'Select products',
                        }}
                     />
                     {!hasAvailableProducts ? (
                        <div className='text-xs text-gray-500 mt-1'>
                           No products available for assignment.
                        </div>
                     ) : (
                        <div className='text-xs text-gray-500 mt-1'>
                           Selected: {selectedOptions.length} product
                           {selectedOptions.length !== 1 ? 's' : ''}
                        </div>
                     )}
                  </div>
               )}
            </div>

            <DialogFooter>
               <Button
                  variant='outline'
                  onClick={() => handleOpenChange(false)}
                  disabled={isSubmitting}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={selectedOptions.length === 0 || isSubmitting || !hasAvailableProducts}
                  className='min-w-[100px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Adding...
                     </div>
                  ) : (
                     `Add ${selectedOptions.length || ''} Product${
                        selectedOptions.length !== 1 ? 's' : ''
                     }`
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

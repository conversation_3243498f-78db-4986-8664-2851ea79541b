'use client';

import React from 'react';
import { InfoWindow } from '@react-google-maps/api';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { RadarDriver, Coordinate } from '../types/radar';
import { getStatusColor } from '../utils/map-utils';
import { ExternalLink } from 'lucide-react';

interface DriverInfoPopoverProps {
  driver: RadarDriver | null;
  isOpen: boolean;
  onClose: () => void;
  position: Coordinate | null;
}

export const DriverInfoPopover: React.FC<DriverInfoPopoverProps> = ({
  driver,
  isOpen,
  onClose,
  position,
}) => {
  const router = useRouter();

  if (!isOpen || !driver || !position) return null;

  const statusColor = getStatusColor(driver.status);

  const handleViewDetails = () => {
    router.push(`/dashboard/drivers/${driver.id}`);
  };

  const truncateId = (id: string) => {
    return `${id.substring(0, 8)}...`;
  };

  return (
    <InfoWindow position={position} onCloseClick={onClose}>
      <div className='p-2 min-w-[200px]'>
        {/* Driver ID */}
        <div className='mb-2'>
          <p className='text-xs text-gray-500 font-medium'>Driver ID</p>
          <p className='text-sm font-mono text-gray-800'>{truncateId(driver.id)}</p>
        </div>

        {/* Product Name */}
        <div className='mb-2'>
          <p className='text-xs text-gray-500 font-medium'>Product</p>
          <p className='text-sm text-gray-800'>{driver.productName}</p>
        </div>

        {/* Status Badge */}
        <div className='mb-3'>
          <p className='text-xs text-gray-500 font-medium mb-1'>Status</p>
          <span
            className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white'
            style={{ backgroundColor: statusColor }}
          >
            {driver.status.charAt(0).toUpperCase() + driver.status.slice(1)}
          </span>
        </div>

        {/* View Details Button */}
        <Button
          onClick={handleViewDetails}
          variant='outline'
          size='sm'
          className='w-full text-xs'
        >
          <ExternalLink className='mr-1 h-3 w-3' />
          More Details
        </Button>
      </div>
    </InfoWindow>
  );
};

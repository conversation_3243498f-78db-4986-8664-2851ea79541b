import { Card } from '@/components/ui/card';
import { RideRouteMap } from './ride-route-map';
import { Location } from '../types/ride';

interface RideRouteMapCardProps {
   pickupLocation: Location;
   destinationLocation: Location;
   stops?: Location[] | null;
}

export function RideRouteMapCard({
   pickupLocation,
   destinationLocation,
   stops,
}: RideRouteMapCardProps) {
   return (
      <Card className='p-4 rounded-sm'>
         <h3 className='text-base font-semibold text-gray-900 mb-2'>Route Map</h3>
         <RideRouteMap
            pickupLocation={pickupLocation}
            destinationLocation={destinationLocation}
            stops={stops}
         />
      </Card>
   );
}

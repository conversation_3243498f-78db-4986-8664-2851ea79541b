model DriverAccount {
  id               String    @id @default(uuid()) @map("id") @db.Uuid
  driverId         String    @unique @map("driver_id") @db.Uuid
  availableBalance Decimal   @default(0.0) @map("available_balance") @db.Decimal(10, 2)
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  driver       UserProfile                @relation("DriverAccount", fields: [driverId], references: [id], onDelete: Cascade)
  transactions DriverAccountTransaction[]

  @@index([driverId], name: "idx_driver_account_driver_id")
  @@index([availableBalance], name: "idx_driver_account_balance")
  @@map("driver_accounts")
}

model DriverAccountTransaction {
  id              String          @id @default(uuid()) @map("id") @db.Uuid
  driverId        String          @map("driver_id") @db.Uuid
  rideId          String?         @map("ride_id") @db.Uuid
  amount          Decimal         @map("amount") @db.Decimal(10, 2)
  transactionType TransactionType @map("transaction_type")
  reason          String          @map("reason")
  balanceAfter    Decimal         @map("balance_after") @db.Decimal(10, 2)
  createdAt       DateTime        @default(now()) @map("created_at")
  updatedAt       DateTime        @updatedAt @map("updated_at")
  deletedAt       DateTime?       @map("deleted_at") @db.Timestamptz

  // Relations
  driver        UserProfile    @relation("DriverAccountTransaction", fields: [driverId], references: [id], onDelete: Cascade, map: "driver_account_transaction_driver_fkey")
  ride          Ride?          @relation(fields: [rideId], references: [id], onDelete: SetNull)
  driverAccount DriverAccount? @relation(fields: [driverId], references: [driverId])

  @@index([driverId], name: "idx_driver_account_transaction_driver_id")
  @@index([rideId], name: "idx_driver_account_transaction_ride_id")
  @@index([transactionType], name: "idx_driver_account_transaction_type")
  @@index([createdAt], name: "idx_driver_account_transaction_created_at")
  @@map("driver_account_transactions")
}

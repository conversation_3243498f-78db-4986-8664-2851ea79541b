export interface CityAdmin {
   id: string;
   cityId: string;
   isEnabled: boolean;
   createdAt: string;
   admin: Admin;
}

interface Admin {
   id: string;
   firstName: string;
   lastName: string;
   status: string;
   user: User;
   role: Role;
}

interface Role {
   id: string;
   name: string;
}

interface User {
   id: string;
   email: string;
   phoneNumber: null;
}

export interface ListCityAdminsParams {
   page?: number;
   limit?: number;
   search?: string;
   status?: string;
}

export interface ListCityAdminsResponse {
   success: boolean;
   message: string;
   data: CityAdmin[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

export interface CityAdminResponse {
   success: boolean;
   message: string;
   data: Admin;
   timestamp: number;
}

export interface ListAvailableAdminsResponse {
   success: boolean;
   message: string;
   data: Admin[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

export interface AddAdminToCityRequest {
   adminIds: string[];
}

export interface RemoveAdminFromCityRequest {
   userProfileId: string;
}

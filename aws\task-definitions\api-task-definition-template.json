{"family": "api", "executionRoleArn": "arn:aws:iam::111311033809:role/ecsTaskExecutionRole", "networkMode": "bridge", "requiresCompatibilities": ["EC2"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "containerDefinitions": [{"name": "api-container", "image": "111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:BUILD_NUMBER_PLACEHOLDER", "cpu": 0, "portMappings": [{"name": "tuxi-node-api", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::tuxi-staging-bucket/server-variables/.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/api", "awslogs-create-group": "true", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}]}
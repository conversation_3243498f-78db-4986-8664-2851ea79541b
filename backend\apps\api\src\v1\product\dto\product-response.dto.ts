import { ApiProperty } from '@nestjs/swagger';

export class ProductLanguageSpecResponseDto {
  @ApiProperty({
    example: {
      en: 'Standard Ride',
      fr: 'Course Standard',
      ml: 'സ്റ്റാൻഡേർഡ് റൈഡ്',
    },
    description: 'Name translations',
    required: false,
  })
  name?: { [languageCode: string]: string };

  @ApiProperty({
    example: {
      en: 'Comfortable ride',
      fr: 'Course confortable',
      ml: 'സുഖകരമായ യാത്ര',
    },
    description: 'Description translations',
    required: false,
  })
  description?: { [languageCode: string]: string };

  @ApiProperty({
    example: { en: 'Economy', fr: 'Économique', ml: 'ഇക്കണോമി' },
    description: 'Title translations',
    required: false,
  })
  title?: { [languageCode: string]: string };
}

export class VehicleTypeResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'Car' })
  name!: string;

  @ApiProperty({ example: 'Four-wheeler vehicle', required: false })
  description?: string;

  @ApiProperty({ example: 'https://example.com/car.jpg', required: false })
  image?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;
}

export class CityProductResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  cityId!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  productId!: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  vehicleTypeId!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;
}

export class ProductServiceResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'Ride Sharing' })
  name!: string;

  @ApiProperty({ example: 'On-demand ride sharing service', required: false })
  description?: string;

  @ApiProperty({
    example: 'https://signed-url.amazonaws.com/uploads/icons/ride-sharing.png',
    required: false,
    description: 'Signed URL for the icon (valid for 1 hour)',
  })
  icon?: string;

  @ApiProperty({ example: 'ride_sharing', required: false })
  identifier?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;
}

export class ProductResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  vehicleTypeId!: string;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    required: false,
  })
  productServiceId?: string;

  @ApiProperty({ example: 'Standard Ride' })
  name!: string;

  @ApiProperty({
    example: 'Comfortable and affordable ride for daily commuting',
    required: false,
  })
  description?: string;

  @ApiProperty({ example: 'standard_ride', required: false })
  identifier?: string;

  @ApiProperty({
    example:
      'https://signed-url.amazonaws.com/uploads/products/standard-ride-icon.png',
    required: false,
    description: 'Signed URL for the icon (valid for 1 hour)',
  })
  icon?: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;

  @ApiProperty({
    example: 4,
    description: 'Maximum number of passengers allowed for this product',
  })
  passengerLimit!: number;

  @ApiProperty({
    type: ProductLanguageSpecResponseDto,
    description: 'Language specifications for translations',
    required: false,
  })
  languageSpec?: ProductLanguageSpecResponseDto;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;

  @ApiProperty({ example: null, required: false })
  deletedAt?: string;

  @ApiProperty({ type: VehicleTypeResponseDto, required: false })
  vehicleType?: VehicleTypeResponseDto;

  @ApiProperty({ type: ProductServiceResponseDto, required: false })
  productService?: ProductServiceResponseDto;

  @ApiProperty({ type: [CityProductResponseDto], required: false })
  cityProducts?: CityProductResponseDto[];
}

export class ProductStatisticsResponseDto {
  @ApiProperty({ example: 25 })
  totalProducts!: number;

  @ApiProperty({
    example: { Car: 10, Bike: 8, Auto: 7 },
    description: 'Number of products grouped by vehicle type',
  })
  productsByVehicleType!: Record<string, number>;
}

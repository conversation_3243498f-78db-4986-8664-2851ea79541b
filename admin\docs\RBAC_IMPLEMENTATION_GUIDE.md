# Role-Based Access Control (RBAC) Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing role-based access control (RBAC) permission checks in any module within the Tukxi admin dashboard. By following this guide, you can ensure consistent permission enforcement across all features.

---

## Table of Contents

1. [Understanding the RBAC System](#understanding-the-rbac-system)
2. [When to Implement RBAC](#when-to-implement-rbac)
3. [Step-by-Step Implementation](#step-by-step-implementation)
4. [Code Examples](#code-examples)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

---

## Understanding the RBAC System

### Core Components

1. **Permission Definitions** (`/src/role-based-access/permissions.ts`)
   - Central file containing all permission constants
   - Organized by module/feature
   - Format: `module:action` (e.g., `charge:create`, `driver:edit`)

2. **RBAC Hook** (`/src/role-based-access/use-role-based-access.ts`)
   - Provides permission checking functionality
   - Two main functions:
     - `hasPermission(permission)` - Returns boolean, used for conditionally enabling/disabling features
     - `withPermission(permission, callback)` - Executes callback only if user has permission, shows toast if denied

3. **Permission Query** (`/src/role-based-access/queries.ts`)
   - Fetches current user's permissions from the backend
   - Cached by React Query for performance

### How It Works

1. User permissions are fetched when the app loads
2. Each action checks if the user has the required permission
3. If permission exists, action proceeds
4. If permission is missing, action is blocked and user sees an error message

---

## When to Implement RBAC

Implement RBAC permission checks for:

- ✅ **Data fetching queries** - Prevent unauthorized data access
- ✅ **Create buttons** - Control who can add new records
- ✅ **Edit buttons** - Control who can modify records
- ✅ **Delete buttons** - Control who can remove records
- ✅ **Status toggle buttons** - Control who can activate/deactivate records
- ✅ **Bulk actions** - Control who can perform mass operations
- ✅ **Export/Import features** - Control data export/import access

---

## Step-by-Step Implementation

### Step 1: Define Permissions

First, check if permissions exist in `/src/role-based-access/permissions.ts`. If not, add them:

```typescript
export const RBAC_PERMISSIONS = {
  // ... existing permissions

  YOUR_MODULE: {
    CREATE: 'your_module:create',
    EDIT: 'your_module:edit',
    LIST: 'your_module:list',
    DELETE: 'your_module:delete',
    STATUS_UPDATE: 'your_module:status_update',  // Optional
    MANAGE: 'your_module:manage',                // Optional (for admin-level actions)
  },
} as const;
```

**Note:** Ensure these permissions match what the backend expects and are configured in the roles/permissions system.

---

### Step 2: Add Permissions to Query Hooks

**File:** `/src/modules/your-module/api/queries.ts`

#### Import Required Dependencies

```typescript
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
```

#### Add Permission Check to All Query Hooks

**List Query (Paginated):**
```typescript
export const useListYourModule = (page: number, limit: number, filters?: any) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST),  // Add this line
    queryKey: ['your-module-list', page, limit, filters],
    queryFn: async () => {
      // Your API call here
    },
    refetchOnWindowFocus: false,
  });
};
```

**Single Item Query (for viewing/editing):**
```typescript
export const useGetYourModule = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['your-module', id],
    queryFn: async () => {
      // Your API call here
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT),  // Add permission check
    refetchOnWindowFocus: false,
  });
};
```

**Active/Filtered List Query:**
```typescript
export const useGetActiveYourModules = () => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST),  // Add this line
    queryKey: ['your-module-active'],
    queryFn: async () => {
      // Your API call here
    },
    refetchOnWindowFocus: false,
  });
};
```

**Key Points:**
- The `enabled` prop prevents the query from running if the user lacks permission
- Use `hasPermission()` for boolean checks in query hooks
- The query won't execute and won't fetch data without permission
- Add permission checks to **ALL** query hooks, not just the list query
- For single item queries used in edit mode, use the EDIT permission
- For list/view queries, use the LIST permission

---

### Step 3: Add Permissions to Create/Add Buttons

**File:** `/src/modules/your-module/components/your-module-filters.tsx` (or wherever your create button lives)

#### Import Required Dependencies

```typescript
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
```

#### Add Permission Check to Button

```typescript
export function YourModuleFilters({ onAddItem }: YourModuleFiltersProps) {
  const { withPermission } = useRoleBasedAccess();

  return (
    <div className='flex justify-between items-center gap-4 mb-4'>
      {/* Search/filter components */}

      <Button
        className='cursor-pointer'
        variant='outline'
        onClick={() => withPermission(RBAC_PERMISSIONS.YOUR_MODULE.CREATE, onAddItem)}
      >
        <Plus />
        Add New Item
      </Button>
    </div>
  );
}
```

**Key Points:**
- Use `withPermission()` to wrap the callback
- If user lacks permission, a toast notification will appear automatically
- The callback (`onAddItem`) only executes if permission exists

---

### Step 4: Add Permissions to Table Actions (Edit/Delete)

**File:** `/src/modules/your-module/components/your-module-table.tsx`

#### Import Required Dependencies

```typescript
import {
  USE_ROLE_BASED_ACCESS,
  useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
```

#### Update Column Definition Function

Add `withPermission` to the function parameters:

```typescript
const getColumns = ({
  handleEditClick,
  handleDeleteClick,
  withPermission,  // Add this parameter
}: {
  handleEditClick: (item: YourItem) => void;
  handleDeleteClick: (item: YourItem) => void;
  withPermission: USE_ROLE_BASED_ACCESS['withPermission'];  // Add this type
}): ColumnDef<YourItem>[] => [
  // ... other columns

  {
    id: 'actions',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
    cell: ({ row }) => {
      const item = row.original;

      return (
        <div className='flex justify-center gap-1'>
          <button
            className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT, () => handleEditClick(item));
            }}
          >
            Edit
          </button>

          <button
            className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.YOUR_MODULE.DELETE, () => handleDeleteClick(item));
            }}
          >
            Delete
          </button>
        </div>
      );
    },
  },
];
```

#### Update Table Component

Initialize the hook and pass `withPermission` to `getColumns`:

```typescript
export function YourModuleTable({ data, isLoading }: YourModuleTableProps) {
  const { withPermission } = useRoleBasedAccess();  // Add this line

  // ... other state and logic

  const columns = getColumns({
    handleEditClick,
    handleDeleteClick,
    withPermission,  // Pass it here
  });

  // ... rest of component
}
```

---

### Step 5: Add Permissions to Status Toggle (Optional)

If your module has activate/deactivate functionality:

```typescript
<button
  onClick={() => {
    withPermission(RBAC_PERMISSIONS.YOUR_MODULE.STATUS_UPDATE, () =>
      handleToggleClick(item)
    );
  }}
>
  {item.isActive ? 'Deactivate' : 'Activate'}
</button>
```

---

## Code Examples

### Complete Example: Zone Type Module

#### 1. Permission Definitions (`permissions.ts`)

```typescript
export const RBAC_PERMISSIONS = {
  ZONE_TYPE: {
    CREATE: 'zone_type:create',
    DELETE: 'zone_type:delete',
    EDIT: 'zone_type:edit',
    LIST: 'zone_type:list',
    STATUS_UPDATE: 'zone_type:status_update',
  },
} as const;
```

#### 2. Query Hook (`queries.ts`)

```typescript
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListZoneType = ({ page, limit, search }: ListZoneTypeParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.ZONE_TYPE.LIST),
    queryKey: ['zone-types', page, limit, search],
    queryFn: (): Promise<ListZoneTypeResponse> => {
      return apiClient.get('/zone-types', {
        params: { page, limit, search },
      });
    },
  });
};
```

#### 3. Create Modal (`zone-type-modal.tsx`)

```typescript
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const ZoneTypeModal = ({ mode = 'create' }: ZoneTypeModalProps) => {
  const { withPermission } = useRoleBasedAccess();

  // For create mode with button
  if (mode === 'create') {
    return (
      <>
        <Button
          onClick={() => withPermission(RBAC_PERMISSIONS.ZONE_TYPE.CREATE, () => setModalOpen(true))}
        >
          <Plus />
          Add Zone Type
        </Button>
        {/* Modal component */}
      </>
    );
  }

  // For edit mode
  return <Dialog>{/* Modal content */}</Dialog>;
};
```

#### 4. Table Component (`zone-type-table.tsx`)

```typescript
import {
  USE_ROLE_BASED_ACCESS,
  useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
  handleEditClick,
  handleDeleteClick,
  handleToggleClick,
  withPermission,
}: {
  handleEditClick: (id: string) => void;
  handleDeleteClick: (item: ZoneType) => void;
  handleToggleClick: (item: ZoneType) => void;
  withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<ZoneType>[] => [
  // ... other columns
  {
    id: 'actions',
    cell: ({ row }) => {
      const zoneType = row.original;
      return (
        <div className='flex justify-center gap-1'>
          <button
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.ZONE_TYPE.EDIT, () =>
                handleEditClick(zoneType.id)
              );
            }}
          >
            Edit
          </button>
          <button
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.ZONE_TYPE.STATUS_UPDATE, () =>
                handleToggleClick(zoneType)
              );
            }}
          >
            {zoneType.isActive ? 'Deactivate' : 'Activate'}
          </button>
          <button
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.ZONE_TYPE.DELETE, () =>
                handleDeleteClick(zoneType)
              );
            }}
          >
            Delete
          </button>
        </div>
      );
    },
  },
];

export function ZoneTypeTable({ data }: ZoneTypeTableProps) {
  const { withPermission } = useRoleBasedAccess();

  const columns = getColumns({
    handleEditClick,
    handleDeleteClick,
    handleToggleClick,
    withPermission,
  });

  // ... rest of component
}
```

---

## Best Practices

### 1. **Consistency**
- Always use the same permission names in frontend and backend
- Follow the naming convention: `module:action`
- Keep permission definitions organized by module

### 2. **Granularity**
- Define permissions at the appropriate level
- Don't create overly broad permissions (e.g., `admin:all`)
- Consider future needs when designing permission structure

### 3. **User Experience**
- Always show clear error messages when permissions are denied
- **Keep buttons visible** even without permissions to maintain consistent UI (hiding too many elements makes the UI messy)
- Use `withPermission()` to block unauthorized actions and automatically show toast notifications
- Provide immediate feedback through toast notifications when permission is denied

### 4. **Performance**
- Use `hasPermission()` in query `enabled` props to prevent unnecessary API calls
- Permissions are cached by React Query, so checking is fast

### 5. **Testing**
- Test with users having different permission levels
- Verify that denied permissions show appropriate messages
- Ensure protected queries don't run without permission

### 6. **Documentation**
- Document which permissions are required for each feature
- Keep this guide updated when adding new patterns
- Comment complex permission logic

---

## Common Patterns

### Pattern 1: Button Protection with Toast Feedback (Recommended)

**Use this pattern for buttons to keep UI consistent while blocking unauthorized actions:**

```typescript
const { withPermission } = useRoleBasedAccess();

<Button
  onClick={() => withPermission(RBAC_PERMISSIONS.YOUR_MODULE.CREATE, handleCreate)}
>
  Create
</Button>
```

**How it works:**
- Button remains visible regardless of permission
- If user has permission: callback executes
- If user lacks permission: toast notification appears automatically, callback is blocked
- Maintains clean, consistent UI

### Pattern 2: Query Permission Checks

**Use this pattern in query hooks to prevent unauthorized data fetching:**

```typescript
const { hasPermission } = useRoleBasedAccess();

return useQuery({
  enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST),
  queryKey: ['your-module'],
  queryFn: async () => {
    // API call
  },
});
```

**How it works:**
- Returns boolean check without toast
- Query doesn't run if permission is missing
- Used in `enabled` prop of React Query

### Pattern 3: Conditional Rendering (Use Sparingly)

**Only use when hiding UI elements won't create messy/inconsistent layouts:**

```typescript
const { hasPermission } = useRoleBasedAccess();

{hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.CREATE) && (
  <AdvancedFeatureSection />
)}
```

**When to use:**
- Large sections/features that won't break layout when hidden
- Admin-only panels or settings
- NOT for action buttons (use Pattern 1 instead)

### Pattern 4: Multiple Permissions (OR Logic)

```typescript
const { hasAnyPermission } = useRoleBasedAccess();

<Button
  onClick={() => {
    if (hasAnyPermission([
      RBAC_PERMISSIONS.YOUR_MODULE.EDIT,
      RBAC_PERMISSIONS.YOUR_MODULE.MANAGE
    ])) {
      handleEdit();
    }
  }}
>
  Edit
</Button>
```

---

## Troubleshooting

### Issue 1: Permission Check Always Fails

**Symptoms:** User has permission in database but check still fails

**Solutions:**
1. Verify permission name matches exactly between frontend and backend
2. Check if user's role has the permission assigned
3. Clear React Query cache and refresh
4. Check browser console for permission fetch errors

### Issue 2: Query Doesn't Run

**Symptoms:** Data never loads even with correct permissions

**Solutions:**
1. Check if `enabled` prop is correctly set
2. Verify `hasPermission()` is being called correctly
3. Ensure permissions have loaded before component renders
4. Check network tab for failed API calls

### Issue 3: No Toast Notification on Denial

**Symptoms:** Action is blocked but no error message appears

**Solutions:**
1. Ensure you're using `withPermission()` instead of `hasPermission()`
2. Check if toast library is properly configured
3. Verify `showToast` is not set to `false`

### Issue 4: Permission Check Runs Before Permissions Load

**Symptoms:** Intermittent permission denials on page load

**Solutions:**
1. Check `isLoading` from `useRoleBasedAccess()`
2. Show loading state while permissions fetch
3. Don't render permission-protected UI until `isLoading` is `false`

---

## Checklist for Implementation

When implementing RBAC in a new module, use this checklist:

### 1. Permission Setup
- [ ] Define permissions in `permissions.ts`
- [ ] Verify permissions match backend expectations

### 2. Query Protection (All Query Hooks)
- [ ] Add permission check to list/paginated query (`enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST)`)
- [ ] Add permission check to single item query (`enabled: !!id && hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT)`)
- [ ] Add permission check to active/filtered list queries (`enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST)`)
- [ ] Add permission checks to any other custom query hooks

### 3. Action Protection (Buttons/UI)
- [ ] Add permission check to create button (`withPermission(RBAC_PERMISSIONS.YOUR_MODULE.CREATE, ...)`)
- [ ] Add permission check to edit button (`withPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT, ...)`)
- [ ] Add permission check to delete button (`withPermission(RBAC_PERMISSIONS.YOUR_MODULE.DELETE, ...)`)
- [ ] Add permission check to status toggle (`withPermission(RBAC_PERMISSIONS.YOUR_MODULE.STATUS_UPDATE, ...)`) (if applicable)
- [ ] Add permission checks to any bulk actions
- [ ] Ensure buttons remain visible (don't hide them based on permissions)

### 4. Testing
- [ ] Test with user having all permissions (verify all features work)
- [ ] Test with user lacking LIST permission (verify no data loads)
- [ ] Test with user having LIST but lacking CREATE (verify toast on create attempt)
- [ ] Test with user having LIST but lacking EDIT (verify toast on edit attempt)
- [ ] Test with user having LIST but lacking DELETE (verify toast on delete attempt)
- [ ] Verify toast notifications appear on all permission denials
- [ ] Check that UI remains consistent when permissions are missing

### 5. Documentation & Backend
- [ ] Update backend to enforce same permissions
- [ ] Document permissions in module README/docs
- [ ] Add comments for complex permission logic

---

## Files Reference

### Key Files to Know

1. **`/src/role-based-access/permissions.ts`** - All permission definitions
2. **`/src/role-based-access/use-role-based-access.ts`** - RBAC hook
3. **`/src/role-based-access/queries.ts`** - Permission fetching
4. **`/src/role-based-access/page-permissions.ts`** - Route-level permissions

### Example Module Files

- **Zone Type Module:** `/src/modules/zone-type/`
- **Charge Module:** `/src/modules/charge/`
- **Tax Group Module:** `/src/modules/tax-group/`

---

## Additional Resources

- [Main Architecture Guide](/admin/CLAUDE.md)
- [API Documentation](http://localhost:3000/docs)
- [React Query Docs](https://tanstack.com/query/latest)

---

## Summary

Implementing RBAC is a systematic process:

1. **Define permissions** in `permissions.ts` matching backend expectations
2. **Protect ALL query hooks** with `enabled: hasPermission(...)` - includes list, single item, and filtered queries
3. **Protect action buttons** with `withPermission(...)` - create, edit, delete, status toggles
4. **Keep UI consistent** - buttons stay visible, `withPermission` handles blocking and feedback
5. **Test thoroughly** with different permission levels and scenarios

### Key Principles:
- **`hasPermission()`** - Returns boolean, use in query `enabled` props (no toast)
- **`withPermission()`** - Wraps callbacks, blocks execution, shows toast on denial (use for buttons)
- **Buttons stay visible** - Don't hide based on permissions (keeps UI clean)
- **Protect ALL queries** - Not just lists, also single item and filtered queries
- **Backend alignment** - Frontend permissions must match backend enforcement

By following this guide, you ensure consistent, secure, and user-friendly permission enforcement across the entire admin dashboard.

'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Info } from 'lucide-react';
import { useListZoneType } from '../../zone-type/api/queries';
import { useCreateZone, useUpdateZone } from '../api/city-zone-mutations';
import { CreateZoneRequest, UpdateZoneRequest, Zone } from '../types/city-zone';

const zoneFormSchema = z.object({
   name: z
      .string()
      .min(2, 'Zone name must be at least 2 characters long')
      .max(100, 'Zone name cannot exceed 100 characters')
      .regex(/^[a-zA-Z0-9\s]+$/, 'Zone name can only contain letters and numbers'),
   description: z.string().optional(),
   zoneTypeId: z.string().min(1, 'Zone type is required'),
   priority: z.number().min(0, 'Priority must be at least 0'),
});

type ZoneFormData = z.infer<typeof zoneFormSchema>;

interface CityZoneModalProps {
   cityId: string;
   cityName?: string;
   zone?: Zone | null;
   open?: boolean;
   onOpenChange?: (open: boolean) => void;
   trigger?: React.ReactNode;
   onZoneCreated?: (zone: Zone) => void;
}

export function CityZoneModal({
   cityId,
   cityName,
   zone,
   open: controlledOpen,
   onOpenChange: controlledOnOpenChange,
   onZoneCreated,
}: CityZoneModalProps) {
   const isEdit = !!zone;

   // Use controlled or internal state
   const open = controlledOpen !== undefined ? controlledOpen : false;
   const setOpen = controlledOnOpenChange || (() => {});

   const form = useForm<ZoneFormData>({
      resolver: zodResolver(zoneFormSchema),
      defaultValues: {
         name: '',
         description: '',
         zoneTypeId: '',
         priority: 1,
      },
   });

   const createZoneMutation = useCreateZone();
   const updateZoneMutation = useUpdateZone();
   const zoneTypesQuery = useListZoneType({
      page: 1,
      limit: 100,
      isActive: true,
   });

   // Reset form when zone changes or modal opens
   useEffect(() => {
      if (open) {
         if (isEdit && zone) {
            form.reset({
               name: zone.name,
               description: zone.description || '',
               zoneTypeId: zone.zoneTypeId,
               priority: zone.priority,
            });
         } else {
            form.reset({
               name: '',
               description: '',
               zoneTypeId: '',
               priority: 1,
            });
         }
      }
   }, [zone, open, isEdit, form]);

   const handleSubmit = form.handleSubmit(async (data: ZoneFormData) => {
      if (isEdit && zone) {
         // Update existing zone
         const submitData: UpdateZoneRequest = {
            ...data,
            description: data.description?.trim() || undefined,
         };

         updateZoneMutation.mutate(
            { id: zone.id, data: submitData },
            {
               onSuccess: () => {
                  setOpen(false);
               },
            }
         );
      } else {
         // Create new zone
         const submitData: CreateZoneRequest = {
            ...data,
            cityId,
            description: data.description?.trim() || undefined,
         };

         createZoneMutation.mutate(submitData, {
            onSuccess: response => {
               setOpen(false);
               form.reset();
               // Call the callback to open the map
               onZoneCreated?.(response.data);
            },
         });
      }
   });

   const isLoading = createZoneMutation.isPending || updateZoneMutation.isPending;

   const modalContent = (
      <DialogContent className='sm:max-w-[500px]'>
         <form onSubmit={handleSubmit}>
            <DialogHeader>
               <DialogTitle>{isEdit ? 'Edit Zone' : 'Add New Zone'}</DialogTitle>
               <DialogDescription>
                  {isEdit
                     ? 'Update zone details. Polygon boundaries can be modified using the map.'
                     : 'Create a new zone within this city. You can add polygon boundaries later using the map.'}
               </DialogDescription>
            </DialogHeader>

            <div className='grid gap-4 py-4'>
               {/* Zone Name */}
               <div className='grid gap-2'>
                  <Label htmlFor='name' className='text-sm font-medium'>
                     Zone Name <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                     id='name'
                     placeholder='Enter zone name...'
                     {...form.register('name')}
                     className={`w-full ${form.formState.errors.name ? 'border-red-500' : ''}`}
                  />
                  {form.formState.errors.name && (
                     <span className='text-xs text-red-500'>
                        {form.formState.errors.name.message}
                     </span>
                  )}
               </div>

               {/* Zone Type */}
               <div className='grid gap-2 w-full'>
                  <Label htmlFor='zoneType' className='text-sm font-medium'>
                     Zone Type <span className='text-red-500'>*</span>
                  </Label>
                  <Select
                     value={form.watch('zoneTypeId')}
                     onValueChange={value => form.setValue('zoneTypeId', value)}
                  >
                     <SelectTrigger
                        className={`w-full ${
                           form.formState.errors.zoneTypeId ? 'border-red-500' : ''
                        }`}
                     >
                        <SelectValue placeholder='Select zone type...' />
                     </SelectTrigger>
                     <SelectContent className='w-full'>
                        {zoneTypesQuery.data?.data.data.map(zoneType => (
                           <SelectItem key={zoneType.id} value={zoneType.id}>
                              <div className='flex items-center gap-2'>
                                 <span>{zoneType.name}</span>
                                 <span className='text-xs text-gray-500'>
                                    ({zoneType.algorithm})
                                 </span>
                              </div>
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
                  {form.formState.errors.zoneTypeId && (
                     <span className='text-xs text-red-500'>
                        {form.formState.errors.zoneTypeId.message}
                     </span>
                  )}
               </div>

               {/* Priority */}
               <div className='grid gap-2'>
                  <Label htmlFor='priority' className='text-sm font-medium'>
                     Priority <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                     id='priority'
                     type='number'
                     min={0}
                     placeholder='Enter priority...'
                     {...form.register('priority', { valueAsNumber: true })}
                     className={`w-full ${form.formState.errors.priority ? 'border-red-500' : ''}`}
                  />
                  {form.formState.errors.priority && (
                     <span className='text-xs text-red-500'>
                        {form.formState.errors.priority.message}
                     </span>
                  )}
                  {/* Info text */}
                  <div className='flex gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md'>
                     <Info className='w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5' />
                     <div className='text-xs text-blue-800 space-y-2'>
                        <p>
                           <strong>Note:</strong> Lower numbers indicate higher priority (with 0
                           being the highest). Even if two zones don't appear to overlap visually,
                           they may technically overlap within the H3 index system. In such cases,
                           the zone with the lowest priority number will take precedence.
                        </p>
                        <p>
                           {cityName ? (
                              <>
                                 The auto generated <strong>{cityName}</strong> city boundary itself
                                 is assigned priority 100, meaning zones with priority values 0-99
                                 will take precedence over the city boundary.
                              </>
                           ) : (
                              'The auto generated city boundary itself is assigned priority 100, meaning zones with priority values 0-99 will take precedence over the city boundary.'
                           )}
                        </p>
                     </div>
                  </div>
               </div>

               {/* Description */}
               <div className='grid gap-2'>
                  <Label htmlFor='description' className='text-sm font-medium'>
                     Description
                  </Label>
                  <Textarea
                     id='description'
                     placeholder='Enter zone description...'
                     {...form.register('description')}
                     rows={3}
                     className='w-full'
                  />
               </div>
            </div>

            <DialogFooter>
               <Button
                  type='button'
                  variant='outline'
                  onClick={() => setOpen(false)}
                  disabled={isLoading}
               >
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading}>
                  {isLoading
                     ? isEdit
                        ? 'Saving...'
                        : 'Creating...'
                     : isEdit
                     ? 'Save Changes'
                     : 'Create Zone'}
               </Button>
            </DialogFooter>
         </form>
      </DialogContent>
   );
   // For edit modal (controlled)
   return (
      <Dialog open={open} onOpenChange={setOpen}>
         {modalContent}
      </Dialog>
   );
}

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import {
  DriverAccount,
  DriverAccountTransaction,
  TransactionType,
} from './models/driverAccount.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class DriverAccountRepository extends BaseRepository<DriverAccount> {
  protected readonly modelName = 'driverAccount';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new driver account
   */
  async createDriverAccount(
    data: Omit<DriverAccount, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<DriverAccount> {
    return this.create(data);
  }

  /**
   * Find driver account by driver ID
   */
  async findByDriverId(driverId: string): Promise<DriverAccount | null> {
    return this.findOne({
      where: { driverId },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        transactions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }

  /**
   * Update driver account balance
   */
  async updateBalance(
    driverId: string,
    newBalance: number,
  ): Promise<DriverAccount> {
    return this.update({
      where: { driverId },
      data: { availableBalance: newBalance },
    });
  }

  /**
   * Get or create driver account
   */
  async getOrCreateDriverAccount(driverId: string): Promise<DriverAccount> {
    let account = await this.findByDriverId(driverId);

    if (!account) {
      account = await this.createDriverAccount({
        driverId,
        availableBalance: 0,
      });
    }

    return account;
  }
}

@Injectable()
export class DriverAccountTransactionRepository extends BaseRepository<DriverAccountTransaction> {
  protected readonly modelName = 'driverAccountTransaction';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new transaction
   */
  async createTransaction(
    data: Omit<
      DriverAccountTransaction,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
    >,
  ): Promise<DriverAccountTransaction> {
    return this.create(data);
  }

  /**
   * Find transactions by driver ID with pagination
   */
  async findTransactionsByDriverId(
    driverId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ transactions: DriverAccountTransaction[]; total: number }> {
    const skip = (page - 1) * limit;

    const [transactions, total] = await Promise.all([
      this.findMany({
        where: { driverId },
        include: {
          ride: {
            select: {
              id: true,
              pickupLocation: true,
              destinationLocation: true,
              completedAt: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.count({ where: { driverId } }),
    ]);

    return { transactions, total };
  }

  /**
   * Find transactions by ride ID
   */
  async findTransactionsByRideId(
    rideId: string,
  ): Promise<DriverAccountTransaction[]> {
    return this.findMany({
      where: { rideId },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Get transaction summary for driver
   */
  async getTransactionSummary(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    totalCredits: number;
    totalDebits: number;
    netAmount: number;
    transactionCount: number;
  }> {
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    const transactions = await this.findMany({
      where: whereClause,
      select: {
        amount: true,
        transactionType: true,
      },
    });

    const summary = transactions.reduce(
      (acc, transaction) => {
        if (transaction.transactionType === TransactionType.CREDIT) {
          acc.totalCredits += Number(transaction.amount);
        } else {
          acc.totalDebits += Number(transaction.amount);
        }
        acc.transactionCount++;
        return acc;
      },
      { totalCredits: 0, totalDebits: 0, transactionCount: 0, netAmount: 0 },
    );

    summary.netAmount = summary.totalCredits - summary.totalDebits;
    return summary;
  }
}

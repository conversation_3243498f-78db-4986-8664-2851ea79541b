'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useRejectKycDocument } from '../api/mutations';
import { toast } from '@/lib/toast';
import { XCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const rejectFormSchema = z.object({
   rejectionNote: z.string().min(1, 'Rejection reason is required').min(10, 'Please provide a detailed reason (at least 10 characters)'),
});

type RejectFormData = z.infer<typeof rejectFormSchema>;

interface RejectKycModalProps {
   open: boolean;
   onClose: () => void;
   onSuccess: () => void;
   documentId: string;
   documentName: string;
}

export function RejectKycModal({
   open,
   onClose,
   onSuccess,
   documentId,
   documentName,
}: RejectKycModalProps) {
   const rejectKycMutation = useRejectKycDocument();
   
   const {
      register,
      handleSubmit,
      formState: { errors },
      reset,
   } = useForm<RejectFormData>({
      resolver: zodResolver(rejectFormSchema),
   });

   const handleReject = async (data: RejectFormData) => {
      try {
         await rejectKycMutation.mutateAsync({
            id: documentId,
            rejectionNote: data.rejectionNote,
         });
         toast.success('Document rejected successfully');
         onSuccess();
         handleClose();
      } catch {
         toast.error('Failed to reject document');
      }
   };

   const handleClose = () => {
      reset();
      onClose();
   };

   return (
      <Dialog open={open} onOpenChange={handleClose}>
         <DialogContent 
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className="sm:max-w-md"
         >
            <form onSubmit={handleSubmit(handleReject)}>
               <DialogHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                     <XCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <DialogTitle className="text-lg font-semibold text-gray-900">
                     Reject Document
                  </DialogTitle>
                  <DialogDescription className="text-sm text-gray-600">
                     Please provide a reason for rejecting the {documentName}. This will help the driver understand what needs to be corrected.
                  </DialogDescription>
               </DialogHeader>
               
               <div className="my-6">
                  <label htmlFor="rejectionNote" className="block text-sm font-medium text-gray-700 mb-2">
                     Rejection Reason *
                  </label>
                  <Textarea
                     id="rejectionNote"
                     placeholder="Document is not clear. Please upload a clearer image..."
                     rows={4}
                     {...register('rejectionNote')}
                     className={errors.rejectionNote ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                  />
                  {errors.rejectionNote && (
                     <p className="mt-1 text-sm text-red-600">{errors.rejectionNote.message}</p>
                  )}
               </div>

               <DialogFooter className="gap-2">
                  <Button
                     type="button"
                     variant="outline"
                     onClick={handleClose}
                     disabled={rejectKycMutation.isPending}
                     className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={rejectKycMutation.isPending}
                     className="bg-red-600 text-white hover:bg-red-700"
                  >
                     {rejectKycMutation.isPending ? 'Rejecting...' : 'Reject Document'}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}

'use client';

import Image from 'next/image';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteVehicleCategory } from '../api/mutations';
import { VehicleCategory, ListVehicleCategoryResponse } from '../types/vehicle-category';
import { DeleteVehicleCategoryDialog } from './delete-vehicle-category-dialog';
import { VehicleCategoryModal } from './vehicle-category-modal';
import { VehicleCategoryTableEmpty } from './vehicle-category-table-empty';
import { VehicleCategoryTableLoading } from './vehicle-category-table-loading';
import { CustomPagination } from '@/components/pagination';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   deleteVehicleCategoryMutation,
   handleDeleteClick,
   handleEditClick,
   vehicleCategoryToDelete,
   withPermission,
}: {
   handleDeleteClick: (id: string) => void;
   handleEditClick: (id: string) => void;
   deleteVehicleCategoryMutation: any;
   vehicleCategoryToDelete: string | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<VehicleCategory>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const vehicleCategory = row.original as VehicleCategory;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium'>{vehicleCategory.name}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const vehicleCategory = row.original as VehicleCategory;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {vehicleCategory.description || 'No description'}
               </div>
            </div>
         );
      },
      size: 300,
   },
   {
      accessorKey: 'image',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Image</div>,
      cell: ({ row }) => {
         const vehicleCategory = row.original as VehicleCategory;
         return (
            <div className='flex justify-center'>
               {vehicleCategory.image ? (
                  <div className='w-10 h-10 overflow-hidden rounded border border-gray-200 flex-shrink-0'>
                     <Image
                        src={vehicleCategory.image}
                        alt={vehicleCategory.name}
                        width={40}
                        height={40}
                        className='w-full h-full object-cover'
                        onError={e => {
                           const target = e.target as HTMLImageElement;
                           target.style.display = 'none';
                           const parent = target.parentElement;
                           if (parent) {
                              parent.innerHTML =
                                 '<span class="text-xs text-gray-400">No image</span>';
                           }
                        }}
                     />
                  </div>
               ) : (
                  <span className='text-xs text-gray-400'>No image</span>
               )}
            </div>
         );
      },
      size: 100,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const vehicleCategory = row.original as VehicleCategory;
         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.VEHICLE_CATEGORY.EDIT, () =>
                        handleEditClick(vehicleCategory.id)
                     );
                  }}
               >
                  Edit
               </button>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleDeleteClick(vehicleCategory.id)}
                  disabled={
                     deleteVehicleCategoryMutation.isPending &&
                     vehicleCategoryToDelete === vehicleCategory.id
                  }
                  style={{ display: 'none' }}
               >
                  Delete
               </button>
            </div>
         );
      },
      size: 150,
   },
];

interface VehicleCategoryTableProps {
   data: ListVehicleCategoryResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function VehicleCategoryTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
}: VehicleCategoryTableProps) {
   const [vehicleCategoryToDelete, setVehicleCategoryToDelete] = useState<string | null>(null);
   const [vehicleCategoryToEdit, setVehicleCategoryToEdit] = useState<string | null>(null);
   const deleteVehicleCategoryMutation = useDeleteVehicleCategory();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleDeleteClick = (id: string) => {
      setVehicleCategoryToDelete(id);
   };

   const handleEditClick = (id: string) => {
      setVehicleCategoryToEdit(id);
   };

   const handleDeleteConfirm = () => {
      if (!vehicleCategoryToDelete) return;

      deleteVehicleCategoryMutation.mutate(vehicleCategoryToDelete, {
         onSuccess: () => {
            toast.success('Vehicle category deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['vehicle-categories'] });
         },
         onSettled: () => {
            setVehicleCategoryToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteVehicleCategoryMutation,
      handleDeleteClick,
      handleEditClick,
      vehicleCategoryToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <VehicleCategoryTableLoading />;
   }

   if (!data?.data?.length) {
      return <VehicleCategoryTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         <DeleteVehicleCategoryDialog
            isOpen={!!vehicleCategoryToDelete}
            onClose={() => setVehicleCategoryToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteVehicleCategoryMutation.isPending}
         />

         <VehicleCategoryModal
            mode='edit'
            vehicleCategoryId={vehicleCategoryToEdit}
            isOpen={!!vehicleCategoryToEdit}
            onClose={() => setVehicleCategoryToEdit(null)}
         />
      </div>
   );
}

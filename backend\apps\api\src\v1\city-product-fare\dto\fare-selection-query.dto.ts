import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsOptional } from 'class-validator';

export class FareSelectionQueryDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Pickup zone ID for fare selection',
    required: false,
  })
  @IsUUID('4', { message: 'Pickup zone ID must be a valid UUID' })
  @IsOptional()
  pickupZoneId?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Destination zone ID for fare selection',
    required: false,
  })
  @IsUUID('4', { message: 'Destination zone ID must be a valid UUID' })
  @IsOptional()
  destinationZoneId?: string;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UpdateLanguageDto {
  @ApiProperty({ example: 'en', required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ example: 'English', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'English', required: false })
  @IsOptional()
  @IsString()
  nameInNative?: string;
}

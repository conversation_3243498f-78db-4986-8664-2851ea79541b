import { Users, Search } from 'lucide-react';

interface CityAdminTableEmptyProps {
  hasFilters: boolean;
}

export function CityAdminTableEmpty({ hasFilters }: CityAdminTableEmptyProps) {
  return (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4'>
        {hasFilters ? (
          <Search className='w-6 h-6 text-gray-400' />
        ) : (
          <Users className='w-6 h-6 text-gray-400' />
        )}
      </div>
      <h3 className='text-lg font-medium text-gray-900 mb-2'>
        {hasFilters ? 'No admins found' : 'No city admins'}
      </h3>
      <p className='text-gray-500 max-w-sm'>
        {hasFilters
          ? 'Try adjusting your search or filter criteria to find what you are looking for.'
          : 'This city does not have any admins assigned yet. Add some admins to get started.'}
      </p>
    </div>
  );
}
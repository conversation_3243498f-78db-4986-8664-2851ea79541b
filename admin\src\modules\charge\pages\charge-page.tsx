'use client';

import { Card } from '@/components/ui/card';
import { useState, useEffect } from 'react';
import { useListAllCharges } from '../api/charge-queries';
import { ChargeFilters } from '../components/charge-filters';
import { ChargeTable } from '../components/charge-table';
import { ChargeModal } from '../components/charge-modal';

export function ChargePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [debouncedSearch, setDebouncedSearch] = useState('');
   const [isCreateChargeModalOpen, setIsCreateChargeModalOpen] = useState(false);

   // Debounce search to prevent excessive API calls
   useEffect(() => {
      const timer = setTimeout(() => {
         setDebouncedSearch(search);
         setPage(1);
      }, 500);

      return () => clearTimeout(timer);
   }, [search]);

   const listChargesQuery = useListAllCharges(page, limit, debouncedSearch || undefined, true);

   const handleClearSearch = () => {
      setSearch('');
      setDebouncedSearch('');
      setPage(1);
   };

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex flex-col gap-2'>
            <h2 className='text-2xl font-semibold text-gray-900'>Charges</h2>
            <p className='text-sm text-gray-600'>
               These are common charges that can be shared across multiple charge groups. They are
               centrally managed and can be attached to any charge group as needed.
            </p>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <ChargeFilters
               search={search}
               onSearchChange={setSearch}
               onClearSearch={handleClearSearch}
               isLoading={listChargesQuery.isFetching}
               onAddCharge={() => setIsCreateChargeModalOpen(true)}
            />

            <ChargeTable
               data={listChargesQuery.data?.data?.data}
               isLoading={listChargesQuery.isLoading}
            />
         </Card>

         {/* Create Charge Modal */}
         <ChargeModal
            mode='create'
            isOpen={isCreateChargeModalOpen}
            onClose={() => setIsCreateChargeModalOpen(false)}
         />
      </div>
   );
}

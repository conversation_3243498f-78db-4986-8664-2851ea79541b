'use client';

import { useState } from 'react';
import { Plus, Car, AlertCircle, CheckCircle, Clock, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useDriverVehicles } from '../api/queries';
import { useChangeVehicleStatus, useVerifyDriverVehicle } from '../api/mutations';
import { VehicleModal } from './vehicle-modal';
import { VehicleDocuments } from './vehicle-documents';
import { VehicleProducts } from './vehicle-products';
import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from '@/components/ui/accordion';
import { DriverVehicle } from '../types/driver';
import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertD<PERSON>ogHeader,
   <PERSON>ertDialog<PERSON><PERSON><PERSON>,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

interface DriverVehiclesProps {
   profileId: string;
   driverCityId?: string;
   driverCityName?: string;
}

export function DriverVehicles({ profileId, driverCityId, driverCityName }: DriverVehiclesProps) {
   const [isVehicleModalOpen, setIsVehicleModalOpen] = useState(false);
   const [editingVehicle, setEditingVehicle] = useState<DriverVehicle | null>(null);
   const [expandedDocuments, setExpandedDocuments] = useState<string | null>(null);
   const [expandedProducts, setExpandedProducts] = useState<string | null>(null);

   const { data: vehiclesResponse, isLoading, error, refetch } = useDriverVehicles(profileId);
   const vehicles = vehiclesResponse?.data || [];
   const changeStatusMutation = useChangeVehicleStatus();
   const verifyVehicleMutation = useVerifyDriverVehicle();
   const queryClient = useQueryClient();

   // Separate function for verification status based on document count
   // const getVerificationBadge = (vehicle: DriverVehicle) => {
   //    const isVerified = vehicle._count.driverVehicleDocuments > 0;

   //    if (isVerified) {
   //       return (
   //          <Badge className='bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1'>
   //             <CheckCircle className='w-3 h-3 mr-1' />
   //             Verified
   //          </Badge>
   //       );
   //    } else {
   //       return (
   //          <Badge className='bg-gray-100 text-gray-700 text-xs font-medium px-2 py-1'>
   //             <Clock className='w-3 h-3 mr-1' />
   //             Not Verified
   //          </Badge>
   //       );
   //    }
   // };

   // Separate function for operational status
   const getOperationalStatusBadge = (vehicle: DriverVehicle) => {
      switch (vehicle.status) {
         case 'active':
            return (
               <Badge className='bg-green-100 text-green-700 text-xs font-medium px-2 py-1'>
                  <CheckCircle className='w-3 h-3 mr-1' />
                  Active
               </Badge>
            );
         case 'inactive':
            return (
               <Badge className='bg-red-100 text-red-700 text-xs font-medium px-2 py-1'>
                  <AlertCircle className='w-3 h-3 mr-1' />
                  Inactive
               </Badge>
            );
         case 'pending':
         default:
            return (
               <Badge className='bg-yellow-100 text-yellow-700 text-xs font-medium px-2 py-1'>
                  <Clock className='w-3 h-3 mr-1' />
                  Pending
               </Badge>
            );
      }
   };

   const handleVehicleClick = (vehicle: DriverVehicle) => {
      if (vehicle._count.driverVehicleDocuments === 0) {
         verifyVehicleMutation.mutate(vehicle.id, {
            onSuccess: response => {
               if (response.success) {
                  toast.success('Vehicle verified successfully!');
                  setExpandedDocuments(vehicle.id);
                  refetch();
               }
            },
         });
      }
   };

   const handleVehicleCreated = async (vehicleId: string) => {
      // Refetch vehicles to get the newly created vehicle
      const updatedVehicles = await refetch();
      const newVehicle = updatedVehicles.data?.data.find(v => v.id === vehicleId);

      if (newVehicle) {
         // Auto-expand documents accordion for the new vehicle
         setExpandedDocuments(vehicleId);
      }
   };

   const handleModalClose = () => {
      setIsVehicleModalOpen(false);
      setEditingVehicle(null);
      refetch();
   };

   const handleVehicleUpdated = (shouldShowDocuments?: boolean) => {
      if (shouldShowDocuments && editingVehicle) {
         // Expand the documents accordion for the updated vehicle
         setExpandedDocuments(editingVehicle.id);
         setEditingVehicle(null);
         setIsVehicleModalOpen(false);
         refetch();
      } else {
         handleModalClose();
      }
   };

   const handleCreateVehicle = () => {
      setEditingVehicle(null); // Ensure we're in create mode
      setIsVehicleModalOpen(true);
   };

   const handleEditVehicle = (vehicle: DriverVehicle) => {
      setEditingVehicle(vehicle);
      setIsVehicleModalOpen(true);
   };

   const handleStatusChange = (vehicleId: string, newStatus: 'active' | 'inactive') => {
      changeStatusMutation.mutate(
         {
            vehicleId,
            status: newStatus,
         },
         {
            onSuccess: () => {
               toast.success(`Vehicle status changed to ${newStatus}`);
               queryClient.invalidateQueries({ queryKey: ['driver-vehicles', profileId] });
               queryClient.invalidateQueries({ queryKey: ['vehicle-documents', vehicleId] });
               queryClient.invalidateQueries({ queryKey: ['driver', profileId] });
               refetch();
            },
         }
      );
   };

   if (isLoading) {
      return (
         <div className='space-y-4'>
            <div className='flex justify-between items-center'>
               <h3 className='text-lg font-medium'>Driver Vehicles</h3>
               <div className='h-9 w-32 bg-gray-200 rounded animate-pulse'></div>
            </div>
            <div className='space-y-3'>
               {[1, 2].map(i => (
                  <div key={i} className='h-24 bg-gray-200 rounded animate-pulse'></div>
               ))}
            </div>
         </div>
      );
   }

   if (error) {
      return (
         <div className='space-y-4'>
            <div className='flex justify-between items-center'>
               <h3 className='text-lg font-medium'>Driver Vehicles</h3>
               <Button onClick={handleCreateVehicle}>
                  <Plus className='w-4 h-4 mr-2' />
                  Add Vehicle
               </Button>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>Failed to Load Vehicles</h3>
               <p className='text-gray-600 mb-4'>
                  There was an error loading the driver's vehicles.
               </p>
               <Button onClick={() => refetch()} variant='outline'>
                  Try Again
               </Button>
            </Card>
         </div>
      );
   }

   return (
      <div className='space-y-6'>
         <div className='flex justify-between items-center'>
            <div>
               <h3 className='text-lg font-semibold text-gray-900'>Driver Vehicles</h3>
               <p className='text-sm text-gray-600'>
                  Manage driver vehicle information and verification.
               </p>
            </div>
            <Button
               onClick={handleCreateVehicle}
               variant='outline'
               className='bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            >
               <Plus className='w-4 h-4 mr-2' />
               Add Vehicle
            </Button>
         </div>

         {vehicles.length === 0 ? (
            <Card className='p-8 text-center bg-white border border-gray-200'>
               <div className='w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4'>
                  <Car className='w-8 h-8 text-gray-400' />
               </div>
               <h3 className='text-lg font-medium text-gray-900 mb-2'>No Vehicles Added</h3>
               <p className='text-gray-600 mb-6 max-w-md mx-auto'>
                  This driver hasn't added any vehicles yet. Add the first vehicle to get started.
               </p>
               <div className='flex justify-center'>
                  <Button
                     onClick={handleCreateVehicle}
                     variant='outline'
                     className='bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  >
                     <Plus className='w-4 h-4 mr-2' />
                     Add First Vehicle
                  </Button>
               </div>
            </Card>
         ) : (
            <div className='grid grid-cols-1 gap-6 justify-between'>
               {vehicles.map(vehicle => (
                  <Card
                     key={vehicle.id}
                     className='p-6 gap-2 bg-white border border-gray-200 hover:shadow-md transition-shadow'
                  >
                     {/* Vehicle Header */}
                     <div className='flex justify-between mb-4'>
                        <div className='flex items-center gap-3'>
                           <div className='w-10 h-10 rounded-lg bg-gray-50 flex items-center justify-center border border-gray-200'>
                              <Car className='w-5 h-5 text-gray-600' />
                           </div>
                           <div>
                              <div className='flex items-center gap-2'>
                                 <h4 className='font-semibold text-gray-900 text-sm'>
                                    {vehicle.vehicleNumber}
                                 </h4>
                                 <Badge
                                    className={`text-xs px-2 py-1 xl:inline-flex hidden ${
                                       vehicle.isPrimary
                                          ? 'bg-blue-100 text-blue-700 border-blue-200'
                                          : 'bg-gray-100 text-gray-600 border-gray-200'
                                    }`}
                                 >
                                    {vehicle.isPrimary ? 'Primary' : 'Secondary'}
                                 </Badge>
                              </div>
                              <p className='text-xs text-gray-500'>{vehicle.vehicleType.name}</p>
                           </div>
                        </div>

                        <div className='flex items-start gap-2 flex-wrap justify-end'>
                           <Badge
                              className={`text-xs px-2 py-1 xl:hidden ${
                                 vehicle.isPrimary
                                    ? 'bg-blue-100 text-blue-700 border-blue-200'
                                    : 'bg-gray-100 text-gray-600 border-gray-200'
                              }`}
                           >
                              {vehicle.isPrimary ? 'Primary' : 'Secondary'}
                           </Badge>
                           {/* {getVerificationBadge(vehicle)} */}
                           {getOperationalStatusBadge(vehicle)}
                           {/* {vehicle.isNocRequired && vehicle.status !== 'active' && (
                              <Badge
                                 variant='outline'
                                 className='text-xs border-orange-200 text-orange-700 bg-orange-50 whitespace-nowrap'
                              >
                                 NOC Required
                              </Badge>
                           )} */}
                        </div>
                     </div>

                     {vehicle._count.driverVehicleDocuments === 0 ? (
                        /* Not Verified State */
                        <div className='text-center py-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50'>
                           <div className='w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-3'>
                              <Shield className='w-5 h-5 text-blue-500' />
                           </div>
                           <h4 className='text-sm font-medium text-gray-900 mb-2'>
                              Verification Required
                           </h4>
                           <p className='text-xs text-gray-500 mb-3'>
                              Verify vehicle registration details to proceed
                           </p>
                           <Button
                              size='sm'
                              onClick={() => handleVehicleClick(vehicle)}
                              className='gap-1 text-xs px-3 py-1 bg-blue-600 hover:bg-blue-700 cursor-pointer'
                              disabled={verifyVehicleMutation.isPending}
                           >
                              <Shield className='w-3 h-3' />
                              {verifyVehicleMutation.isPending
                                 ? 'Verifying...'
                                 : 'Verify Vehicle Details'}
                           </Button>
                        </div>
                     ) : (
                        /* Verified State */
                        <div className='space-y-4'>
                           {/* Vehicle Details */}
                           <div className='bg-gray-50 rounded-lg p-4 relative'>
                              {/* Action Buttons */}
                              <div className='absolute top-4 right-4 flex gap-2'>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={e => {
                                       e.stopPropagation();
                                       handleEditVehicle(vehicle);
                                    }}
                                    className='border border-gray-300 bg-white text-gray-600 hover:text-gray-900 hover:border-gray-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                 >
                                    Edit
                                 </Button>
                                 <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                       <Button
                                          size='sm'
                                          variant='outline'
                                          disabled={changeStatusMutation.isPending}
                                          className={`text-sm font-medium rounded-md px-3 py-1 transition-colors ${
                                             vehicle.status === 'active'
                                                ? 'border border-red-300 bg-white text-red-600 hover:text-red-700 hover:border-red-400'
                                                : 'border border-green-300 bg-white text-green-600 hover:text-green-700 hover:border-green-400'
                                          }`}
                                       >
                                          {vehicle.status === 'active' ? 'Deactivate' : 'Activate'}
                                       </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                       <AlertDialogHeader>
                                          <AlertDialogTitle>
                                             {vehicle.status === 'active'
                                                ? 'Deactivate'
                                                : 'Activate'}{' '}
                                             Vehicle
                                          </AlertDialogTitle>
                                          <AlertDialogDescription>
                                             Are you sure you want to{' '}
                                             {vehicle.status === 'active'
                                                ? 'deactivate'
                                                : 'activate'}{' '}
                                             this vehicle?
                                             {vehicle.status === 'active'
                                                ? ' This will prevent the driver from using this vehicle.'
                                                : ' This will allow the driver to use this vehicle again.'}
                                          </AlertDialogDescription>
                                       </AlertDialogHeader>
                                       <AlertDialogFooter>
                                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                                          <AlertDialogAction
                                             onClick={() =>
                                                handleStatusChange(
                                                   vehicle.id,
                                                   vehicle.status === 'active'
                                                      ? 'inactive'
                                                      : 'active'
                                                )
                                             }
                                             className={
                                                vehicle.status === 'active'
                                                   ? 'bg-red-600 hover:bg-red-700'
                                                   : 'bg-green-600 hover:bg-green-700'
                                             }
                                          >
                                             {vehicle.status === 'active'
                                                ? 'Deactivate'
                                                : 'Activate'}
                                          </AlertDialogAction>
                                       </AlertDialogFooter>
                                    </AlertDialogContent>
                                 </AlertDialog>
                              </div>

                              {/* Vehicle Info Single Column */}
                              <div className='space-y-2 text-xs pr-40'>
                                 {vehicle.vehicleType.description && (
                                    <div className='space-y-1'>
                                       <span className='text-gray-500 block'>Description</span>
                                       <span className='text-gray-900 font-medium block'>
                                          {vehicle.vehicleType.description}
                                       </span>
                                    </div>
                                 )}
                                 {vehicle.isNocRequired && (
                                    <div className='space-y-1'>
                                       <span className='text-gray-500 block'>NOC Status</span>
                                       <span className='text-orange-700 font-medium block'>Required</span>
                                    </div>
                                 )}
                                 <div className='space-y-1'>
                                    <span className='text-gray-500 block'>Documents</span>
                                    <span className='text-gray-900 font-medium block'>
                                       {vehicle._count.driverVehicleDocuments} uploaded
                                    </span>
                                 </div>
                              </div>
                           </div>
                     </div>
                     )}

                     {/* Documents Accordion */}
                     {vehicle._count.driverVehicleDocuments > 0 && (
                        <Card className='mt-2 py-0 border border-gray-200'>
                           <Accordion
                              type='single'
                              collapsible
                              value={expandedDocuments === vehicle.id ? vehicle.id : undefined}
                              onValueChange={value => setExpandedDocuments(value)}
                           >
                              <AccordionItem value={vehicle.id} className='border-none'>
                                 <AccordionTrigger className='px-4 cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 hover:no-underline'>
                                    {/* Vehicle Documents ({vehicle._count.driverVehicleDocuments}) */}
                                    <div className='flex gap-1.5'>
                                       <h4 className='text-md font-medium'>Vehicle Documents</h4>

                                       <Badge variant='outline' className='text-xs'>
                                          {vehicle._count.driverVehicleDocuments} Documents
                                       </Badge>

                                       {vehicle.isNocRequired && vehicle.status !== 'active' && (
                                          <Badge
                                             variant='outline'
                                             className='text-xs border-orange-200 text-orange-700 bg-orange-50 whitespace-nowrap'
                                          >
                                             NOC Required
                                          </Badge>
                                       )}
                                    </div>
                                 </AccordionTrigger>
                                 <AccordionContent className='px-4 pb-4 pt-0'>
                                    <VehicleDocuments vehicle={vehicle} />
                                 </AccordionContent>
                              </AccordionItem>
                           </Accordion>
                        </Card>
                     )}

                     {/* Products Accordion */}
                     <Card className='mt-2 py-0 border border-gray-200'>
                        <Accordion
                           type='single'
                           collapsible
                           value={expandedProducts === vehicle.id ? vehicle.id : undefined}
                           onValueChange={value => setExpandedProducts(value)}
                        >
                           <AccordionItem value={vehicle.id} className='border-none'>
                              <AccordionTrigger className='px-4 cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 hover:no-underline'>
                                 <div className='flex gap-1.5'>
                                    <h4 className='text-md font-medium'>Vehicle Products</h4>
                                 </div>
                              </AccordionTrigger>
                              <AccordionContent className='px-4 pb-4 pt-0'>
                                 <VehicleProducts
                                    vehicleId={vehicle.id}
                                    driverCityId={driverCityId || ''}
                                    driverCityName={driverCityName || 'Unknown City'}
                                 />
                              </AccordionContent>
                           </AccordionItem>
                        </Accordion>
                     </Card>
                  </Card>
               ))}
            </div>
         )}

         <VehicleModal
            isOpen={isVehicleModalOpen}
            onClose={handleModalClose}
            profileId={profileId}
            vehicle={editingVehicle}
            onVehicleCreated={handleVehicleCreated}
            onVehicleUpdated={handleVehicleUpdated}
         />
      </div>
   );
}

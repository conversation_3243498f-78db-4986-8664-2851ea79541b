'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Dialog,
   DialogClose,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateDriver } from '../api/mutations';
import { useGetDriver } from '../api/queries';
import { useAllCities } from '@/modules/city/api/queries';
import { useListLanguage } from '@/modules/language/api/queries';
import { useQueryClient } from '@tanstack/react-query';
import { formatDateForAPI } from '../utils/driver-utils';

// Define the Zod schema for form validation
const editDriverFormSchema = z.object({
   firstName: z
      .string()
      .min(1, 'First name is required')
      .min(2, 'First name must be at least 2 characters')
      .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters'),
   lastName: z
      .string()
      .min(1, 'Last name is required')
      .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters'),
   gender: z.enum(['MALE', 'FEMALE', 'OTHER'], {
      message: 'Please select gender',
   }),
   dob: z
      .date({
         message: 'Date of birth is required',
      })
      .refine(
         date => {
            const today = new Date();
            const birthDate = new Date(date);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            const dayDiff = today.getDate() - birthDate.getDate();

            // Calculate exact age
            const exactAge = age - (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0) ? 1 : 0);

            return exactAge >= 18;
         },
         {
            message: 'Driver must be at least 18 years old',
         }
      ),
   cityId: z.string().min(1, 'City is required'),
   languageId: z.string().min(1, 'Language is required'),
});

// Infer the type from the schema
type EditDriverFormValues = z.infer<typeof editDriverFormSchema>;

interface EditDriverProps {
   driverId: string | null;
   isOpen: boolean;
   onClose: () => void;
}

export const EditDriver = ({ driverId, isOpen, onClose }: EditDriverProps) => {
   const [dobOpen, setDobOpen] = useState(false);
   const [selectKey, setSelectKey] = useState(0);
   const updateDriverMutation = useUpdateDriver();
   const queryClient = useQueryClient();
   const driverResponse = useGetDriver(driverId);
   const driver = driverResponse?.data?.data;
   const citiesQuery = useAllCities();
   const languagesQuery = useListLanguage({});
   const isLoading = driverResponse.isLoading || citiesQuery.isLoading || languagesQuery.isLoading;

   // Calculate default date (18 years ago)
   const getDefaultDate = () => {
      const today = new Date();
      const eighteenYearsAgo = new Date(
         today.getFullYear() - 18,
         today.getMonth(),
         today.getDate()
      );
      return eighteenYearsAgo;
   };

   const form = useForm<EditDriverFormValues>({
      resolver: zodResolver(editDriverFormSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         gender: 'MALE',
         dob: getDefaultDate(),
         cityId: '',
         languageId: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   // Reset form when driver data is loaded
   useEffect(() => {
      if (driver && isOpen && !isLoading) {
         reset({
            firstName: driver.firstName || '',
            lastName: driver.lastName || '',
            gender: (driver.gender as 'MALE' | 'FEMALE' | 'OTHER') || 'MALE',
            dob: driver.dob ? new Date(driver.dob) : new Date(),
            cityId: driver.cityId || '',
            languageId: driver.languageId || '',
         });
         // Force re-render of Select components to sync with new values
         setSelectKey(prev => prev + 1);
      }
   }, [driver, isLoading, reset, isOpen]);

   const onSubmit = async (data: EditDriverFormValues) => {
      if (!driverId) return;

      updateDriverMutation.mutate(
         {
            id: driverId,
            firstName: data.firstName,
            lastName: data.lastName,
            gender: data.gender,
            dob: formatDateForAPI(data.dob),
            cityId: data.cityId,
            languageId: data.languageId,
         },
         {
            onSuccess: () => {
               toast.success('Driver updated successfully');
               onClose();
               queryClient.invalidateQueries({ queryKey: ['drivers'] });
            },
         }
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-md max-h-[90vh] overflow-y-auto'
         >
            <form className='flex flex-col space-y-4' onSubmit={form.handleSubmit(onSubmit)}>
               <DialogHeader>
                  <DialogTitle>Edit Driver</DialogTitle>
                  <DialogDescription>Update driver information</DialogDescription>
               </DialogHeader>

               <div className='py-4' id='form-container'>
                  {isLoading ? (
                     <div className='flex items-center justify-center py-8'>
                        <div className='text-gray-500'>Loading driver data...</div>
                     </div>
                  ) : (
                     <div className='space-y-4'>
                        <div className='grid grid-cols-2 gap-4'>
                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='firstName'>First Name *</Label>
                              <Controller
                                 control={control}
                                 name='firstName'
                                 render={({ field }) => (
                                    <Input
                                       id='firstName'
                                       placeholder='Enter first name'
                                       {...field}
                                       className='w-full'
                                    />
                                 )}
                              />
                              {errors.firstName && <ErrorMessage error={errors.firstName} />}
                           </div>

                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='lastName'>Last Name *</Label>
                              <Controller
                                 control={control}
                                 name='lastName'
                                 render={({ field }) => (
                                    <Input
                                       id='lastName'
                                       placeholder='Enter last name'
                                       {...field}
                                       className='w-full'
                                    />
                                 )}
                              />
                              {errors.lastName && <ErrorMessage error={errors.lastName} />}
                           </div>
                        </div>

                        <div className='grid grid-cols-2 gap-4'>
                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='gender'>Gender *</Label>
                              <Controller
                                 control={control}
                                 name='gender'
                                 render={({ field }) => (
                                    <Select
                                       key={`gender-${selectKey}`}
                                       onValueChange={field.onChange}
                                       value={field.value}
                                    >
                                       <SelectTrigger className='w-full'>
                                          <SelectValue placeholder='Select gender' />
                                       </SelectTrigger>
                                       <SelectContent>
                                          <SelectItem value='MALE'>Male</SelectItem>
                                          <SelectItem value='FEMALE'>Female</SelectItem>
                                          <SelectItem value='OTHER'>Other</SelectItem>
                                       </SelectContent>
                                    </Select>
                                 )}
                              />
                              {errors.gender && <ErrorMessage error={errors.gender} />}
                           </div>

                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='dob'>Date of Birth *</Label>
                              <Controller
                                 control={control}
                                 name='dob'
                                 render={({ field }) => {
                                    return (
                                       <Popover
                                          modal={true}
                                          open={dobOpen}
                                          onOpenChange={setDobOpen}
                                       >
                                          <PopoverTrigger asChild>
                                             <Button
                                                variant={'outline'}
                                                className={cn(
                                                   'w-full pl-3 text-left font-normal',
                                                   !field.value && 'text-muted-foreground'
                                                )}
                                             >
                                                {field.value ? (
                                                   format(field.value, 'dd/MM/yyyy')
                                                ) : (
                                                   <span>dd/mm/yyyy</span>
                                                )}
                                                <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                                             </Button>
                                          </PopoverTrigger>
                                          <PopoverContent
                                             className='w-auto p-0 z-[9999]'
                                             align='start'
                                          >
                                             <Calendar
                                                mode='single'
                                                selected={field.value}
                                                onSelect={date => {
                                                   field.onChange(date);
                                                   setDobOpen(false);
                                                }}
                                                captionLayout='dropdown'
                                                defaultMonth={field.value || getDefaultDate()}
                                                disabled={date => {
                                                   const today = new Date();
                                                   const eighteenYearsAgo = new Date(
                                                      today.getFullYear() - 18,
                                                      today.getMonth(),
                                                      today.getDate()
                                                   );
                                                   return date > eighteenYearsAgo;
                                                }}
                                                toYear={new Date().getFullYear() - 18}
                                                fromYear={1900}
                                             />
                                          </PopoverContent>
                                       </Popover>
                                    );
                                 }}
                              />
                              {errors.dob && <ErrorMessage error={errors.dob} />}
                           </div>
                        </div>

                        {/* Phone number is not editable - it's verified during registration */}
                        <div className='flex flex-col gap-2'>
                           <Label>Phone Number</Label>
                           <Input
                              value={driver?.phoneNumber || ''}
                              disabled
                              className='w-full bg-gray-50'
                           />
                           <p className='text-xs text-muted-foreground'>
                              Phone number cannot be changed after verification
                           </p>
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label>Email Address</Label>
                           <Input
                              value={driver?.email || 'Not provided'}
                              disabled
                              className='w-full bg-gray-50'
                           />
                           <p className='text-xs text-muted-foreground'>
                              Email cannot be changed after registration
                           </p>
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='cityId'>City *</Label>
                           <Controller
                              control={control}
                              name='cityId'
                              render={({ field }) => (
                                 <Select
                                    key={`city-${selectKey}`}
                                    onValueChange={field.onChange}
                                    value={field.value}
                                 >
                                    <SelectTrigger className='w-full'>
                                       <SelectValue placeholder='Select city' />
                                    </SelectTrigger>
                                    <SelectContent>
                                       {citiesQuery.data?.data
                                          ?.filter(city => city.status === 'active')
                                          .map(city => (
                                             <SelectItem key={city.id} value={city.id}>
                                                {city.name}
                                             </SelectItem>
                                          ))}
                                    </SelectContent>
                                 </Select>
                              )}
                           />
                           {errors.cityId && <ErrorMessage error={errors.cityId} />}
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='languageId'>Language *</Label>
                           <Controller
                              control={control}
                              name='languageId'
                              render={({ field }) => (
                                 <Select
                                    key={`language-${selectKey}`}
                                    onValueChange={field.onChange}
                                    value={field.value}
                                 >
                                    <SelectTrigger className='w-full'>
                                       <SelectValue placeholder='Select language' />
                                    </SelectTrigger>
                                    <SelectContent>
                                       {languagesQuery.data?.data?.map(language => (
                                          <SelectItem key={language.id} value={language.id}>
                                             {language.name}
                                          </SelectItem>
                                       ))}
                                    </SelectContent>
                                 </Select>
                              )}
                           />
                           {errors.languageId && <ErrorMessage error={errors.languageId} />}
                        </div>
                     </div>
                  )}
               </div>

               <DialogFooter className='flex gap-4 pt-4'>
                  <DialogClose asChild>
                     <Button
                        type='button'
                        variant='outline'
                        disabled={updateDriverMutation.isPending}
                     >
                        Cancel
                     </Button>
                  </DialogClose>
                  <Button type='submit' disabled={updateDriverMutation.isPending || isLoading}>
                     {updateDriverMutation.isPending ? 'Updating...' : 'Update Driver'}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
};

export interface Location {
   lat: number;
   lng: number;
   address?: string | null;
}

export interface User {
   id: string;
   phoneNumber?: string | null;
   email?: string | null;
}

export interface UserProfile {
   id: string;
   firstName: string;
   lastName: string;
   profilePictureUrl?: string | null;
   averageRating?: number;
   email?: string | null;
   phoneNumber?: string | null;
   user?: User;
}

export interface VehicleType {
   id: string;
   name: string;
}

export interface DriverVehicle {
   id: string;
   vehicleNumber: string;
   vehicleType: VehicleType;
}

export interface Product {
   id: string;
   name: string;
   description?: string | null;
   icon?: string | null;
}

export interface ReviewBy {
   id: string;
   firstName: string;
   lastName: string;
}

export interface Review {
   id: string;
   rating: number;
   review?: string | null;
   createdAt: string;
   reviewBy: ReviewBy;
}

export interface RideLifecycle {
   id: string;
   status: string;
   location: Location | null;
   createdAt: string;
   meta?: any;
}

export interface RideHistoryItem {
   id: string;
   status: string;
   createdAt: string;
   completedAt?: string | null;
   pickupLocation: Location;
   destinationLocation: Location;
   duration?: number | null;
   distance?: number | null;
   rider: {
      id: string;
      firstName: string;
      lastName: string;
   };
   driver?: {
      id: string;
      firstName: string;
      lastName: string;
   } | null;
   product: {
      id: string;
      name: string;
      icon?: string | null;
   };
}

export interface TieredBreakdown {
   from: number;
   to: number;
   flatFee?: number;
   unitPrice?: number;
   rate?: number;
   tierIndex: number;
   tierAmount: number;
   unitsInTier: number;
}

export interface CalculationBreakdown {
   flatAmount?: number;
   priceModel: string;
   finalAmount: number;
   tieredBreakdown?: TieredBreakdown[];
   totalTieredAmount?: number;
   rate?: number;
   units?: number;
   linearAmount?: number;
}

export interface CommissionCalculationBreakdown {
   afterCaps: number;
   netAmount: number;
   taxAmount: number;
   baseAmount: number;
   beforeCaps: number;
   totalBonuses: number;
   totalWithTax: number;
   capAdjustment: number;
   taxableAmount: number;
   commissionType: string;
   totalDiscounts: number;
   percentageValue?: number;
   percentageAmount?: number;
   finalCommissionAmount: number;
}

export interface AppliedCommission {
   success: boolean;
   baseAmount: number;
   appliedCaps: any[];
   commissionId: string;
   appliedBonuses: any[];
   commissionName: string;
   commissionRate?: number;
   flatAmount?: number;
   commissionType: string;
   appliedDiscounts: any[];
   commissionTaxAmount: number;
   netCommissionAmount: number;
   calculationBreakdown: CommissionCalculationBreakdown;
   finalCommissionAmount: number;
   grossCommissionAmount: number;
   totalCommissionWithTax: number;
   taxCalculation?: {
      taxGroupId: string;
      taxGroupName: string;
      totalTaxAmount: number;
      subcategoryResults: TaxSubcategoryResult[];
   };
}

export interface TaxSubcategoryResult {
   isExempt: boolean;
   taxAmount: number;
   baseAmount: number;
   percentage: number;
   hasOverride: boolean;
   subcategoryId: string;
   subcategoryName: string;
   appliedPercentage: number;
}

export interface AppliedTax {
   success: boolean;
   isActive: boolean;
   baseAmount: number;
   taxGroupId: string;
   hasOverrides: boolean;
   taxGroupName: string;
   hasExemptions: boolean;
   totalTaxAmount: number;
   totalPercentage: string;
   subcategoryResults: TaxSubcategoryResult[];
}

export interface FareBreakdownItem {
   success: boolean;
   chargeId: string;
   chargeName: string;
   chargeType: string;
   priceModel: string;
   baseAmount: number;
   calculatedAmount: number;
   conditionMet: boolean;
   appliedTaxes: AppliedTax[];
   appliedCommission?: AppliedCommission;
   calculationBreakdown: CalculationBreakdown;
   meter?: string;
   meterValue?: number;
}

export interface FareSpec {
   currency: string;
   subtotal: number;
   grandTotal?: number;
   totalTaxes: number;
   passengerFare: number;
   driverEarnings: number;
   platformRevenue: number;
   totalCommissions: number;
   chargeBreakdown: FareBreakdownItem[];
   taxBreakdown?: AppliedTax[];
   commissionBreakdown?: AppliedCommission[];
   calculationSummary?: {
      hasErrors: boolean;
      totalCharges: number;
      totalTaxGroups: number;
      totalCommissions: number;
      totalChargeGroups: number;
   };
   calculatedAt: string;
   cityProductId: string;
   cityProductFareId: string;
}

export interface RideDetails {
   id: string;
   status: string;
   pickupLocation: Location;
   destinationLocation: Location;
   stops?: Location[] | null;
   verificationCode?: string | null;
   createdAt: string;
   completedAt?: string | null;
   otpVerifiedAt?: string | null;
   duration?: number | null;
   actualDuration?: number | null;
   distance?: number | null;
   fareSpec?: FareSpec | null;
   rider: UserProfile;
   driver?: UserProfile | null;
   product: Product;
   driverVehicle?: DriverVehicle | null;
   rideLifecycles: RideLifecycle[];
   reviews: Review[];
}

export interface ListRidesParams {
   page?: number;
   limit?: number;
   status?: string;
   riderName?: string;
   driverName?: string;
   riderId?: string;
   driverId?: string;
   productId?: string;
   fromDate?: string;
   toDate?: string;
   vehicleNumber?: string;
   riderPhoneNumber?: string;
   driverPhoneNumber?: string;
}

export interface ListRidesResponse {
   success: boolean;
   message: string;
   data: RideHistoryItem[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

export interface RideDetailsResponse {
   success: boolean;
   message: string;
   data: RideDetails;
   timestamp: number;
}

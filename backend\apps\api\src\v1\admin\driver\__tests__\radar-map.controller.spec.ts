import { Test, TestingModule } from '@nestjs/testing';
import { DriverController } from '../driver.controller';
import { LocationIngestorService } from '@shared/shared/modules/location/location-ingestor.service';
import { UserProfileService } from '@shared/shared/modules/user-profile/user-profile.service';
import { RadarMapBoundsDto } from '../dto/radar-map-bounds.dto';

describe('DriverController - Radar Map (Optimized)', () => {
  let controller: DriverController;
  let locationIngestorService: LocationIngestorService;

  // Optimized response with minimal data for performance
  const mockRadarMapResponse = {
    drivers: [
      {
        id: 'driver-1',
        lat: 12.9716,
        lng: 77.5946,
        status: 'active',
        cityProductId: 'cp-1',
        productName: 'Standard Ride',
        productIcon: 'https://example.com/icon.png',
      },
      {
        id: 'driver-2',
        lat: 12.9726,
        lng: 77.5956,
        status: 'active',
        cityProductId: 'cp-2',
        productName: 'Premium Ride',
        productIcon: 'https://example.com/icon-premium.png',
      },
    ],
    total: 2,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DriverController],
      providers: [
        {
          provide: LocationIngestorService,
          useValue: {
            getRadarMapDrivers: jest
              .fn()
              .mockResolvedValue(mockRadarMapResponse),
          },
        },
        {
          provide: UserProfileService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<DriverController>(DriverController);
    locationIngestorService = module.get<LocationIngestorService>(
      LocationIngestorService,
    );
  });

  describe('getRadarMapDrivers', () => {
    it('should return drivers with minimal optimized data', async () => {
      const bounds: RadarMapBoundsDto = {
        northeast: { lat: 13.0, lng: 77.6 },
        southwest: { lat: 12.9, lng: 77.5 },
      };

      const result = await controller.getRadarMapDrivers(bounds);

      expect(result).toEqual({
        success: true,
        message: 'Drivers retrieved successfully for radar map view',
        data: mockRadarMapResponse,
        timestamp: expect.any(Number),
      });

      expect(locationIngestorService.getRadarMapDrivers).toHaveBeenCalledWith(
        bounds,
      );
    });

    it('should return empty array when no drivers found', async () => {
      const emptyResponse = { drivers: [], total: 0 };
      jest
        .spyOn(locationIngestorService, 'getRadarMapDrivers')
        .mockResolvedValueOnce(emptyResponse);

      const bounds: RadarMapBoundsDto = {
        northeast: { lat: 13.0, lng: 77.6 },
        southwest: { lat: 12.9, lng: 77.5 },
      };

      const result = await controller.getRadarMapDrivers(bounds);

      expect(result.data).toEqual(emptyResponse);
      expect(result.data.total).toBe(0);
    });

    it('should include only essential driver fields', async () => {
      const bounds: RadarMapBoundsDto = {
        northeast: { lat: 13.0, lng: 77.6 },
        southwest: { lat: 12.9, lng: 77.5 },
      };

      const result = await controller.getRadarMapDrivers(bounds);

      const driver = result.data.drivers[0];
      expect(driver).toHaveProperty('id');
      expect(driver).toHaveProperty('lat');
      expect(driver).toHaveProperty('lng');
      expect(driver).toHaveProperty('status');
      expect(driver).toHaveProperty('cityProductId');
      expect(driver).toHaveProperty('productName');
      expect(driver).toHaveProperty('productIcon');

      // Verify no unnecessary fields
      expect(driver).not.toHaveProperty('firstName');
      expect(driver).not.toHaveProperty('lastName');
      expect(driver).not.toHaveProperty('phoneNumber');
      expect(driver).not.toHaveProperty('profilePictureUrl');
    });

    it('should return product name and icon for each driver', async () => {
      const bounds: RadarMapBoundsDto = {
        northeast: { lat: 13.0, lng: 77.6 },
        southwest: { lat: 12.9, lng: 77.5 },
      };

      const result = await controller.getRadarMapDrivers(bounds);

      result.data.drivers.forEach((driver) => {
        expect(driver.productName).toBeDefined();
        expect(typeof driver.productName).toBe('string');
        expect(driver.productIcon).toBeDefined();
      });
    });

    it('should handle multiple drivers efficiently', async () => {
      const manyDrivers = {
        drivers: Array.from({ length: 100 }, (_, i) => ({
          id: `driver-${i}`,
          lat: 12.9 + i * 0.001,
          lng: 77.5 + i * 0.001,
          status: 'active',
          cityProductId: `cp-${i}`,
          productName: `Product ${i}`,
          productIcon: `https://example.com/icon-${i}.png`,
        })),
        total: 100,
      };

      jest
        .spyOn(locationIngestorService, 'getRadarMapDrivers')
        .mockResolvedValueOnce(manyDrivers);

      const bounds: RadarMapBoundsDto = {
        northeast: { lat: 13.0, lng: 77.6 },
        southwest: { lat: 12.9, lng: 77.5 },
      };

      const result = await controller.getRadarMapDrivers(bounds);

      expect(result.data.total).toBe(100);
      expect(result.data.drivers.length).toBe(100);
    });
  });
});

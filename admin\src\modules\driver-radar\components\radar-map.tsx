'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { GoogleMap } from '@react-google-maps/api';
import { toast } from '@/lib/toast';
import { useRadarMapDrivers } from '../api/queries';
import { DriverMarker } from './driver-marker';
import { DriverInfoPopover } from './driver-info-popover';
import { PollingIndicator } from './polling-indicator';
import { MapControls } from './map-controls';
import { LocationSearch } from './location-search';
import {
   KOCHI_CENTER,
   MAP_CONSTANTS,
   RadarDriver,
   RadarMapBounds,
   Coordinate,
} from '../types/radar';
import { calculateBoundsFromCenter, getCenterFromMap } from '../utils/map-utils';

const mapContainerStyle = {
   width: '100%',
   height: '100%',
};

const mapOptions: google.maps.MapOptions = {
   gestureHandling: 'greedy',
   disableDefaultUI: true,
   zoomControl: true,
   mapTypeControl: false,
   streetViewControl: false,
   fullscreenControl: false,
   minZoom: MAP_CONSTANTS.MIN_ZOOM,
   maxZoom: MAP_CONSTANTS.MAX_ZOOM,
};

export const RadarMap: React.FC = () => {
   const mapRef = useRef<google.maps.Map | null>(null);
   const circleRef = useRef<google.maps.Circle | null>(null);
   const [mapBounds, setMapBounds] = useState<RadarMapBounds | null>(null);
   const [mapCenter, setMapCenter] = useState<Coordinate | null>(null);
   const [isPollingEnabled, setIsPollingEnabled] = useState(true);
   const [selectedDriver, setSelectedDriver] = useState<RadarDriver | null>(null);
   const [selectedPosition, setSelectedPosition] = useState<Coordinate | null>(null);
   const [nextUpdate, setNextUpdate] = useState<number>(
      Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS
   );

   // Update bounds based on current map center
   // This should be called periodically, not on every drag
   const updateBoundsFromMapCenter = useCallback(() => {
      if (!mapRef.current) return;

      const center = getCenterFromMap(mapRef.current);
      if (!center) return;

      // Calculate fixed radius bounds from current center
      const newBounds = calculateBoundsFromCenter(center, MAP_CONSTANTS.MAX_RADIUS_KM);
      setMapBounds(newBounds);
      setMapCenter(center);
   }, []);

   // Fetch driver locations with polling
   const { data, isFetching, refetch } = useRadarMapDrivers(mapBounds, isPollingEnabled);

   // Initialize map and set initial bounds
   const onLoad = useCallback((map: google.maps.Map) => {
      mapRef.current = map;

      // Set initial center and zoom to Kochi
      map.setCenter({ lat: KOCHI_CENTER.lat, lng: KOCHI_CENTER.lng });
      map.setZoom(KOCHI_CENTER.zoom);

      // Set initial bounds and center for Kochi
      const initialCenter = { lat: KOCHI_CENTER.lat, lng: KOCHI_CENTER.lng };
      const initialBounds = calculateBoundsFromCenter(initialCenter, MAP_CONSTANTS.MAX_RADIUS_KM);
      setMapBounds(initialBounds);
      setMapCenter(initialCenter);
   }, []);

   // Handle zoom changes - enforce min zoom and warn user
   const handleZoomChanged = useCallback(() => {
      if (!mapRef.current) return;

      const currentZoom = mapRef.current.getZoom();
      if (currentZoom !== undefined && currentZoom < MAP_CONSTANTS.MIN_ZOOM) {
         toast.error('Zoom limit reached. Maximum area is restricted to reduce server load.');
         mapRef.current.setZoom(MAP_CONSTANTS.MIN_ZOOM);
      }
   }, []);

   // Handle map drag/pan - update bounds to follow current view
   const handleDragEnd = useCallback(() => {
      updateBoundsFromMapCenter();
   }, [updateBoundsFromMapCenter]);

   // Manage circle overlay lifecycle
   useEffect(() => {
      if (!mapRef.current || !mapCenter) return;

      // Remove old circle if it exists
      if (circleRef.current) {
         circleRef.current.setMap(null);
         circleRef.current = null;
      }

      // Create new circle with current center and radius
      const circle = new google.maps.Circle({
         center: { lat: mapCenter.lat, lng: mapCenter.lng },
         radius: MAP_CONSTANTS.MAX_RADIUS_KM * 1000, // Convert km to meters
         strokeColor: '#10B981',
         strokeOpacity: 0.7,
         strokeWeight: 2,
         fillColor: '#10B981',
         fillOpacity: 0.08,
         clickable: false,
         draggable: false,
         editable: false,
         map: mapRef.current,
      });

      circleRef.current = circle;

      // Cleanup on unmount
      return () => {
         if (circleRef.current) {
            circleRef.current.setMap(null);
            circleRef.current = null;
         }
      };
   }, [mapCenter]);

   // Update last updated timestamp and bounds when data changes
   useEffect(() => {
      if (data) {
         setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);

         // Update bounds from current map center for next poll
         // This ensures we always fetch data for wherever the user has navigated
         updateBoundsFromMapCenter();
      }
   }, [data, updateBoundsFromMapCenter]);

   // Handle driver marker click
   const handleDriverClick = useCallback((driver: RadarDriver) => {
      setSelectedDriver(driver);
      setSelectedPosition({ lat: driver.lat, lng: driver.lng });
   }, []);

   // Handle popover close
   const handlePopoverClose = useCallback(() => {
      setSelectedDriver(null);
      setSelectedPosition(null);
   }, []);

   // Manual refresh
   const handleManualRefresh = useCallback(() => {
      // Update bounds from current map position before refetching
      updateBoundsFromMapCenter();
      refetch();
      setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
      toast.success('Driver locations refreshed');
   }, [refetch, updateBoundsFromMapCenter]);

   // Toggle polling
   const handleTogglePolling = useCallback(() => {
      setIsPollingEnabled(prev => {
         const newState = !prev;
         toast.info(newState ? 'Auto-refresh enabled' : 'Auto-refresh paused');
         if (newState) {
            setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
         }
         return newState;
      });
   }, []);

   // Handle location search
   const handleLocationSelect = useCallback(
      (place: google.maps.places.PlaceResult) => {
         if (!mapRef.current || !place.geometry?.location) return;

         const location = place.geometry.location;
         const lat = location.lat();
         const lng = location.lng();

         // Pan to the selected location
         mapRef.current.panTo({ lat, lng });

         // Set zoom level based on the type of place
         if (place.geometry.viewport) {
            mapRef.current.fitBounds(place.geometry.viewport);
         } else {
            mapRef.current.setZoom(15);
         }

         // Update bounds for the new center
         updateBoundsFromMapCenter();

         toast.success(`Moved to ${place.formatted_address || place.name}`);
      },
      [updateBoundsFromMapCenter]
   );

   // Pause polling when tab is inactive
   useEffect(() => {
      const handleVisibilityChange = () => {
         if (document.visibilityState === 'hidden') {
            // Tab is inactive - polling is automatically paused by React Query
         } else {
            // Tab is active again - trigger a refresh
            if (isPollingEnabled) {
               refetch();
               setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
            }
         }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
   }, [isPollingEnabled, refetch]);

   const drivers = data?.data?.drivers || [];

   return (
      <div className='relative w-full h-full'>
         <GoogleMap
            mapContainerStyle={mapContainerStyle}
            onLoad={onLoad}
            onZoomChanged={handleZoomChanged}
            onDragEnd={handleDragEnd}
            options={mapOptions}
         >
            {/* Render driver markers */}
            {drivers.map(driver => (
               <DriverMarker key={driver.id} driver={driver} onClick={handleDriverClick} />
            ))}

            {/* Driver info popover */}
            <DriverInfoPopover
               driver={selectedDriver}
               isOpen={!!selectedDriver}
               onClose={handlePopoverClose}
               position={selectedPosition}
            />
         </GoogleMap>

         {/* Location Search */}
         <div className='absolute top-4 left-1/2 -translate-x-1/2 z-10'>
            <LocationSearch onLocationSelect={handleLocationSelect} />
         </div>

         {/* Polling indicator */}
         <PollingIndicator
            isFetching={isFetching}
            nextUpdate={nextUpdate}
            onManualRefresh={handleManualRefresh}
            isPaused={!isPollingEnabled}
            onTogglePolling={handleTogglePolling}
         />

         {/* Map controls */}
         <MapControls drivers={drivers} />
      </div>
   );
};

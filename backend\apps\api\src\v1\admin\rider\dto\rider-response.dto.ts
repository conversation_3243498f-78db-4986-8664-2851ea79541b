import { ApiProperty } from '@nestjs/swagger';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

export class RiderUserDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+************',
  })
  phoneNumber!: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
    nullable: true,
  })
  email?: string | null;

  @ApiProperty({
    description: 'Phone verification status',
    example: '2024-01-15T10:30:00Z',
    nullable: true,
  })
  phoneVerifiedAt?: Date | null;

  @ApiProperty({
    description: 'Email verification status',
    example: '2024-01-15T10:30:00Z',
    nullable: true,
  })
  emailVerifiedAt?: Date | null;

  @ApiProperty({
    description: 'User creation date',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt!: Date;
}

export class RiderCityDto {
  @ApiProperty({
    description: 'City ID',
    example: 'city-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'City name',
    example: 'Mumbai',
  })
  name!: string;

  @ApiProperty({
    description: 'City code',
    example: 'MUM',
  })
  code!: string;
}

export class RiderResponseDto {
  @ApiProperty({
    description: 'Rider profile ID',
    example: 'profile-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'First name',
    example: 'John',
  })
  firstName!: string;

  @ApiProperty({
    description: 'Last name',
    example: 'Doe',
  })
  lastName!: string;

  @ApiProperty({
    description: 'Full name (computed)',
    example: 'John Doe',
  })
  fullName!: string;

  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/profile.jpg',
    nullable: true,
  })
  profilePictureUrl?: string | null;

  @ApiProperty({
    description: 'Gender',
    example: 'male',
    nullable: true,
  })
  gender?: string | null;

  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-15',
    nullable: true,
  })
  dob?: Date | null;

  @ApiProperty({
    description: 'Referral code',
    example: 'REF123456',
  })
  referralCode!: string;

  @ApiProperty({
    description: 'Rider status',
    enum: UserProfileStatus,
    example: UserProfileStatus.ACTIVE,
  })
  status!: UserProfileStatus;

  @ApiProperty({
    description: 'Profile creation date',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Profile last update date',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt!: Date;

  @ApiProperty({
    description: 'Associated user details',
    type: RiderUserDto,
  })
  user!: RiderUserDto;

  @ApiProperty({
    description: 'Associated city details',
    type: RiderCityDto,
    nullable: true,
  })
  city?: RiderCityDto | null;
}

export class RiderListResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Riders retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'List of riders',
    type: [RiderResponseDto],
  })
  data!: RiderResponseDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    example: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
    },
  })
  meta!: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}

export class RiderDetailsResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Rider details retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Rider details',
    type: RiderResponseDto,
  })
  data!: RiderResponseDto;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}

'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useState } from 'react';
import { useListRiders } from '../api/queries';
import { RiderFilters } from '../components/rider-filters';
import { RiderTable } from '../components/rider-table';

export function RiderPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [name, setName] = useState<string | undefined>(undefined);
  const [email, setEmail] = useState<string | undefined>(undefined);
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);
  const [cityId, setCityId] = useState<string | undefined>(undefined);
  const [status, setStatus] = useState<string | undefined>(undefined);

  // Reset to first page when filters change
  const handleNameChange = (value: string | undefined) => {
    setName(value);
    setPage(1);
  };

  const handleEmailChange = (value: string | undefined) => {
    setEmail(value);
    setPage(1);
  };

  const handlePhoneChange = (value: string | undefined) => {
    setPhoneNumber(value);
    setPage(1);
  };

  const handleCityChange = (value: string | undefined) => {
    setCityId(value);
    setPage(1);
  };

  const handleStatusChange = (value: string | undefined) => {
    setStatus(value);
    setPage(1);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setName(undefined);
    setEmail(undefined);
    setPhoneNumber(undefined);
    setCityId(undefined);
    setStatus(undefined);
    setPage(1);
  };

  const listQuery = useListRiders({
    page,
    limit,
    name: name || undefined,
    email: email || undefined,
    phoneNumber: phoneNumber || undefined,
    cityId: cityId || undefined,
    status: status as any,
  });

  // Check if any filters are active
  const hasActiveFilters = !!name || !!email || !!phoneNumber || !!cityId || !!status;

  return (
    <div className='flex flex-1 flex-col gap-4 p-6'>
      <div className='flex justify-between items-center'>
        <div className='flex items-center gap-3'>
          <h2 className='text-2xl font-semibold text-gray-900'>Riders</h2>
          {listQuery.data?.meta && (
            <Badge variant='secondary' className='text-sm'>
              {listQuery.data.meta.total} Total
            </Badge>
          )}
        </div>
      </div>

      <Card className='overflow-hidden py-4 px-4 rounded-sm'>
        <RiderFilters
          name={name}
          email={email}
          phoneNumber={phoneNumber}
          cityId={cityId}
          status={status}
          onNameChange={handleNameChange}
          onEmailChange={handleEmailChange}
          onPhoneChange={handlePhoneChange}
          onCityChange={handleCityChange}
          onStatusChange={handleStatusChange}
          isLoading={listQuery.isFetching && !listQuery.isLoading}
        />

        <RiderTable
          data={listQuery.data}
          isLoading={listQuery.isLoading}
          currentPage={page}
          onPageChange={(newPage: number) => setPage(newPage)}
          hasFilters={hasActiveFilters}
          onClearFilters={handleClearFilters}
        />
      </Card>
    </div>
  );
}

// Zone type interface for API responses
export interface ZoneType {
  id: string;
  name: string;
  description?: string | null;
  algorithm: ZoneAlgorithm;
  config?: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  zonesCount?: number;
}

// Zone algorithm enum values
export type ZoneAlgorithm = 'CITY' | 'AIRPORT' | 'HIGHWAY' | 'SUBURBAN' | 'RURAL';

// API response structure for listing zone types
export interface ListZoneTypeResponse {
  success: boolean;
  message: string;
  data: {
    data: ZoneType[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  timestamp: number;
}

// API response structure for single zone type
export interface ZoneTypeResponse {
  success: boolean;
  message: string;
  data: ZoneType;
  timestamp: number;
}

// API response structure for algorithms
export interface AlgorithmsResponse {
  success: boolean;
  message: string;
  data: ZoneAlgorithm[];
  timestamp: number;
}

// Request for creating zone type
export interface CreateZoneTypeRequest {
  name: string;
  description?: string;
  algorithm: ZoneAlgorithm;
  config?: any;
}

// Request for updating zone type
export interface UpdateZoneTypeRequest {
  name?: string;
  description?: string;
  algorithm?: ZoneAlgorithm;
  config?: any;
}

// Parameters for listing zone types with pagination
export interface ListZoneTypeParams {
  page?: number;
  limit?: number;
  search?: string;
  algorithm?: ZoneAlgorithm;
  isActive?: boolean;
  isInactive?: boolean;
  includeInactive?: boolean;
  includeZoneCount?: boolean;
  sortBy?: string;
}

// Dropdown option interface for algorithms
export interface AlgorithmOption {
  value: ZoneAlgorithm;
  label: string;
}
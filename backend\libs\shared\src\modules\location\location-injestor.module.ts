//write location-ingestor.module.ts
import { Module } from '@nestjs/common';
import { LocationIngestorService } from './location-ingestor.service';
import { CellDriverRepository } from '@shared/shared/repositories/cell-driver.repository';
import {
  DriverCellMappingRepository,
  DriverMetadataRepository,
} from '@shared/shared/repositories';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { RedisModule } from '@shared/shared/database/redis/redis.module';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
@Module({
  imports: [RedisModule],
  providers: [
    LocationIngestorService,
    CellDriverRepository,
    DriverCellMappingRepository,
    DriverMetadataRepository,
    H3UtilityService,
    PrismaService,
  ],
  exports: [LocationIngestorService],
})
export class LocationIngestorModule {}

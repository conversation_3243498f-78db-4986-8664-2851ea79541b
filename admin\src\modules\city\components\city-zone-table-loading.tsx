export function CityZoneTableLoading() {
   return (
      <div className='space-y-3'>
         {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className='flex items-center space-x-4 p-4'>
               {/* Zone Name */}
               <div className='flex-1 space-y-2'>
                  <div className='h-4 w-32 bg-gray-200 rounded animate-pulse' />
                  <div className='h-3 w-24 bg-gray-200 rounded animate-pulse' />
               </div>
               {/* Zone Type */}
               <div className='space-y-1'>
                  <div className='h-6 w-16 bg-gray-200 rounded animate-pulse' />
                  <div className='h-3 w-12 bg-gray-200 rounded animate-pulse' />
               </div>
               {/* Polygon */}
               <div className='flex items-center space-x-2'>
                  <div className='h-6 w-16 bg-gray-200 rounded animate-pulse' />
                  <div className='h-7 w-7 bg-gray-200 rounded animate-pulse' />
               </div>
               {/* Created */}
               <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
               {/* Actions */}
               <div className='flex items-center space-x-2'>
                  <div className='h-8 w-12 bg-gray-200 rounded animate-pulse' />
                  <div className='h-8 w-12 bg-gray-200 rounded animate-pulse' />
                  <div className='h-8 w-8 bg-gray-200 rounded animate-pulse' />
               </div>
            </div>
         ))}
      </div>
   );
}
import { ApiProperty } from '@nestjs/swagger';
import { CountryResponseDto } from '../../country/dto/country-response.dto';

export class KycDocumentResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  countryId!: string;

  @ApiProperty({ example: 'Aadhaar Card' })
  name!: string;

  @ApiProperty({ example: 'aadhaar_card' })
  identifier!: string;

  @ApiProperty({
    example: { fields: ['aadhaar_number', 'name', 'address'] },
    required: false,
    nullable: true,
  })
  requiredFields?: any | null;

  @ApiProperty({ example: true })
  isMandatory!: boolean;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;

  @ApiProperty({ type: CountryResponseDto, required: false })
  country?: CountryResponseDto;
}

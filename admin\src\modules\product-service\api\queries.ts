import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   ProductServiceResponse,
   ListProductServiceParams,
   ListProductServiceResponse,
} from '../types/product-service';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListProductService = ({
   page,
   limit,
   sortBy,
   sortOrder,
}: ListProductServiceParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['product-services', page, limit, sortBy, sortOrder],
      refetchOnWindowFocus: false,
      enabled: hasPermission(RBAC_PERMISSIONS.PRODUCT_SERVICE.LIST),
      queryFn: (): Promise<ListProductServiceResponse> => {
         return apiClient.get('/product-services', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetProductService = (id: string | null) => {
   return useQuery({
      queryKey: ['product-service', id],
      queryFn: (): Promise<ProductServiceResponse> => {
         return apiClient.get(`/product-services/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

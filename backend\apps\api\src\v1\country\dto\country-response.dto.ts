import { ApiProperty } from '@nestjs/swagger';

export class CountryResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'India' })
  name!: string;

  @ApiProperty({ example: 'IN' })
  iso2!: string;

  @ApiProperty({ example: 'IND', required: false, nullable: true })
  iso3?: string | null;

  @ApiProperty({ example: '+91', required: false, nullable: true })
  phoneCode?: string | null;

  @ApiProperty({ example: 'INR', required: false, nullable: true })
  currency?: string | null;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;
}

import { Card } from '@/components/ui/card';
import { formatStatus, getStatusBadgeClass, formatDuration, formatDistance } from '../utils/ride-formatters';

interface RideOverviewCardProps {
   status: string;
   actualDuration: number | null | undefined;
   distance: number | null | undefined;
   verificationCode?: string | null;
}

export function RideOverviewCard({
   status,
   actualDuration,
   distance,
   verificationCode,
}: RideOverviewCardProps) {
   return (
      <Card className='p-4 rounded-sm'>
         <div className='flex items-center justify-between mb-2'>
            <h3 className='text-base font-semibold text-gray-900'>Ride Overview</h3>
            <span
               className={`inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium border ${getStatusBadgeClass(
                  status
               )}`}
            >
               {formatStatus(status)}
            </span>
         </div>

         <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
               <div className='text-gray-500 text-xs mb-1'>Duration</div>
               <div className='text-gray-900 font-semibold'>{formatDuration(actualDuration)}</div>
            </div>
            <div>
               <div className='text-gray-500 text-xs mb-1'>Distance</div>
               <div className='text-gray-900 font-semibold'>{formatDistance(distance)}</div>
            </div>
            {verificationCode && (
               <div className='col-span-2'>
                  <div className='text-gray-500 text-xs mb-1'>Code</div>
                  <div className='text-gray-900 font-bold tracking-wider'>{verificationCode}</div>
               </div>
            )}
         </div>
      </Card>
   );
}

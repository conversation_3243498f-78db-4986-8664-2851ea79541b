import { Location } from '../types/book-ride';

/**
 * Reverse geocode a coordinate to get a human-readable address
 * @param lat Latitude
 * @param lng Longitude
 * @returns Promise<string | null> - The formatted address or null if failed
 */
export const reverseGeocode = async (
   lat: number,
   lng: number
): Promise<string | null> => {
   try {
      // Check if Google Maps API is loaded
      if (!window.google || !window.google.maps) {
         console.error('Google Maps API not loaded');
         return null;
      }

      const geocoder = new google.maps.Geocoder();

      return new Promise(resolve => {
         geocoder.geocode(
            {
               location: { lat, lng },
            },
            (results, status) => {
               if (status === 'OK' && results && results.length > 0) {
                  // Return the formatted address of the first result
                  resolve(results[0].formatted_address);
               } else {
                  console.error('Geocoding failed:', status);
                  resolve(null);
               }
            }
         );
      });
   } catch (error) {
      console.error('Error during reverse geocoding:', error);
      return null;
   }
};

/**
 * Create a Location object from coordinates and address
 * @param lat Latitude
 * @param lng Longitude
 * @param address Optional address string
 * @returns Location object
 */
export const createLocation = (
   lat: number,
   lng: number,
   address?: string
): Location => {
   return {
      lat,
      lng,
      address,
   };
};

/**
 * Format coordinates as a fallback address
 * @param lat Latitude
 * @param lng Longitude
 * @returns Formatted coordinate string
 */
export const formatCoordinates = (lat: number, lng: number): string => {
   return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
};

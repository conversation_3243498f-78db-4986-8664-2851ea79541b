'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { MapPin, ChevronRight } from 'lucide-react';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { Location } from '../types/ride';

interface RideTripDetailsCardProps {
   pickupLocation: Location;
   destinationLocation: Location;
   stops?: Location[] | null;
}

export function RideTripDetailsCard({
   pickupLocation,
   destinationLocation,
   stops,
}: RideTripDetailsCardProps) {
   const [locationsModalOpen, setLocationsModalOpen] = useState(false);

   return (
      <Card className='p-4 rounded-sm'>
         <div className='flex items-center justify-between mb-2'>
            <h3 className='text-base font-semibold text-gray-900'>Trip details</h3>
            {stops && stops.length > 0 && (
               <Dialog open={locationsModalOpen} onOpenChange={setLocationsModalOpen}>
                  <DialogTrigger asChild>
                     <button className='text-sm cursor-pointer text-blue-600 hover:text-blue-700 flex items-center gap-1'>
                        View all stops
                        <ChevronRight className='w-4 h-4' />
                     </button>
                  </DialogTrigger>
                  <DialogContent className='max-w-2xl max-h-[80vh] overflow-y-auto'>
                     <DialogHeader>
                        <DialogTitle>All Locations</DialogTitle>
                     </DialogHeader>
                     <div className='space-y-3 mt-4'>
                        {/* Pickup */}
                        <div className='border rounded-md p-3'>
                           <div className='flex items-center gap-2 mb-2'>
                              <div className='w-2 h-2 rounded-full bg-green-500'></div>
                              <div className='text-xs font-semibold text-gray-500'>Pickup</div>
                           </div>
                           <div className='text-sm font-medium text-gray-900 mb-1'>
                              {pickupLocation.address || 'N/A'}
                           </div>
                           <div className='text-xs text-gray-500'>
                              Lat: {pickupLocation.lat.toFixed(6)}, Long: {pickupLocation.lng.toFixed(6)}
                           </div>
                        </div>

                        {/* All Stops */}
                        {stops.map((stop, index) => (
                           <div key={index} className='border rounded-md p-3'>
                              <div className='flex items-center gap-2 mb-2'>
                                 <div className='w-2 h-2 rounded-full bg-blue-500'></div>
                                 <div className='text-xs font-semibold text-gray-500'>
                                    Stop {index + 1}
                                 </div>
                              </div>
                              <div className='text-sm font-medium text-gray-900 mb-1'>
                                 {stop.address || 'N/A'}
                              </div>
                              <div className='text-xs text-gray-500'>
                                 Lat: {stop.lat.toFixed(6)}, Long: {stop.lng.toFixed(6)}
                              </div>
                           </div>
                        ))}

                        {/* Destination */}
                        <div className='border rounded-md p-3'>
                           <div className='flex items-center gap-2 mb-2'>
                              <div className='w-2 h-2 rounded-full bg-red-500'></div>
                              <div className='text-xs font-semibold text-gray-500'>Destination</div>
                           </div>
                           <div className='text-sm font-medium text-gray-900 mb-1'>
                              {destinationLocation.address || 'N/A'}
                           </div>
                           <div className='text-xs text-gray-500'>
                              Lat: {destinationLocation.lat.toFixed(6)}, Long: {destinationLocation.lng.toFixed(6)}
                           </div>
                        </div>
                     </div>
                  </DialogContent>
               </Dialog>
            )}
         </div>

         <div className='space-y-3 text-sm'>
            {/* Pickup Location */}
            <div className='flex items-start gap-3'>
               <MapPin className='w-4 h-4 text-green-500 mt-0.5 flex-shrink-0' />
               <div className='flex-1 min-w-0'>
                  <div className='text-xs text-gray-500 mb-0.5'>Pickup</div>
                  <div className='text-gray-900 font-medium truncate'>
                     {pickupLocation.address || 'N/A'}
                  </div>
                  <div className='text-xs text-gray-500 mt-1'>
                     Lat: {pickupLocation.lat.toFixed(6)}, Long: {pickupLocation.lng.toFixed(6)}
                  </div>
               </div>
            </div>

            {/* Destination Location */}
            <div className='flex items-start gap-3'>
               <MapPin className='w-4 h-4 text-red-500 mt-0.5 flex-shrink-0' />
               <div className='flex-1 min-w-0'>
                  <div className='text-xs text-gray-500 mb-0.5'>Destination</div>
                  <div className='text-gray-900 font-medium truncate'>
                     {destinationLocation.address || 'N/A'}
                  </div>
                  <div className='text-xs text-gray-500 mt-1'>
                     Lat: {destinationLocation.lat.toFixed(6)}, Long: {destinationLocation.lng.toFixed(6)}
                  </div>
               </div>
            </div>
         </div>
      </Card>
   );
}

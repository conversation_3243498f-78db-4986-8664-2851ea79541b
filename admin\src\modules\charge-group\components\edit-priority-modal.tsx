'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useUpdateChargePriority } from '../api/charge-mutations';

interface EditPriorityModalProps {
  chargeGroupId: string;
  chargeGroupChargeId: string | null;
  currentPriority: number;
  chargeName: string;
  isOpen: boolean;
  onClose: () => void;
}

export function EditPriorityModal({
  chargeGroupId,
  chargeGroupChargeId,
  currentPriority,
  chargeName,
  isOpen,
  onClose,
}: EditPriorityModalProps) {
  const [priority, setPriority] = useState<string>(String(currentPriority));

  const queryClient = useQueryClient();
  const updatePriorityMutation = useUpdateChargePriority();

  // Update priority when currentPriority changes
  useEffect(() => {
    if (isOpen) {
      setPriority(String(currentPriority));
    }
  }, [isOpen, currentPriority]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!chargeGroupChargeId) {
      toast.error('Invalid charge relationship');
      return;
    }

    const priorityNum = parseInt(priority, 10);
    if (isNaN(priorityNum)) {
      toast.error('Priority must be a valid number');
      return;
    }

    updatePriorityMutation.mutate(
      {
        chargeGroupChargeId,
        priority: priorityNum,
      },
      {
        onSuccess: () => {
          toast.success('Priority updated successfully');
          queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
          onClose();
        },
        onError: (error: any) => {
          toast.error(error?.message || 'Failed to update priority');
        },
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[400px]'>
        <DialogHeader>
          <DialogTitle>Edit Priority</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className='space-y-4 py-4'>
            <div className='space-y-2'>
              <p className='text-sm text-gray-600'>
                Editing priority for: <span className='font-medium'>{chargeName}</span>
              </p>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='priority'>Priority</Label>
              <Input
                id='priority'
                type='number'
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                placeholder='Enter priority (e.g., 0, 1, 2...)'
              />
              <p className='text-xs text-gray-500'>
                Lower priority values are executed first
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={updatePriorityMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={updatePriorityMutation.isPending}
            >
              {updatePriorityMutation.isPending ? (
                <>
                  <Spinner className='h-4 w-4 mr-2' />
                  Updating...
                </>
              ) : (
                'Update Priority'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

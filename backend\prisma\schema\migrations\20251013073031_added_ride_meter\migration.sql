-- AlterTable
ALTER TABLE "rides" ADD COLUMN     "city_product_id" UUID,
ADD COLUMN     "fare_spec" JSONB;

-- CreateTable
CREATE TABLE "ride_fares" (
    "id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "charge_id" UUID,
    "fare" DECIMAL(10,2) NOT NULL,
    "tax_id" UUID,
    "commission_id" UUID,
    "total_fare" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "city_product_id" UUID,
    "city_product_fare_id" UUID,
    "subtotal" DECIMAL(10,2) NOT NULL,
    "total_taxes" DECIMAL(10,2) NOT NULL,
    "total_commissions" DECIMAL(10,2) NOT NULL,
    "passenger_fare" DECIMAL(10,2) NOT NULL,
    "driver_earnings" DECIMAL(10,2) NOT NULL,
    "platform_revenue" DECIMAL(10,2) NOT NULL,
    "calculated_at" TIMESTAMPTZ NOT NULL,
    "fare_breakdown" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "ride_fares_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ride_meters" (
    "id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "unit" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "ride_meters_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_ride_fare_ride_id" ON "ride_fares"("ride_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_charge_id" ON "ride_fares"("charge_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_tax_id" ON "ride_fares"("tax_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_commission_id" ON "ride_fares"("commission_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_city_product_id" ON "ride_fares"("city_product_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_city_product_fare_id" ON "ride_fares"("city_product_fare_id");

-- CreateIndex
CREATE INDEX "idx_ride_fare_calculated_at" ON "ride_fares"("calculated_at");

-- CreateIndex
CREATE INDEX "idx_ride_fare_created_at" ON "ride_fares"("created_at");

-- CreateIndex
CREATE INDEX "idx_ride_meter_ride_id" ON "ride_meters"("ride_id");

-- CreateIndex
CREATE INDEX "idx_ride_meter_name" ON "ride_meters"("name");

-- CreateIndex
CREATE INDEX "idx_ride_meter_ride_name" ON "ride_meters"("ride_id", "name");

-- CreateIndex
CREATE INDEX "idx_ride_meter_created_at" ON "ride_meters"("created_at");

-- CreateIndex
CREATE INDEX "idx_ride_city_product_id" ON "rides"("city_product_id");

-- AddForeignKey
ALTER TABLE "rides" ADD CONSTRAINT "rides_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_fares" ADD CONSTRAINT "ride_fares_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_fares" ADD CONSTRAINT "ride_fares_charge_id_fkey" FOREIGN KEY ("charge_id") REFERENCES "charges"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_fares" ADD CONSTRAINT "ride_fares_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_fares" ADD CONSTRAINT "ride_fares_city_product_fare_id_fkey" FOREIGN KEY ("city_product_fare_id") REFERENCES "city_product_fares"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_meters" ADD CONSTRAINT "ride_meters_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

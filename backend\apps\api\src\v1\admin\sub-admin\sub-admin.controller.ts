import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
  Query,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AdminService } from '@shared/shared/modules/admin/admin.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import {
  InviteAdminDto,
  SetupPasswordDto,
  SetupPasswordResponseDto,
  MultiCityInviteAdminDto,
  ChangeStatusDto,
  RemoveCityAdminsDto,
  RemoveCityAdminDto,
  CityAdminsPaginationDto,
  CityAdminsResponseDto,
  VerifyTokenDto,
  InviteAdminResponseDto,
  MultiCityInviteResponseDto,
  VerifyTokenResponseDto,
  GetAdminProfileResponseDto,
  ResendInviteResponseDto,
  ChangeStatusResponseDto,
  RemoveCityAdminsResponseDto,
  RemoveCityAdminResponseDto,
  AddAdminToCityDto,
  AddAdminToCityResponseDto,
} from './dto';

@ApiTags('Admin - Sub-Admin Management')
@Controller('admin/sub-admin')
export class SubAdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('invite')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Invite a user to be an admin/sub-admin',
    description:
      'Send an invitation to a user to join as an admin or sub-admin. If the user exists, use the existing user; otherwise, create a new user. Creates a user profile with invited status and sends an invitation email.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Admin invitation sent successfully',
    type: InviteAdminResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'User already has this admin role or invalid data',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'User already has this admin role',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Role with ID ... not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async inviteAdmin(
    @Body() inviteAdminDto: InviteAdminDto,
  ): Promise<InviteAdminResponseDto> {
    const data = await this.adminService.inviteAdmin(inviteAdminDto);

    return {
      success: true,
      message: 'Admin invitation sent successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('cities/invite')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Invite a user to be a city admin/sub-admin for multiple cities',
    description:
      'Send an invitation to a user to join as a city admin or sub-admin for multiple cities. If the user exists, use the existing user with active status; otherwise, create a new user with invited status. Creates a user profile, assigns to multiple city admins, and sends an invitation email with cities and role details.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Multi-city admin invitation sent successfully',
    type: MultiCityInviteResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'User already has this admin role or invalid data',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'User already has this admin role',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City or role not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'City with ID ... not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async inviteMultiCityAdmin(
    @Body() inviteDto: MultiCityInviteAdminDto,
  ): Promise<MultiCityInviteResponseDto> {
    const data = await this.adminService.inviteMultiCityAdmin(inviteDto);

    return {
      success: true,
      message: data.message,
      data,
      timestamp: Date.now(),
    };
  }

  @Post('cities/remove')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove multiple city admins',
    description:
      'Hard delete multiple city admin entries by their IDs. This permanently removes the city admin assignments.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'City admins removed successfully',
    type: RemoveCityAdminsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid city admin IDs or validation error',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'At least one city admin ID is required',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async removeCityAdmins(
    @Body() removeCityAdminsDto: RemoveCityAdminsDto,
  ): Promise<RemoveCityAdminsResponseDto> {
    const data = await this.adminService.removeCityAdmins(
      removeCityAdminsDto.cityAdminIds,
    );

    return {
      success: true,
      message: 'City admins removed successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('cities/:cityId/remove-admin')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove admin from city',
    description:
      'Remove a user profile from city admin assignments (soft delete).',
  })
  @ApiParam({
    name: 'cityId',
    description: 'City ID from which to remove the admin',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Admin removed from city successfully',
    type: RemoveCityAdminResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City or admin assignment not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Admin not found for this city' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async removeCityAdmin(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Body() removeDto: RemoveCityAdminDto,
  ): Promise<RemoveCityAdminResponseDto> {
    const success = await this.adminService.removeCityAdmin(
      cityId,
      removeDto.userProfileId,
    );

    if (!success) {
      throw new NotFoundException('Admin not found for this city');
    }

    return {
      success: true,
      message: 'Admin removed from city successfully',
      data: {},
      timestamp: Date.now(),
    };
  }

  @Post('cities/:cityId/add-admin')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Add admin to city',
    description:
      'Add an existing admin to a specific city with active status. Checks if the admin is already assigned to the city.',
  })
  @ApiParam({
    name: 'cityId',
    description: 'City ID to add the admin to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Admin added to city successfully',
    type: AddAdminToCityResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Admin already assigned to this city or invalid data',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Admin already assigned to this city',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City or admin not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'City or admin not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async addAdminToCity(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Body() addAdminDto: AddAdminToCityDto,
  ): Promise<AddAdminToCityResponseDto> {
    const data = await this.adminService.addAdminToCity(
      cityId,
      addAdminDto.adminIds,
    );

    return {
      success: true,
      message: 'Admin added to city successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('cities/:cityId/list-admins')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get paginated list of city admins',
    description:
      'Retrieve a paginated list of admins assigned to the city with optional search and status filters.',
  })
  @ApiParam({
    name: 'cityId',
    description: 'City ID to get admins for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by name or email',
    example: 'john',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by admin status',
    enum: ['active', 'pending', 'disabled', 'inactive', 'invited'],
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'City admins retrieved successfully',
    type: CityAdminsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'City not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async listCityAdmins(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Query() query: CityAdminsPaginationDto,
  ): Promise<CityAdminsResponseDto> {
    const result = await this.adminService.getCityAdminsPaginated(
      cityId,
      query.page || 1,
      query.limit || 10,
      query.search,
      query.status,
    );

    return {
      success: true,
      message: 'City admins retrieved successfully',
      data: result.admins,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('profile/token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify invitation token and get profile details',
    description:
      'Validate the invitation JWT token and return the associated user profile details if valid and not expired.',
  })
  @ApiQuery({
    name: 'token',
    required: true,
    description: 'JWT invitation token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token verified successfully, profile details returned',
    type: VerifyTokenResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or expired token',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Invitation token has expired' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User profile not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'User profile not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async verifyToken(
    @Query() query: VerifyTokenDto,
  ): Promise<VerifyTokenResponseDto> {
    const data = await this.adminService.verifyInvitationToken(query.token);

    return {
      success: true,
      message: 'Token verified successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('setup-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Setup password for invited admin',
    description:
      'Complete the admin invitation process by setting up a password. Validates the invitation token and updates the user profile status to active.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password setup completed successfully',
    type: SetupPasswordResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Password validation failed or passwords do not match',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Password and password confirmation do not match',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or expired invitation token',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Invitation token has expired' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User profile not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'User profile not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async setupPassword(
    @Body() setupPasswordDto: SetupPasswordDto,
  ): Promise<SetupPasswordResponseDto> {
    const tokens = await this.adminService.setupPassword(setupPasswordDto);

    return {
      success: true,
      message: 'Password setup completed successfully',
      data: tokens,
      timestamp: Date.now(),
    };
  }

  @Get('profile/:profileId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get admin profile details',
    description:
      'Retrieve admin profile details by profile ID, including associated user information.',
  })
  @ApiParam({
    name: 'profileId',
    description: 'Admin profile ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Admin profile retrieved successfully',
    type: GetAdminProfileResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Admin profile not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Admin profile not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async getAdminProfile(
    @Param('profileId') profileId: string,
  ): Promise<GetAdminProfileResponseDto> {
    const data = await this.adminService.getAdminProfile(profileId);

    return {
      success: true,
      message: 'Admin profile retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('profile/:profileId/resend-invite')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Resend invitation to admin',
    description:
      'Resend invitation email to an existing admin profile. Generates a new invitation token and sends the invitation email again.',
  })
  @ApiParam({
    name: 'profileId',
    description: 'Admin profile ID to resend invitation for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Invitation resent successfully',
    type: ResendInviteResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Admin profile not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Admin profile not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Admin profile is already active',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Admin profile is already active' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async resendInvite(
    @Param('profileId') profileId: string,
  ): Promise<ResendInviteResponseDto> {
    const data = await this.adminService.resendInvite(profileId);

    return {
      success: true,
      message: 'Invitation resent successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('profile/:profileId/change-status')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Change admin profile status',
    description:
      'Update the status of an admin profile between active and inactive. Cannot change to the same status.',
  })
  @ApiParam({
    name: 'profileId',
    description: 'Admin profile ID to change status for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Status changed successfully',
    type: ChangeStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Admin profile not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Admin profile not found' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Profile already has the requested status',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Profile is already active' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async changeStatus(
    @Param('profileId') profileId: string,
    @Body() changeStatusDto: ChangeStatusDto,
  ): Promise<ChangeStatusResponseDto> {
    const data = await this.adminService.changeProfileStatus(
      profileId,
      changeStatusDto.status,
    );

    return {
      success: true,
      message: 'Admin profile status updated successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

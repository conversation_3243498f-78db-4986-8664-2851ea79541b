import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, IsBoolean, IsOptional } from 'class-validator';

export class UpdateKycDocumentDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Country ID where this KYC document is applicable',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  countryId?: string;

  @ApiProperty({
    example: 'Aadhaar Card',
    description: 'Name of the KYC document',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 'aadhaar_card',
    description: 'Unique identifier for the KYC document type',
    required: false,
  })
  @IsOptional()
  @IsString()
  identifier?: string;

  @ApiProperty({
    example: { fields: ['aadhaar_number', 'name', 'address'] },
    description: 'Required fields for this KYC document',
    required: false,
  })
  @IsOptional()
  requiredFields?: any;

  @ApiProperty({
    example: true,
    description: 'Whether this KYC document is mandatory for the country',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isMandatory?: boolean;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsPhoneNumber, IsNotEmpty, IsString, Length } from 'class-validator';

export class VerifyOtpDto {
  @ApiProperty({
    example: '+919876543210',
    description: 'Driver mobile number in international format',
  })
  @IsPhoneNumber()
  @IsNotEmpty()
  phoneNumber!: string;

  @ApiProperty({
    example: '1234',
    description: '4-digit OTP code',
    minLength: 4,
    maxLength: 4,
  })
  @IsString()
  @IsNotEmpty()
  @Length(4, 4)
  otp!: string;
}

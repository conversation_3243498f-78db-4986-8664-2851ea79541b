import { Test, TestingModule } from '@nestjs/testing';
import { HealthService } from './health.service';
import { AppConfigService } from '@shared/shared/config';

describe('HealthService', () => {
  let service: HealthService;
  let configService: AppConfigService;

  beforeEach(async () => {
    const mockConfigService = {
      coreApiPort: 3000,
      coreApiHost: 'localhost',
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: AppConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
    configService = module.get<AppConfigService>(AppConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getHealth', () => {
    it('should return health status with required fields', async () => {
      const result = await service.getHealth();

      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('uptime');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('environment');
      expect(result).toHaveProperty('memory');
      expect(result).toHaveProperty('services');

      expect(result.memory).toHaveProperty('used');
      expect(result.memory).toHaveProperty('total');
      expect(result.memory).toHaveProperty('percentage');

      expect(typeof result.uptime).toBe('number');
      expect(result.uptime).toBeGreaterThanOrEqual(0);
    });

    it('should have API service in services', async () => {
      const result = await service.getHealth();

      expect(result.services).toHaveProperty('api');
      expect(result.services['api']).toHaveProperty('status');
      expect(result.services['api'].status).toBe('OK');
    });
  });

  describe('getSimpleHealth', () => {
    it('should return simple health status', async () => {
      const result = await service.getSimpleHealth();

      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result.status).toBe('OK');
    });
  });

  describe('checkReadiness', () => {
    it('should return readiness status with checks', async () => {
      const result = await service.checkReadiness();

      expect(result).toHaveProperty('ready');
      expect(result).toHaveProperty('checks');
      expect(typeof result.ready).toBe('boolean');

      expect(result.checks).toHaveProperty('database');
      expect(result.checks).toHaveProperty('configuration');
      expect(result.checks).toHaveProperty('memory');
    });
  });

  describe('checkLiveness', () => {
    it('should return liveness status', async () => {
      const result = await service.checkLiveness();

      expect(result).toHaveProperty('alive');
      expect(result.alive).toBe(true);
    });
  });
});

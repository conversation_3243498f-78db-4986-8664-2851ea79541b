import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { RideFareRepository } from '@shared/shared/repositories/ride-fare.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import {
  RideFare,
  CreateRideFareData,
  UpdateRideFareData,
  RideFareFilters,
  BulkCreateRideFareData,
  RideFareSummary,
  CreateRideFareFromCalculationData,
  RideFareCalculationContext,
} from '@shared/shared/repositories/models/rideFare.model';
import { FareCalculationResult } from '@shared/shared/modules/fare-engine/fare-calculator/interfaces/fare-calculation.interface';

@Injectable()
export class RideFareService {
  private readonly logger = new Logger(RideFareService.name);

  constructor(
    private readonly rideFareRepository: RideFareRepository,
    private readonly rideRepository: RideRepository,
  ) {}

  /**
   * Create a new ride fare
   */
  async createRideFare(data: CreateRideFareData): Promise<RideFare> {
    this.logger.log(
      `Creating ride fare for ride ${data.rideId}: ${data.totalFare} ${data.currency}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    // Validate fare data
    this.validateFareData(data);

    return this.rideFareRepository.createRideFare(data);
  }

  /**
   * Create ride fare from fare calculation result
   */
  async createRideFareFromCalculation(
    data: CreateRideFareFromCalculationData,
  ): Promise<RideFare> {
    this.logger.log(
      `Creating ride fare from calculation for ride ${data.rideId}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    return this.rideFareRepository.createRideFareFromCalculation(data);
  }

  /**
   * Create multiple ride fares in bulk
   */
  async createBulkRideFares(data: BulkCreateRideFareData): Promise<RideFare[]> {
    this.logger.log(
      `Creating ${data.fares.length} ride fares for ride ${data.rideId}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    // Validate all fare data
    data.fares.forEach((fare, index) => {
      try {
        this.validateFareData(fare);
      } catch (error: any) {
        throw new BadRequestException(
          `Invalid fare data at index ${index}: ${error.message}`,
        );
      }
    });

    return this.rideFareRepository.createBulkRideFares(data);
  }

  /**
   * Get ride fares by ride ID
   */
  async getRideFaresByRideId(rideId: string): Promise<RideFare[]> {
    this.logger.log(`Getting ride fares for ride ${rideId}`);

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideFareRepository.findByRideId(rideId);
  }

  /**
   * Get latest ride fare for a ride
   */
  async getLatestRideFare(rideId: string): Promise<RideFare | null> {
    this.logger.log(`Getting latest ride fare for ride ${rideId}`);

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideFareRepository.findLatestByRideId(rideId);
  }

  /**
   * Get ride fares with filters
   */
  async getRideFaresWithFilters(filters: RideFareFilters): Promise<RideFare[]> {
    this.logger.log(
      `Getting ride fares with filters: ${JSON.stringify(filters)}`,
    );

    return this.rideFareRepository.findWithFilters(filters);
  }

  /**
   * Update ride fare
   */
  async updateRideFare(
    id: string,
    data: UpdateRideFareData,
  ): Promise<RideFare> {
    this.logger.log(`Updating ride fare ${id}`);

    // Check if fare exists
    const existingFare = await this.rideFareRepository.findById(id);
    if (!existingFare) {
      throw new NotFoundException(`Ride fare with ID ${id} not found`);
    }

    // Validate updated data
    if (data.fare !== undefined || data.totalFare !== undefined) {
      if (data.fare !== undefined && data.fare < 0) {
        throw new BadRequestException('Fare cannot be negative');
      }
      if (data.totalFare !== undefined && data.totalFare < 0) {
        throw new BadRequestException('Total fare cannot be negative');
      }
    }

    return this.rideFareRepository.updateRideFare(id, data);
  }

  /**
   * Delete ride fare
   */
  async deleteRideFare(id: string): Promise<void> {
    this.logger.log(`Deleting ride fare ${id}`);

    // Check if fare exists
    const existingFare = await this.rideFareRepository.findById(id);
    if (!existingFare) {
      throw new NotFoundException(`Ride fare with ID ${id} not found`);
    }

    await this.rideFareRepository.deleteRideFare(id);
  }

  /**
   * Delete all ride fares for a ride
   */
  async deleteRideFaresByRideId(rideId: string): Promise<void> {
    this.logger.log(`Deleting all ride fares for ride ${rideId}`);

    await this.rideFareRepository.deleteByRideId(rideId);
  }

  /**
   * Get ride fare summary
   */
  async getRideFareSummary(rideId: string): Promise<RideFareSummary | null> {
    this.logger.log(`Getting ride fare summary for ride ${rideId}`);

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideFareRepository.getRideFareSummary(rideId);
  }

  /**
   * Check if ride has fare calculated
   */
  async hasRideFare(rideId: string): Promise<boolean> {
    this.logger.log(`Checking if ride ${rideId} has fare calculated`);

    return this.rideFareRepository.existsForRide(rideId);
  }

  /**
   * Replace existing ride fares with new calculation
   */
  async replaceRideFares(
    rideId: string,
    fareCalculationResult: FareCalculationResult,
  ): Promise<RideFare> {
    this.logger.log(
      `Replacing ride fares for ride ${rideId} with new calculation`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Delete existing fares
    await this.deleteRideFaresByRideId(rideId);

    // Create new fare from calculation
    return this.createRideFareFromCalculation({
      rideId,
      fareCalculationResult,
    });
  }

  /**
   * Get fare calculation context for a ride
   */
  async getFareCalculationContext(
    rideId: string,
  ): Promise<RideFareCalculationContext | null> {
    this.logger.log(`Getting fare calculation context for ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      return null;
    }

    if (!ride.cityProductId) {
      this.logger.warn(`Ride ${rideId} does not have a city product ID`);
      return null;
    }

    // Get ride meters (this would be implemented when we integrate with RideMeterService)
    const rideMeters: Record<string, number> = {};

    // Extract zone IDs from ride locations (simplified)
    const pickupZoneId = ride.pickupLocation ? null : null; // Would need zone lookup
    const destinationZoneId = ride.destinationLocation ? null : null; // Would need zone lookup

    return {
      rideId,
      cityProductId: ride.cityProductId,
      pickupZoneId,
      destinationZoneId,
      stopsZoneIds: null, // Would extract from stops
      rideMeters,
      currency: 'INR',
      timestamp: new Date(),
    };
  }

  /**
   * Validate fare data
   */
  private validateFareData(data: CreateRideFareData): void {
    if (data.fare < 0) {
      throw new BadRequestException('Fare cannot be negative');
    }

    if (data.totalFare < 0) {
      throw new BadRequestException('Total fare cannot be negative');
    }

    if (data.subtotal < 0) {
      throw new BadRequestException('Subtotal cannot be negative');
    }

    if (data.totalTaxes < 0) {
      throw new BadRequestException('Total taxes cannot be negative');
    }

    if (data.totalCommissions < 0) {
      throw new BadRequestException('Total commissions cannot be negative');
    }

    if (data.passengerFare < 0) {
      throw new BadRequestException('Passenger fare cannot be negative');
    }

    if (data.driverEarnings < 0) {
      throw new BadRequestException('Driver earnings cannot be negative');
    }

    if (data.platformRevenue < 0) {
      throw new BadRequestException('Platform revenue cannot be negative');
    }

    // Validate currency format
    const currencyRegex = /^[A-Z]{3}$/;
    if (data.currency && !currencyRegex.test(data.currency)) {
      throw new BadRequestException(
        'Currency must be a 3-letter ISO code (e.g., INR, USD)',
      );
    }

    // Basic fare calculation validation
    const expectedPassengerFare = data.subtotal + data.totalTaxes;
    if (Math.abs(data.passengerFare - expectedPassengerFare) > 0.01) {
      this.logger.warn(
        `Passenger fare mismatch for ride ${data.rideId}: expected ${expectedPassengerFare}, got ${data.passengerFare}`,
      );
    }

    const expectedDriverEarnings = data.subtotal - data.totalCommissions;
    if (Math.abs(data.driverEarnings - expectedDriverEarnings) > 0.01) {
      this.logger.warn(
        `Driver earnings mismatch for ride ${data.rideId}: expected ${expectedDriverEarnings}, got ${data.driverEarnings}`,
      );
    }
  }
}

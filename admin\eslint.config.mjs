import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
   baseDirectory: __dirname,
});

const eslintConfig = [
   ...compat.extends('next/core-web-vitals', 'next/typescript'),
   {
      files: ['**/*.ts', '**/*.tsx'],
      rules: {
         '@typescript-eslint/no-explicit-any': 'off',
         'react/no-unescaped-entities': 'off',
         'react/no-access-during-render': 'off',
         '@typescript-eslint/no-unused-vars': [
            'error',
            {
               vars: 'all',
               varsIgnorePattern: '^_',
               args: 'after-used',
               argsIgnorePattern: '^_',
            },
         ],
         // Disable React Compiler rules
         'react-hooks/incompatible-library': 'off',
         'react-hooks/set-state-in-effect': 'off',
         'react-hooks/purity': 'off',
         'react-hooks/static-components': 'off',
         'react-hooks/immutability': 'off',
         'react-hooks/refs': 'off',
         'react-hooks/preserve-manual-memoization': 'off',
         'react-hooks/exhaustive-deps': 'warn', // Keep as warning instead of error
         'react/no-access-during-render': 'off',
      },
   },
];

export default eslintConfig;

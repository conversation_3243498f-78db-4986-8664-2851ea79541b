/**
 * Utility functions for consistent date formatting across server and client
 * to prevent hydration mismatches
 */

/**
 * Format a date string for display in a consistent way that works on both server and client
 * Uses ISO date format to avoid locale-based differences
 */
export function formatDateForDisplay(dateString: string | null | undefined): string {
  if (!dateString) return 'Not provided';
  
  try {
    const date = new Date(dateString);
    // Use toISOString().split() to avoid locale issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${month}/${day}/${year}`;
  } catch {
    console.warn('Invalid date string:', dateString);
    return 'Invalid date';
  }
}

/**
 * Format a date with full month name (safe for SSR)
 */
export function formatDateWithMonth(dateString: string | null | undefined): string {
  if (!dateString) return 'Not provided';
  
  try {
    const date = new Date(dateString);
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const year = date.getFullYear();
    const month = months[date.getMonth()];
    const day = date.getDate();
    
    return `${month} ${day}, ${year}`;
  } catch {
    console.warn('Invalid date string:', dateString);
    return 'Invalid date';
  }
}
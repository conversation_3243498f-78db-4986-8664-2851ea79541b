import { IsEnum, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum AdminProfileStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export class ChangeStatusDto {
  @ApiProperty({
    description:
      'New status for the admin profile (can only toggle between active and inactive)',
    enum: AdminProfileStatus,
    enumName: 'AdminProfileStatus',
    example: AdminProfileStatus.ACTIVE,
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'Status is required' })
  @IsEnum(AdminProfileStatus, {
    message: 'Status must be either active or inactive',
  })
  status!: AdminProfileStatus;
}

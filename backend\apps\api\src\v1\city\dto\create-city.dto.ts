import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum CityStatusDto {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export class LatLngDto {
  @ApiProperty({ example: 9.9312, description: 'Latitude coordinate' })
  @IsOptional()
  lat!: number;

  @ApiProperty({ example: 76.2673, description: 'Longitude coordinate' })
  @IsOptional()
  lng!: number;
}

export class CreateCityDto {
  @ApiProperty({ example: 'San Francisco' })
  @IsString()
  name!: string;

  @ApiProperty({
    example: 'uploads/cities/san-francisco-icon.png',
    description: 'Icon file path for the city',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    example: 'California',
    description: 'State where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    example: 'United States',
    description: 'Country where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    example: [
      { lat: 37.81331899998324, lng: -122.40898669999721 },
      { lat: 37.78663020000072, lng: -122.3805436999997 },
      { lat: 37.71980619999785, lng: -122.35447369999936 },
      { lat: 37.70761319999757, lng: -122.5123436999984 },
      { lat: 37.78358719999717, lng: -122.5247187000022 },
      { lat: 37.815157199999845, lng: -122.4798767000009 },
    ],
    description:
      'Array of lat/lng coordinates defining the city polygon. Must be closed (first and last coordinates should be the same).',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LatLngDto)
  polygon?: LatLngDto[];

  @ApiProperty({
    example: 'active',
    enum: CityStatusDto,
    description: 'Status of the city',
    default: 'active',
    required: false,
  })
  @IsOptional()
  @IsEnum(CityStatusDto)
  status?: CityStatusDto;

  @ApiProperty({
    example: { colour: '#6668' },
    description: 'Additional metadata for the city as JSON object',
    required: false,
  })
  @IsOptional()
  @IsObject()
  meta?: Record<string, any>;
}

import { Injectable, Logger } from '@nestjs/common';
import { PaymentType } from '@shared/shared/repositories/models/payment.model';
import {
  DriverAccount,
  DriverAccountTransaction,
  TransactionType,
} from '@shared/shared/repositories/models/driverAccount.model';
import {
  DriverAccountRepository,
  DriverAccountTransactionRepository,
} from '@shared/shared/repositories/driver-account.repository';

export interface EarningsBreakdown {
  totalCollected: number;
  fareTax: number;
  commission: number;
  taxOnCommission: number;
  driverNetEarnings: number;
  tukxiEarnings: number;
}

export interface CreateTransactionData {
  driverId: string;
  rideId?: string;
  amount: number;
  transactionType: TransactionType;
  reason: string;
}

@Injectable()
export class DriverAccountService {
  private readonly logger = new Logger(DriverAccountService.name);

  constructor(
    private readonly driverAccountRepository: DriverAccountRepository,
    private readonly transactionRepository: DriverAccountTransactionRepository,
  ) {}

  /**
   * Get or create driver account
   */
  async getOrCreateDriverAccount(driverId: string): Promise<DriverAccount> {
    return this.driverAccountRepository.getOrCreateDriverAccount(driverId);
  }

  /**
   * Calculate earnings breakdown from fare specification
   */
  calculateEarnings(fareSpec: any): EarningsBreakdown {
    this.logger.log('Calculating earnings breakdown from fare spec');

    // Extract values from fare spec (assuming it follows the fare engine structure)
    const totalCollected = fareSpec.passengerFare || 0;
    const fareTax = fareSpec.totalTaxes || 0;
    const commission = this.extractTotalCommission(fareSpec);

    // Calculate driver net earnings
    const driverNetEarnings = fareSpec.driverEarnings || 0;
    const taxOnCommission = this.extractTotalTaxOnCommission(fareSpec);
    const tukxiEarnings = commission + taxOnCommission + fareTax;

    const breakdown: EarningsBreakdown = {
      totalCollected,
      fareTax,
      commission,
      taxOnCommission,
      driverNetEarnings,
      tukxiEarnings,
    };

    this.logger.debug('Earnings breakdown calculated', breakdown);
    return breakdown;
  }

  /**
   * Update driver balance based on payment type
   */
  async updateDriverBalance(
    driverId: string,
    rideId: string,
    paymentType: PaymentType,
    earningsBreakdown: EarningsBreakdown,
  ): Promise<DriverAccount> {
    this.logger.log(
      `Updating driver balance for ${driverId}, payment type: ${paymentType}`,
    );

    const account = await this.getOrCreateDriverAccount(driverId);
    let newBalance = Number(account.availableBalance);
    let transactionAmount: number;
    let reason: string;

    if (paymentType === PaymentType.CASH) {
      // Cash payment: Driver collects full fare, owes Tukxi's share
      transactionAmount = -earningsBreakdown.tukxiEarnings;
      newBalance += transactionAmount;
      reason = `Tukxi share owed (Cash trip) - Ride ${rideId}`;
    } else if (paymentType === PaymentType.ONLINE) {
      // Online payment: System collects fare, credit driver's net share
      transactionAmount = earningsBreakdown.driverNetEarnings;
      newBalance += transactionAmount;
      reason = `Online payment credited - Ride ${rideId}`;
    } else {
      throw new Error(`Invalid payment type: ${paymentType}`);
    }

    // Update account balance
    const updatedAccount = await this.driverAccountRepository.updateBalance(
      driverId,
      newBalance,
    );

    // Log transaction
    await this.logTransaction(
      {
        driverId,
        rideId,
        amount: Math.abs(transactionAmount),
        transactionType:
          transactionAmount >= 0
            ? TransactionType.CREDIT
            : TransactionType.DEBIT,
        reason,
      },
      newBalance,
    );

    this.logger.log(
      `Driver balance updated: ${account.availableBalance} -> ${newBalance}`,
    );
    return updatedAccount;
  }

  async updateDriverBalanceDirect(
    driverId: string,
    newBalance: number,
  ): Promise<DriverAccount> {
    this.logger.log(
      `Updating driver balance for ${driverId}, payment type: ${newBalance} (Direct)`,
    );

    const updatedAccount = await this.driverAccountRepository.updateBalance(
      driverId,
      newBalance,
    );

    return updatedAccount;
  }

  /**
   * Log a transaction
   */
  async logTransaction(
    data: CreateTransactionData,
    balanceAfter: number,
  ): Promise<DriverAccountTransaction> {
    return this.transactionRepository.createTransaction({
      ...data,
      balanceAfter,
    });
  }

  /**
   * Get driver account with current balance
   */
  async getDriverAccount(driverId: string): Promise<DriverAccount | null> {
    return this.driverAccountRepository.findByDriverId(driverId);
  }

  /**
   * Get driver transactions with pagination
   */
  async getDriverTransactions(
    driverId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    transactions: DriverAccountTransaction[];
    total: number;
    totalPages: number;
  }> {
    const { transactions, total } =
      await this.transactionRepository.findTransactionsByDriverId(
        driverId,
        page,
        limit,
      );
    const totalPages = Math.ceil(total / limit);

    return { transactions, total, totalPages };
  }

  /**
   * Get transaction summary for driver
   */
  async getTransactionSummary(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    totalCredits: number;
    totalDebits: number;
    netAmount: number;
    transactionCount: number;
    currentBalance: number;
  }> {
    const [summary, account] = await Promise.all([
      this.transactionRepository.getTransactionSummary(
        driverId,
        fromDate,
        toDate,
      ),
      this.getDriverAccount(driverId),
    ]);

    return {
      ...summary,
      currentBalance: account ? Number(account.availableBalance) : 0,
    };
  }

  /**
   * Get transactions for a specific ride
   */
  async getRideTransactions(
    rideId: string,
  ): Promise<DriverAccountTransaction[]> {
    return this.transactionRepository.findTransactionsByRideId(rideId);
  }

  private extractTotalCommission(fareSpec: any): number {
    if (
      !fareSpec?.commissionBreakdown ||
      !Array.isArray(fareSpec.commissionBreakdown)
    ) {
      return 0;
    }

    return fareSpec.commissionBreakdown.reduce(
      (total: number, commission: any) => {
        return total + (commission.netCommissionAmount || 0);
      },
      0,
    );
  }

  private extractTotalTaxOnCommission(fareSpec: any): number {
    if (
      !fareSpec?.commissionBreakdown ||
      !Array.isArray(fareSpec.commissionBreakdown)
    ) {
      return 0;
    }

    return fareSpec.commissionBreakdown.reduce(
      (total: number, commission: any) => {
        const taxResults = commission.taxCalculation?.subcategoryResults || [];
        const commissionTax = taxResults.reduce(
          (sum: number, tax: any) => sum + (tax.taxAmount || 0),
          0,
        );
        return total + commissionTax;
      },
      0,
    );
  }
}

'use client';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle } from 'lucide-react';
import { useUpdateCityStatus } from '../api/mutations';
import { toast } from 'sonner';

interface CityStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  cityId: string;
  cityName: string;
  currentStatus: 'active' | 'inactive' | undefined;
  newStatus: 'active' | 'inactive';
  onStatusUpdated: () => void;
}

export function CityStatusModal({
  isOpen,
  onClose,
  cityId,
  cityName,
  currentStatus: _currentStatus,
  newStatus,
  onStatusUpdated,
}: CityStatusModalProps) {
  const updateCityStatusMutation = useUpdateCityStatus();

  const handleConfirm = () => {
    updateCityStatusMutation.mutate(
      {
        id: cityId,
        status: newStatus,
      },
      {
        onSuccess: () => {
          toast.success(
            `City ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`
          );
          onStatusUpdated();
          onClose();
        },
      }
    );
  };

  const isActivating = newStatus === 'active';
  const actionText = isActivating ? 'activate' : 'deactivate';
  const actionTextCapitalized = isActivating ? 'Activate' : 'Deactivate';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-3 mb-2'>
            {isActivating ? (
              <div className='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center'>
                <CheckCircle className='w-5 h-5 text-green-600' />
              </div>
            ) : (
              <div className='w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center'>
                <AlertTriangle className='w-5 h-5 text-orange-600' />
              </div>
            )}
            <DialogTitle className='text-lg font-semibold'>
              {actionTextCapitalized} City
            </DialogTitle>
          </div>
          <DialogDescription className='text-base text-gray-600'>
            Are you sure you want to {actionText}{' '}
            <span className='font-medium'>{cityName}</span>?
          </DialogDescription>
          {isActivating && (
            <div className='mt-3 p-3 bg-green-50 rounded-lg'>
              <div className='text-sm text-green-800'>
                <strong>Note:</strong> Activating this city will make it available 
                for ride requests and driver operations.
              </div>
            </div>
          )}
          {!isActivating && (
            <div className='mt-3 p-3 bg-orange-50 rounded-lg'>
              <div className='text-sm text-orange-800'>
                <strong>Warning:</strong> Deactivating this city will prevent new 
                ride requests and may affect existing operations in this location.
              </div>
            </div>
          )}
        </DialogHeader>
        <DialogFooter className='flex gap-3 sm:gap-3'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={updateCityStatusMutation.isPending}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            onClick={handleConfirm}
            disabled={updateCityStatusMutation.isPending}
            className={`flex-1 ${
              isActivating
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-orange-600 hover:bg-orange-700 text-white'
            }`}
          >
            {updateCityStatusMutation.isPending
              ? 'Processing...'
              : `${actionTextCapitalized} City`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
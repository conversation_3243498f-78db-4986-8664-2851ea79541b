'use client';

import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { User } from 'lucide-react';
import { ErrorBoundary } from 'react-error-boundary';

interface ProfilePictureViewModalProps {
   profilePictureUrl?: string | null;
   driverName: string;
   isOpen: boolean;
   onClose: () => void;
}

export const ProfilePictureViewModal = ({
   profilePictureUrl,
   driverName,
   isOpen,
   onClose,
}: ProfilePictureViewModalProps) => {
   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-md'>
            <DialogHeader>
               <DialogTitle className='text-center'>Profile Picture</DialogTitle>
            </DialogHeader>

            <div className='flex items-center justify-center py-6'>
               {profilePictureUrl ? (
                  <ErrorBoundary
                     fallback={
                        <div className='w-96 h-96 rounded-full bg-gray-100 flex items-center justify-center'>
                           <User className='w-40 h-40 text-gray-400' />
                        </div>
                     }
                  >
                     <Image
                        src={profilePictureUrl}
                        alt={driverName}
                        width={384}
                        height={384}
                        className='w-96 h-96 rounded-full object-cover border-4 border-gray-100 shadow-lg'
                     />
                  </ErrorBoundary>
               ) : (
                  <div className='w-96 h-96 rounded-full bg-gray-100 flex items-center justify-center'>
                     <User className='w-40 h-40 text-gray-400' />
                  </div>
               )}
            </div>
         </DialogContent>
      </Dialog>
   );
};

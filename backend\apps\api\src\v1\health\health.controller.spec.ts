import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

describe('HealthController', () => {
  let controller: HealthController;
  let healthService: HealthService;

  beforeEach(async () => {
    const mockHealthService = {
      getHealth: jest.fn(),
      getSimpleHealth: jest.fn(),
      checkReadiness: jest.fn(),
      checkLiveness: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: mockHealthService,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    healthService = module.get<HealthService>(HealthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getHealth', () => {
    it('should call health service getHealth method', async () => {
      const mockResponse = {
        status: 'OK',
        timestamp: '2025-01-01T00:00:00.000Z',
        uptime: 100,
      };

      jest
        .spyOn(healthService, 'getHealth')
        .mockResolvedValue(mockResponse as any);

      const result = await controller.getHealth();

      expect(healthService.getHealth).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getSimpleHealth', () => {
    it('should call health service getSimpleHealth method', async () => {
      const mockResponse = {
        status: 'OK',
        timestamp: '2025-01-01T00:00:00.000Z',
      };

      jest
        .spyOn(healthService, 'getSimpleHealth')
        .mockResolvedValue(mockResponse);

      const result = await controller.getSimpleHealth();

      expect(healthService.getSimpleHealth).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getReadiness', () => {
    it('should call health service checkReadiness method', async () => {
      const mockResponse = {
        ready: true,
        checks: {
          database: true,
          configuration: true,
          memory: true,
        },
      };

      jest
        .spyOn(healthService, 'checkReadiness')
        .mockResolvedValue(mockResponse);

      const result = await controller.getReadiness();

      expect(healthService.checkReadiness).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getLiveness', () => {
    it('should call health service checkLiveness method', async () => {
      const mockResponse = { alive: true };

      jest
        .spyOn(healthService, 'checkLiveness')
        .mockResolvedValue(mockResponse);

      const result = await controller.getLiveness();

      expect(healthService.checkLiveness).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });
});

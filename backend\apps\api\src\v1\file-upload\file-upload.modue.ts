import { Module } from '@nestjs/common';
import { FileUploadController } from './file-upload.controller';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule as SharedAppConfigModule } from '@shared/shared';

@Module({
  imports: [SharedAppConfigModule],
  controllers: [FileUploadController],
  providers: [FileUploadService],
})
export class ApiFileUploadModule {}

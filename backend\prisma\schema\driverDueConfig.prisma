model DriverDueConfig {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  cityId      String    @unique @map("city_id") @db.Uuid
  maxDueLimit Decimal   @map("max_due_limit") @db.Decimal(10, 2)
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  city City @relation(fields: [cityId], references: [id], onDelete: Cascade)

  @@index([cityId], name: "idx_driver_due_config_city_id")
  @@index([isActive], name: "idx_driver_due_config_is_active")
  @@map("driver_due_configs")
}

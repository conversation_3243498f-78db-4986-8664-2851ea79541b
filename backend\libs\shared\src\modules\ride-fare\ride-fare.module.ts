import { Module } from '@nestjs/common';
import { RideFareService } from './ride-fare.service';
import { RideFareRepository } from '@shared/shared/repositories/ride-fare.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { PrismaModule } from '@shared/shared/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [RideFareService, RideFareRepository, RideRepository],
  exports: [RideFareService, RideFareRepository],
})
export class RideFareModule {}

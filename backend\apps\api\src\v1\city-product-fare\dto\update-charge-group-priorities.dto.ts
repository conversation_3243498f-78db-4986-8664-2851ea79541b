import { ApiProperty } from '@nestjs/swagger';
import {
  IsArra<PERSON>,
  IsUUID,
  IsNumber,
  ArrayNotEmpty,
  ValidateNested,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

class ChargeGroupPriorityUpdateDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Charge group ID to update priority for',
  })
  @IsUUID('4', { message: 'Charge group ID must be a valid UUID' })
  chargeGroupId!: string;

  @ApiProperty({
    example: 1,
    description:
      'New priority for the charge group (lower values = higher priority)',
    minimum: 1,
  })
  @IsNumber({}, { message: 'Priority must be a number' })
  @Min(1, { message: 'Priority must be at least 1' })
  priority!: number;
}

export class UpdateChargeGroupPrioritiesDto {
  @ApiProperty({
    example: [
      { chargeGroupId: '550e8400-e29b-41d4-a716-************', priority: 1 },
      { chargeGroupId: '550e8400-e29b-41d4-a716-************', priority: 2 },
    ],
    description: 'Array of charge group IDs with their new priorities',
    type: [ChargeGroupPriorityUpdateDto],
  })
  @IsArray({ message: 'Charge group priorities must be an array' })
  @ArrayNotEmpty({
    message: 'At least one charge group priority must be provided',
  })
  @ValidateNested({ each: true })
  @Type(() => ChargeGroupPriorityUpdateDto)
  chargeGroups!: ChargeGroupPriorityUpdateDto[];
}

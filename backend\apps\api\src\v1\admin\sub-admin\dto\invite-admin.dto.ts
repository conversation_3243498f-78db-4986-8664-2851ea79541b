import {
  <PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>,
  IsNot<PERSON>mpty,
  IsUUID,
  ValidateIf,
  <PERSON><PERSON><PERSON><PERSON>,
  Max<PERSON>ength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InviteAdminDto {
  @ApiProperty({
    description: 'First name of the admin being invited',
    example: 'John',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  @MinLength(2)
  @MaxLength(20)
  firstName!: string;

  @ApiProperty({
    description: 'Last name of the admin being invited',
    example: 'Doe',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  @MinLength(1)
  @MaxLength(20)
  lastName!: string;

  @ApiProperty({
    description: 'Email address of the admin being invited (must be valid)',
    example: '<EMAIL>',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @MinLength(2)
  @MaxLength(40)
  email!: string;

  @ApiPropertyOptional({
    description:
      'UUID of the role to assign to the admin (required if identifier is not provided)',
    example: '123e4567-e89b-12d3-a456-************',
    type: String,
  })
  // Required if identifier is missing
  @ValidateIf((o) => !o.identifier)
  @IsUUID('4', { message: 'Role ID must be a valid UUID' })
  roleId?: string;

  @ApiPropertyOptional({
    description:
      'Identifier/slug of the role to assign to the admin (required if roleId is not provided)',
    example: 'city_admin',
    type: String,
  })
  // Required if roleId is missing
  @ValidateIf((o) => !o.roleId)
  @IsString({ message: 'Role identifier must be a string' })
  identifier?: string;
}

import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { RideMeterRepository } from '@shared/shared/repositories/ride-meter.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import {
  RideMeter,
  CreateRideMeterData,
  UpdateRideMeterData,
  RideMeterFilters,
  BulkCreateRideMeterData,
  RideMeterType,
  RideMeterUnit,
} from '@shared/shared/repositories/models/rideMeter.model';

@Injectable()
export class RideMeterService {
  private readonly logger = new Logger(RideMeterService.name);

  constructor(
    private readonly rideMeterRepository: RideMeterRepository,
    private readonly rideRepository: RideRepository,
  ) {}

  /**
   * Create a new ride meter
   */
  async createRideMeter(data: CreateRideMeterData): Promise<RideMeter> {
    this.logger.log(
      `Creating ride meter for ride ${data.rideId}: ${data.name} = ${data.value} ${data.unit}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    // Validate meter data
    this.validateMeterData(data);

    // Check if meter already exists for this ride
    const existingMeter = await this.rideMeterRepository.findByRideIdAndName(
      data.rideId,
      data.name,
    );
    if (existingMeter) {
      throw new BadRequestException(
        `Meter '${data.name}' already exists for ride ${data.rideId}`,
      );
    }

    return this.rideMeterRepository.createRideMeter(data);
  }

  /**
   * Create multiple ride meters in bulk
   */
  async createBulkRideMeters(
    data: BulkCreateRideMeterData,
  ): Promise<RideMeter[]> {
    this.logger.log(
      `Creating ${data.meters.length} ride meters for ride ${data.rideId}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    // Validate all meter data
    data.meters.forEach((meter, index) => {
      try {
        this.validateMeterData({
          rideId: data.rideId,
          name: meter.name,
          value: meter.value,
          unit: meter.unit,
        });
      } catch (error: any) {
        throw new BadRequestException(
          `Invalid meter data at index ${index}: ${error.message}`,
        );
      }
    });

    // Check for duplicate meter names
    const meterNames = data.meters.map((m) => m.name);
    const duplicateNames = meterNames.filter(
      (name, index) => meterNames.indexOf(name) !== index,
    );
    if (duplicateNames.length > 0) {
      throw new BadRequestException(
        `Duplicate meter names found: ${duplicateNames.join(', ')}`,
      );
    }

    // Check if any meters already exist
    const existingMeters = await this.rideMeterRepository.findWithFilters({
      rideId: data.rideId,
      names: meterNames,
    });

    if (existingMeters.length > 0) {
      const existingNames = existingMeters.map((m) => m.name);
      throw new BadRequestException(
        `Meters already exist for ride ${data.rideId}: ${existingNames.join(', ')}`,
      );
    }

    return this.rideMeterRepository.createBulkRideMeters(data);
  }

  /**
   * Get ride meters by ride ID
   */
  async getRideMetersByRideId(rideId: string): Promise<RideMeter[]> {
    this.logger.log(`Getting ride meters for ride ${rideId}`);

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideMeterRepository.findByRideId(rideId);
  }

  /**
   * Get specific ride meter by ride ID and name
   */
  async getRideMeterByName(
    rideId: string,
    name: string,
  ): Promise<RideMeter | null> {
    this.logger.log(`Getting ride meter '${name}' for ride ${rideId}`);

    return this.rideMeterRepository.findByRideIdAndName(rideId, name);
  }

  /**
   * Get ride meters with filters
   */
  async getRideMetersWithFilters(
    filters: RideMeterFilters,
  ): Promise<RideMeter[]> {
    this.logger.log(
      `Getting ride meters with filters: ${JSON.stringify(filters)}`,
    );

    return this.rideMeterRepository.findWithFilters(filters);
  }

  /**
   * Update ride meter
   */
  async updateRideMeter(
    id: string,
    data: UpdateRideMeterData,
  ): Promise<RideMeter> {
    this.logger.log(`Updating ride meter ${id}`);

    // Check if meter exists
    const existingMeter = await this.rideMeterRepository.findById(id);
    if (!existingMeter) {
      throw new NotFoundException(`Ride meter with ID ${id} not found`);
    }

    // Validate updated data
    if (data.name || data.value !== undefined || data.unit) {
      const validationData: CreateRideMeterData = {
        rideId: existingMeter.rideId,
        name: data.name || existingMeter.name,
        value: data.value !== undefined ? data.value : existingMeter.value,
        unit: data.unit || existingMeter.unit,
      };
      this.validateMeterData(validationData);
    }

    return this.rideMeterRepository.updateRideMeter(id, data);
  }

  async updateRideMeterByNameAndRideId(
    rideId: string,
    name: string,
    data: UpdateRideMeterData,
  ): Promise<RideMeter> {
    this.logger.log(`Updating ride meter '${name}' for ride ${rideId}`);

    // Check if meter exists
    const existingMeter = await this.rideMeterRepository.findByRideIdAndName(
      rideId,
      name,
    );
    if (!existingMeter) {
      throw new NotFoundException(
        `Ride meter '${name}' for ride ${rideId} not found`,
      );
    }

    // Validate updated data
    if (data.name || data.value !== undefined || data.unit) {
      const validationData: CreateRideMeterData = {
        rideId: existingMeter.rideId,
        name: data.name || existingMeter.name,
        value: data.value !== undefined ? data.value : existingMeter.value,
        unit: data.unit || existingMeter.unit,
      };
      this.validateMeterData(validationData);
    }

    return this.rideMeterRepository.updateRideMeter(existingMeter.id, data);
  }

  /**
   * Delete ride meter
   */
  async deleteRideMeter(id: string): Promise<void> {
    this.logger.log(`Deleting ride meter ${id}`);

    // Check if meter exists
    const existingMeter = await this.rideMeterRepository.findById(id);
    if (!existingMeter) {
      throw new NotFoundException(`Ride meter with ID ${id} not found`);
    }

    await this.rideMeterRepository.deleteRideMeter(id);
  }

  /**
   * Delete all ride meters for a ride
   */
  async deleteRideMetersByRideId(rideId: string): Promise<void> {
    this.logger.log(`Deleting all ride meters for ride ${rideId}`);

    await this.rideMeterRepository.deleteByRideId(rideId);
  }

  /**
   * Create standard ride meters from ride data
   */
  async createStandardRideMeters(
    rideId: string,
    rideData: {
      pickupDistance?: number;
      tripDistance?: number;
      pickupDuration?: number;
      tripDuration?: number;
      actualDuration?: number;
      waitTime?: number;
    },
  ): Promise<RideMeter[]> {
    this.logger.log(`Creating standard ride meters for ride ${rideId}`);

    const meters: Array<{ name: string; value: number; unit: string }> = [];

    if (rideData.pickupDistance !== undefined) {
      meters.push({
        name: RideMeterType.PICKUP_DISTANCE,
        value: rideData.pickupDistance,
        unit: RideMeterUnit.KILOMETERS,
      });
    }

    if (rideData.tripDistance !== undefined) {
      meters.push({
        name: RideMeterType.TRIP_DISTANCE,
        value: rideData.tripDistance,
        unit: RideMeterUnit.KILOMETERS,
      });
    }

    if (rideData.pickupDuration !== undefined) {
      meters.push({
        name: RideMeterType.PICKUP_DURATION,
        value: rideData.pickupDuration,
        unit: RideMeterUnit.SECONDS,
      });
    }

    if (rideData.tripDuration !== undefined) {
      meters.push({
        name: RideMeterType.TRIP_DURATION,
        value: rideData.tripDuration,
        unit: RideMeterUnit.SECONDS,
      });
    }

    if (rideData.actualDuration !== undefined) {
      meters.push({
        name: RideMeterType.ACTUAL_DURATION,
        value: rideData.actualDuration,
        unit: RideMeterUnit.SECONDS,
      });
    }

    if (rideData.waitTime !== undefined) {
      meters.push({
        name: RideMeterType.PICKUP_WAIT_TIME,
        value: rideData.waitTime,
        unit: RideMeterUnit.SECONDS,
      });
    }

    if (meters.length === 0) {
      this.logger.warn(`No valid meter data provided for ride ${rideId}`);
      return [];
    }

    return this.createBulkRideMeters({ rideId, meters });
  }

  /**
   * Validate meter data
   */
  private validateMeterData(data: CreateRideMeterData): void {
    if (!data.name || data.name.trim().length === 0) {
      throw new BadRequestException('Meter name is required');
    }

    if (data.value < 0) {
      throw new BadRequestException('Meter value cannot be negative');
    }

    if (!data.unit || data.unit.trim().length === 0) {
      throw new BadRequestException('Meter unit is required');
    }

    // Validate meter name format (alphanumeric, underscore, hyphen)
    const nameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!nameRegex.test(data.name)) {
      throw new BadRequestException(
        'Meter name can only contain alphanumeric characters, underscores, and hyphens',
      );
    }

    // Validate unit format
    const unitRegex = /^[a-zA-Z0-9_%]+$/;
    if (!unitRegex.test(data.unit)) {
      throw new BadRequestException(
        'Meter unit can only contain alphanumeric characters, underscores, and percent signs',
      );
    }
  }
}

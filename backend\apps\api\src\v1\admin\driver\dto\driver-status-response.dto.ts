import { ApiProperty } from '@nestjs/swagger';
import { DriverStatus } from './change-driver-status.dto';

export class DriverStatusResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Driver profile ID',
  })
  id!: string;

  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.ACTIVE,
    description: 'Current driver status',
  })
  status!: string;

  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.INACTIVE,
    description: 'Previous driver status',
  })
  previousStatus!: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Status change timestamp',
  })
  updatedAt!: Date;

  @ApiProperty({
    example: 'Driver status changed successfully',
    description: 'Status change message',
  })
  message!: string;
}

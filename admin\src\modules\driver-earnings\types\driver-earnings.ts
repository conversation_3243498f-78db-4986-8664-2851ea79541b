// ============================================
// CORE ENTITY INTERFACES
// ============================================

export interface AggregatedDriverEarning {
   driverId: string;
   driverFirstName: string;
   driverLastName: string;
   city: string;
   totalFare: number;
   totalTaxesOnCharge: number;
   totalCommission: number;
   totalTaxOnCommission: number;
   netDriverEarnings: number;
   completedRides: number;
}

export interface DailyDriverEarning {
   date: string;
   totalFare: number;
   totalTaxesOnCharge: number;
   totalCommission: number;
   totalTaxOnCommission: number;
   netDriverEarnings: number;
   completedRides: number;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================

export interface AggregatedEarningsResponse {
   success: boolean;
   message: string;
   data: AggregatedDriverEarning[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
   };
   timestamp: number;
}

export interface DailyEarningsResponse {
   success: boolean;
   message: string;
   data: DailyDriverEarning[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
   };
   timestamp: number;
}

// ============================================
// QUERY PARAMETERS
// ============================================

export interface AggregatedEarningsParams {
   page?: number;
   limit?: number;
   fromDate?: string;
   toDate?: string;
   driverId?: string;
   cityId?: string;
   phoneNumber?: string;
}

export interface DailyEarningsParams {
   page?: number;
   limit?: number;
   fromDate?: string;
   toDate?: string;
   driverId: string;
}

// ============================================
// CSV EXPORT PARAMETERS
// ============================================

export interface ExportAggregatedCSVParams {
   fromDate: string;
   toDate: string;
   cityId?: string;
}

export interface ExportDailyCSVParams {
   driverId: string;
   fromDate: string;
   toDate: string;
}

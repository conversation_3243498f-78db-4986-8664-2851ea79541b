// ============================================
// ENUMS
// ============================================
export type UserProfileStatus = 'active' | 'pending' | 'disabled' | 'inactive' | 'invited';

export type Gender = 'male' | 'female' | 'other';

// ============================================
// CORE ENTITY INTERFACES
// ============================================
export interface RiderUser {
  id: string;
  phoneNumber: string;
  email?: string | null;
  phoneVerifiedAt?: string | null;
  emailVerifiedAt?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface RiderCity {
  id: string;
  name: string;
  code: string;
}

export interface Rider {
  id: string;
  firstName: string;
  lastName: string;
  profilePictureUrl?: string | null;
  gender?: string | null;
  dob?: string | null;
  referralCode: string | null;
  status: UserProfileStatus;
  createdAt: string;
  updatedAt: string;
  user: RiderUser;
  city?: RiderCity | null;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface RiderListResponse {
  success: boolean;
  message: string;
  data: Rider[];
  meta: PaginationMeta;
  timestamp: number;
}

export interface RiderDetailsResponse {
  success: boolean;
  message: string;
  data: Rider;
  timestamp: number;
}

// ============================================
// REQUEST PAYLOADS
// ============================================
export interface UpdateRiderRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  gender?: Gender;
  dob?: string;
  cityId?: string;
  profilePictureUrl?: string;
  status?: UserProfileStatus;
}

export interface UpdateRiderResponse {
  success: boolean;
  message: string;
  data: Rider;
  timestamp: number;
}

// ============================================
// QUERY PARAMETERS
// ============================================
export interface ListRidersParams {
  page?: number;
  limit?: number;
  search?: string;
  name?: string;
  email?: string;
  phoneNumber?: string;
  cityId?: string;
  status?: UserProfileStatus;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

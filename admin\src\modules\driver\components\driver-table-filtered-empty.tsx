'use client';

import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';

interface DriverTableFilteredEmptyProps {
  hasSearch: boolean;
  hasStatus: boolean;
  hasLocation: boolean;
  onClearFilters: () => void;
}

export function DriverTableFilteredEmpty({
  hasSearch,
  hasStatus,
  hasLocation,
  onClearFilters,
}: DriverTableFilteredEmptyProps) {
  const getFilterDescription = () => {
    const filters = [];
    if (hasSearch) filters.push('search term');
    if (hasStatus) filters.push('status filter');
    if (hasLocation) filters.push('location filter');
    
    if (filters.length === 1) {
      return `your ${filters[0]}`;
    } else if (filters.length === 2) {
      return `your ${filters[0]} and ${filters[1]}`;
    } else {
      return `your ${filters[0]}, ${filters[1]}, and ${filters[2]}`;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
        <Search className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No drivers match your filters</h3>
      <p className="text-gray-500 text-center mb-6 max-w-md">
        We couldn't find any drivers that match {getFilterDescription()}. 
        Try adjusting your search criteria or clear the filters to see all drivers.
      </p>
      <Button 
        onClick={onClearFilters} 
        variant="outline" 
        className="flex items-center gap-2"
      >
        <X className="w-4 h-4" />
        Clear Filters
      </Button>
    </div>
  );
}

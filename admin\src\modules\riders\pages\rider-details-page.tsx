'use client';

import { ErrorBoundary } from 'react-error-boundary';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, ArrowLeft, Mail, MapPin, Phone, User } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { useGetRider } from '../api/queries';
import { RiderPersonalDetails } from '../components/rider-personal-details';
import { RiderTripsTab } from '../components/rider-trips-tab';
import { RiderStatusModal } from '../components/rider-status-modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useParams, useSearchParams } from 'next/navigation';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export function RiderDetailsPage() {
  const params = useParams<{ id: string }>();
  const searchParams = useSearchParams();
  const riderId = params.id;
  const returnPage = searchParams.get('returnPage');
  const [activeTab, setActiveTab] = useState('personal');
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<'active' | 'inactive' | 'disabled'>('active');
  const { data: rider, isLoading, error, refetch } = useGetRider(riderId);
  const { withPermission } = useRoleBasedAccess();

  // Construct back URL with returnPage if available
  const backUrl = returnPage ? `/dashboard/riders?page=${returnPage}` : '/dashboard/riders';

  const handleStatusButtonClick = (newStatus: 'active' | 'inactive' | 'disabled') => {
    setPendingStatus(newStatus);
    setIsStatusModalOpen(true);
  };

  const handleStatusUpdated = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className='flex flex-1 flex-col gap-4 p-4'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='h-32 bg-gray-200 rounded mb-4'></div>
          <div className='h-96 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  if (error || !rider?.data) {
    return (
      <div className='flex flex-1 flex-col gap-4 p-4'>
        <div className='flex items-center gap-4 mb-6'>
          <Link href={backUrl}>
            <Button variant='ghost' size='sm' className='gap-2'>
              <ArrowLeft className='h-4 w-4' />
              Back to Riders
            </Button>
          </Link>
        </div>
        <Card className='p-8 text-center'>
          <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
          <h3 className='text-lg font-medium text-gray-900 mb-2'>Rider Not Found</h3>
          <p className='text-gray-600'>
            The rider you're looking for doesn't exist or has been removed.
          </p>
        </Card>
      </div>
    );
  }

  const riderData = rider.data;
  const fullName = `${riderData.firstName || ''} ${riderData.lastName || ''}`.trim() || 'N/A';

  // Determine current status and action
  const getStatusAction = () => {
    if (riderData.status === 'active') {
      return { text: 'Deactivate', action: 'inactive', color: 'bg-red-600 hover:bg-red-700' };
    } else if (riderData.status === 'disabled') {
      return { text: 'Activate', action: 'active', color: 'bg-green-600 hover:bg-green-700' };
    } else {
      return { text: 'Activate', action: 'active', color: 'bg-green-600 hover:bg-green-700' };
    }
  };

  const statusAction = getStatusAction();

  return (
    <div className='flex flex-1 flex-col space-y-6 px-6 py-6'>
      {/* Clean Header */}
      <div className='flex items-center gap-3'>
        <Link href={backUrl}>
          <Button variant='ghost' size='sm' className='gap-2 text-gray-600 hover:text-gray-900'>
            <ArrowLeft className='h-4 w-4' />
            Back to Riders
          </Button>
        </Link>
        <span className='text-gray-300'>/</span>
        <span className='text-sm text-gray-600'>Riders</span>
        <span className='text-gray-300'>/</span>
        <span className='text-sm text-gray-600'>Rider Details</span>
        <span className='text-gray-300'>/</span>
        <span className='text-sm text-gray-900 font-medium'>{fullName}</span>
      </div>

      {/* Compact Rider Header */}
      <Card className='p-6'>
        <div className='flex items-center gap-4'>
          {/* Profile Picture */}
          <div className='flex-shrink-0 relative'>
            {riderData.profilePictureUrl ? (
              <ErrorBoundary
                fallback={
                  <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center'>
                    <User className='w-6 h-6 text-gray-400' />
                  </div>
                }
              >
                <Image
                  src={riderData.profilePictureUrl}
                  alt={fullName}
                  width={56}
                  height={56}
                  className='w-14 h-14 rounded-full object-cover'
                />
              </ErrorBoundary>
            ) : (
              <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center'>
                <User className='w-6 h-6 text-gray-400' />
              </div>
            )}
          </div>

          {/* Rider Info */}
          <div className='flex-1 min-w-0'>
            <div className='flex items-center gap-2 mb-1'>
              <h1 className='text-xl font-semibold text-gray-900 truncate'>{fullName}</h1>
              <Badge
                className={`text-xs ${
                  riderData.status === 'active'
                    ? 'bg-green-100 text-green-700'
                    : riderData.status === 'disabled'
                      ? 'bg-red-100 text-red-700'
                      : riderData.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-gray-100 text-gray-700'
                }`}
              >
                {riderData.status.charAt(0).toUpperCase() + riderData.status.slice(1)}
              </Badge>
            </div>

            {/* Contact Info Row */}
            <div className='flex items-center gap-4 text-sm text-gray-600'>
              <div className='flex items-center gap-1'>
                <Phone className='w-4 h-4' />
                <span>{riderData.user.phoneNumber}</span>
              </div>
              {riderData.user.email && (
                <div className='flex items-center gap-1 truncate'>
                  <Mail className='w-4 h-4 flex-shrink-0' />
                  <span className='truncate'>{riderData.user.email}</span>
                </div>
              )}
              {riderData.city && (
                <div className='flex items-center gap-1'>
                  <MapPin className='w-4 h-4' />
                  <span>{riderData.city.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Status Action Button */}
          <div className='flex-shrink-0'>
            <Button
              className={`${statusAction.color} text-white`}
              onClick={() =>
                withPermission(RBAC_PERMISSIONS.RIDER.STATUS_UPDATE, () =>
                  handleStatusButtonClick(statusAction.action as any)
                )
              }
            >
              {statusAction.text} Rider
            </Button>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='flex-1'>
        <TabsList className='w-full justify-start border-b rounded-none h-auto p-0 bg-transparent'>
          <TabsTrigger
            value='personal'
            className='data-[state=active]:border-b-2 data-[state=active]:border-blue-500 rounded-none'
          >
            Personal Details
          </TabsTrigger>
          <TabsTrigger
            value='trips'
            className='data-[state=active]:border-b-2 data-[state=active]:border-blue-500 rounded-none'
          >
            Trips
          </TabsTrigger>
        </TabsList>

        <TabsContent value='personal' className='mt-6'>
          <Card className='p-6'>
            <RiderPersonalDetails rider={riderData} />
          </Card>
        </TabsContent>

        <TabsContent value='trips' className='mt-6'>
          <Card className='p-6'>
            <RiderTripsTab riderId={riderId} />
          </Card>
        </TabsContent>
      </Tabs>

      {/* Status Modal */}
      <RiderStatusModal
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        riderId={riderId}
        riderName={fullName}
        currentStatus={riderData.status}
        newStatus={pendingStatus}
        onStatusUpdated={handleStatusUpdated}
      />
    </div>
  );
}

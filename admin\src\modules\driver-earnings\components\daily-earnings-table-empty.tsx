import { Calendar } from 'lucide-react';

export function DailyEarningsTableEmpty() {
   return (
      <div className='flex flex-col items-center justify-center py-12 px-4'>
         <div className='rounded-full bg-gray-100 p-4 mb-4'>
            <Calendar className='h-8 w-8 text-gray-400' />
         </div>
         <h3 className='text-lg font-semibold text-gray-900 mb-1'>No daily earnings found</h3>
         <p className='text-sm text-gray-500 text-center max-w-sm'>
            There are no earnings records for this driver in the selected date range. Try adjusting
            your filters.
         </p>
      </div>
   );
}

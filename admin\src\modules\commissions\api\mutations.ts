import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  CreateCommissionRequest,
  CommissionResponse,
  UpdateCommissionRequest,
} from '../types/commissions';

/**
 * Hook for creating a new commission
 */
export const useCreateCommission = () => {
  return useMutation({
    mutationFn: async (data: CreateCommissionRequest): Promise<CommissionResponse> => {
      return apiClient.post('/commissions', data);
    },
  });
};

/**
 * Hook for updating a commission
 */
export const useUpdateCommission = () => {
  return useMutation({
    mutationFn: async (
      data: { id: string } & UpdateCommissionRequest
    ): Promise<CommissionResponse> => {
      const { id, ...payload } = data;
      return apiClient.put(`/commissions/${id}`, payload);
    },
  });
};

/**
 * Hook for deleting a commission (soft delete)
 */
export const useDeleteCommission = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ success: boolean; message: string; timestamp: number }> => {
      return apiClient.delete(`/commissions/${id}`);
    },
  });
};

import { Injectable, Logger } from '@nestjs/common';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { RideLifecycleRepository } from '@shared/shared/repositories/ride-lifecycle.repository';
import { RideService, CreateRideData } from '../ride.service';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import {
  Ride,
  PickupType,
} from '@shared/shared/repositories/models/ride.model';

@Injectable()
export class RideScheduleService {
  private readonly logger = new Logger(RideScheduleService.name);

  constructor(
    private readonly rideRepository: RideRepository,
    private readonly rideLifecycleRepository: RideLifecycleRepository,
    private readonly rideService: RideService,
  ) {}

  /**
   * Fetch all scheduled rides that are ready to be processed
   * Criteria: status = 'scheduled', pickupType = 'later', pickupTime <= now + 10 minutes
   */
  async getScheduledRidesReadyForProcessing(): Promise<Ride[]> {
    const tenMinutesFromNow = new Date(Date.now() + 10 * 60 * 1000); // Current time + 10 minutes
    // current time - 10 minutes
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000); // Current time - 10 minutes

    console.log(tenMinutesFromNow, tenMinutesAgo);
    this.logger.log(
      `Fetching scheduled rides with pickup time before ${tenMinutesFromNow.toISOString()}`,
    );

    try {
      const scheduledRides = await this.rideRepository.findMany({
        where: {
          OR: [
            {
              status: RideStatus.SCHEDULED,
              pickupTime: { lte: tenMinutesFromNow },
            },
            {
              status: { in: [RideStatus.SCHEDULED, RideStatus.UNASSIGNED] },
              pickupTime: { gte: tenMinutesAgo },
            },
          ],
          pickupType: PickupType.LATER,
        },
        include: {
          rider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              rideOtp: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              passengerLimit: true,
            },
          },
        },
        orderBy: {
          pickupTime: 'asc',
        },
      });

      this.logger.log(
        `Found ${scheduledRides.length} scheduled rides ready for processing`,
      );

      return scheduledRides;
    } catch (error) {
      this.logger.error('Failed to fetch scheduled rides:', error);
      throw error;
    }
  }

  /**
   * Process a scheduled ride by converting it to requested status
   * and publishing the ride requested event
   */
  async processScheduledRide(ride: Ride): Promise<void> {
    this.logger.log(
      `Processing scheduled ride ${ride.id} for rider ${ride.riderId}`,
    );

    try {
      // Update ride status from SCHEDULED to REQUESTED
      await this.rideRepository.updateById(ride.id, {
        status: RideStatus.REQUESTED,
      });

      // Create lifecycle entry for the status change
      await this.rideLifecycleRepository.createRideLifecycle({
        rideId: ride.id,
        status: RideStatus.REQUESTED,
        meta: {
          notes: 'Scheduled ride converted to requested by cron job',
          previousStatus: RideStatus.SCHEDULED,
          processedAt: new Date().toISOString(),
          scheduledPickupTime: ride.pickupTime?.toISOString(),
        },
      });

      // Create ride data for publishing event
      const rideData: CreateRideData = {
        riderId: ride.riderId,
        productId: ride.productId,
        cityProductId: ride.cityProductId!,
        pickup: ride.pickupLocation!,
        destination: ride.destinationLocation!,
        ...(ride.stops && { stops: ride.stops }),
        ...(ride.riderMeta && { riderMeta: ride.riderMeta }),
        ...(ride.bookFor && { bookFor: ride.bookFor }),
        pickupType: PickupType.NOW, // Convert to NOW since it's ready for processing
        ...(ride.pickupTime && { pickupTime: ride.pickupTime.toISOString() }),
        ...(ride.createdBy && { createdBy: ride.createdBy }),
      };

      // Ensure product is available
      if (!ride.product) {
        throw new Error(`Product not found for scheduled ride ${ride.id}`);
      }

      // Publish ride requested event (as NOW mode)
      await this.rideService.publishRideRequestedEventAsync(
        ride,
        rideData,
        PickupType.NOW,
        ride.product,
      );

      this.logger.log(
        `Successfully processed scheduled ride ${ride.id} - status updated to REQUESTED and event published`,
      );
    } catch (error) {
      this.logger.error(`Failed to process scheduled ride ${ride.id}:`, error);
      throw error;
    }
  }

  /**
   * Process all scheduled rides that are ready
   */
  async processAllScheduledRides(): Promise<{
    processed: number;
    failed: number;
    errors: string[];
  }> {
    this.logger.log('Starting scheduled rides processing...');

    const scheduledRides = await this.getScheduledRidesReadyForProcessing();

    if (scheduledRides.length === 0) {
      this.logger.log('No scheduled rides ready for processing');
      return { processed: 0, failed: 0, errors: [] };
    }

    let processed = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const ride of scheduledRides) {
      try {
        await this.processScheduledRide(ride);
        processed++;
      } catch (error) {
        failed++;
        const errorMessage = `Failed to process ride ${ride.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        this.logger.error(errorMessage, error);
      }
    }

    this.logger.log(
      `Scheduled rides processing completed: ${processed} processed, ${failed} failed`,
    );

    return { processed, failed, errors };
  }
}

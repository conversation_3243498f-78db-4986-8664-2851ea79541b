'use client';

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { UserProfileDropdown } from '@/components/user-profile-dropdown';
import { OfflineIndicator } from '@/components/offline-indicator';
import { useAuthStore } from '@/store/auth-store';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { resetStore } from '@/store/store-helpers';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { usePageKickoutBasedOnPermissions } from '@/role-based-access/use-page-kickout';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
   const authToken = useAuthStore.getState().authToken;
   const router = useRouter();

   useEffect(() => {
      if (!authToken) {
         resetStore();
         router.push('/');
      }
   }, [authToken, router]);

   // initial fetch to cache the roles permission data at the root
   useRoleBasedAccess();

   // Kick out of a page if we have no permission on that page
   usePageKickoutBasedOnPermissions();

   return (
      <SidebarProvider>
         <AppSidebar />
         <SidebarInset>
            <header className='flex h-[57px] shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 border-b border-gray-200 bg-white px-6'>
               <div className='flex items-center gap-2'>
                  <SidebarTrigger className='-ml-1 cursor-pointer' />
                  <Separator
                     orientation='vertical'
                     className='mr-2 data-[orientation=vertical]:h-4'
                  />
               </div>

               <div className='flex items-center gap-4'>
                  <UserProfileDropdown />
               </div>
            </header>

            <OfflineIndicator />

            <div className='flex flex-1 flex-col'>{children}</div>
         </SidebarInset>
      </SidebarProvider>
   );
}

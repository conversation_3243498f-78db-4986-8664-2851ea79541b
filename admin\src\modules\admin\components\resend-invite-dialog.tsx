'use client';

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Admin } from '../types/admin';
import { useResendInvite } from '../api/mutations';
import { toast } from 'sonner';

interface ResendInviteDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   admin: Admin;
}

export function ResendInviteDialog({ open, onOpenChange, admin }: ResendInviteDialogProps) {
   const resendInviteMutation = useResendInvite();

   const handleConfirm = async () => {
      try {
         await resendInviteMutation.mutateAsync(admin.id);
         toast.success('Invitation resent successfully');
         onOpenChange(false);
      } catch (error: any) {
         toast.error(error?.response?.data?.message || 'Failed to resend invitation');
      }
   };

   return (
      <AlertDialog open={open} onOpenChange={onOpenChange}>
         <AlertDialogContent>
            <AlertDialogHeader>
               <AlertDialogTitle>Resend Invitation</AlertDialogTitle>
               <AlertDialogDescription>
                  Are you sure you want to resend the invitation to{' '}
                  <strong>{admin.firstName} {admin.lastName}</strong> at{' '}
                  <strong>{admin.user.email}</strong>?
                  <br />
                  <br />
                  A new invitation email will be sent to this email address.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel disabled={resendInviteMutation.isPending}>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  onClick={handleConfirm}
                  disabled={resendInviteMutation.isPending}
               >
                  {resendInviteMutation.isPending ? 'Sending...' : 'Resend Invite'}
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
}
import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  RideFare,
  CreateRideFareData,
  UpdateRideFareData,
  RideFareFilters,
  BulkCreateRideFareData,
  RideFareSummary,
  CreateRideFareFromCalculationData,
} from './models/rideFare.model';

@Injectable()
export class RideFareRepository extends BaseRepository<RideFare> {
  protected readonly modelName = 'rideFare';

  constructor(protected readonly prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new ride fare
   */
  async createRideFare(data: CreateRideFareData): Promise<RideFare> {
    const rideFare = await this.prisma.rideFare.create({
      data: {
        rideId: data.rideId,
        chargeId: data.chargeId || null,
        fare: data.fare,
        taxId: data.taxId || null,
        commissionId: data.commissionId || null,
        totalFare: data.totalFare,
        currency: data.currency || 'INR',
        cityProductId: data.cityProductId || null,
        cityProductFareId: data.cityProductFareId || null,
        subtotal: data.subtotal,
        totalTaxes: data.totalTaxes,
        totalCommissions: data.totalCommissions,
        passengerFare: data.passengerFare,
        driverEarnings: data.driverEarnings,
        platformRevenue: data.platformRevenue,
        calculatedAt: data.calculatedAt || new Date(),
        fareBreakdown: data.fareBreakdown || null,
      },
      include: {
        ride: true,
        charge: true,
        cityProduct: true,
        cityProductFare: true,
      },
    });

    return this.mapToModel(rideFare);
  }

  /**
   * Create ride fare from fare calculation result
   */
  async createRideFareFromCalculation(
    data: CreateRideFareFromCalculationData,
  ): Promise<RideFare> {
    const { rideId, fareCalculationResult } = data;

    const rideFareData: CreateRideFareData = {
      rideId,
      fare: fareCalculationResult.passengerFare,
      totalFare: fareCalculationResult.passengerFare,
      currency: fareCalculationResult.currency,
      cityProductId: fareCalculationResult.cityProductId,
      cityProductFareId: fareCalculationResult.cityProductFareId,
      subtotal: fareCalculationResult.subtotal,
      totalTaxes: fareCalculationResult.totalTaxes,
      totalCommissions: fareCalculationResult.totalCommissions,
      passengerFare: fareCalculationResult.passengerFare,
      driverEarnings: fareCalculationResult.driverEarnings,
      platformRevenue: fareCalculationResult.platformRevenue,
      calculatedAt: fareCalculationResult.calculatedAt,
      fareBreakdown: {
        chargeBreakdown: fareCalculationResult.chargeBreakdown,
        taxBreakdown: fareCalculationResult.taxBreakdown,
        commissionBreakdown: fareCalculationResult.commissionBreakdown,
        calculationSummary: fareCalculationResult.calculationSummary,
      },
    };

    return this.createRideFare(rideFareData);
  }

  /**
   * Create multiple ride fares in a single transaction
   */
  async createBulkRideFares(data: BulkCreateRideFareData): Promise<RideFare[]> {
    const rideFares = await this.prisma.$transaction(
      data.fares.map((fare) =>
        this.prisma.rideFare.create({
          data: {
            rideId: data.rideId,
            chargeId: fare.chargeId || null,
            fare: fare.fare,
            taxId: fare.taxId || null,
            commissionId: fare.commissionId || null,
            totalFare: fare.totalFare,
            currency: fare.currency || 'INR',
            cityProductId: fare.cityProductId || null,
            cityProductFareId: fare.cityProductFareId || null,
            subtotal: fare.subtotal,
            totalTaxes: fare.totalTaxes,
            totalCommissions: fare.totalCommissions,
            passengerFare: fare.passengerFare,
            driverEarnings: fare.driverEarnings,
            platformRevenue: fare.platformRevenue,
            calculatedAt: fare.calculatedAt || new Date(),
            fareBreakdown: fare.fareBreakdown || null,
          },
          include: {
            ride: true,
            charge: true,
            cityProduct: true,
            cityProductFare: true,
          },
        }),
      ),
    );

    return rideFares.map((fare) => this.mapToModel(fare));
  }

  /**
   * Find ride fares by ride ID
   */
  async findByRideId(rideId: string): Promise<RideFare[]> {
    const rideFares = await this.prisma.rideFare.findMany({
      where: {
        rideId,
        deletedAt: null,
      },
      include: {
        ride: true,
        charge: true,
        cityProduct: true,
        cityProductFare: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return rideFares.map((fare) => this.mapToModel(fare));
  }

  /**
   * Find the latest ride fare for a ride
   */
  async findLatestByRideId(rideId: string): Promise<RideFare | null> {
    const rideFare = await this.prisma.rideFare.findFirst({
      where: {
        rideId,
        deletedAt: null,
      },
      include: {
        ride: true,
        charge: true,
        cityProduct: true,
        cityProductFare: true,
      },
      orderBy: {
        calculatedAt: 'desc',
      },
    });

    return rideFare ? this.mapToModel(rideFare) : null;
  }

  /**
   * Find ride fares with filters
   */
  async findWithFilters(filters: RideFareFilters): Promise<RideFare[]> {
    const whereClause: any = {
      deletedAt: null,
    };

    if (filters.rideId) {
      whereClause.rideId = filters.rideId;
    }

    if (filters.chargeId) {
      whereClause.chargeId = filters.chargeId;
    }

    if (filters.cityProductId) {
      whereClause.cityProductId = filters.cityProductId;
    }

    if (filters.cityProductFareId) {
      whereClause.cityProductFareId = filters.cityProductFareId;
    }

    if (filters.currency) {
      whereClause.currency = filters.currency;
    }

    if (filters.minFare !== undefined) {
      whereClause.totalFare = {
        ...whereClause.totalFare,
        gte: filters.minFare,
      };
    }

    if (filters.maxFare !== undefined) {
      whereClause.totalFare = {
        ...whereClause.totalFare,
        lte: filters.maxFare,
      };
    }

    if (filters.calculatedAfter) {
      whereClause.calculatedAt = {
        ...whereClause.calculatedAt,
        gte: filters.calculatedAfter,
      };
    }

    if (filters.calculatedBefore) {
      whereClause.calculatedAt = {
        ...whereClause.calculatedAt,
        lte: filters.calculatedBefore,
      };
    }

    if (filters.createdAfter) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        gte: filters.createdAfter,
      };
    }

    if (filters.createdBefore) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        lte: filters.createdBefore,
      };
    }

    const rideFares = await this.prisma.rideFare.findMany({
      where: whereClause,
      include: {
        ride: true,
        charge: true,
        cityProduct: true,
        cityProductFare: true,
      },
      orderBy: {
        calculatedAt: 'desc',
      },
    });

    return rideFares.map((fare) => this.mapToModel(fare));
  }

  /**
   * Update ride fare by ID
   */
  async updateRideFare(
    id: string,
    data: UpdateRideFareData,
  ): Promise<RideFare> {
    const rideFare = await this.prisma.rideFare.update({
      where: { id },
      data: {
        ...(data.fare !== undefined && { fare: data.fare }),
        ...(data.totalFare !== undefined && { totalFare: data.totalFare }),
        ...(data.currency && { currency: data.currency }),
        ...(data.subtotal !== undefined && { subtotal: data.subtotal }),
        ...(data.totalTaxes !== undefined && { totalTaxes: data.totalTaxes }),
        ...(data.totalCommissions !== undefined && {
          totalCommissions: data.totalCommissions,
        }),
        ...(data.passengerFare !== undefined && {
          passengerFare: data.passengerFare,
        }),
        ...(data.driverEarnings !== undefined && {
          driverEarnings: data.driverEarnings,
        }),
        ...(data.platformRevenue !== undefined && {
          platformRevenue: data.platformRevenue,
        }),
        ...(data.fareBreakdown !== undefined && {
          fareBreakdown: data.fareBreakdown,
        }),
      },
      include: {
        ride: true,
        charge: true,
        cityProduct: true,
        cityProductFare: true,
      },
    });

    return this.mapToModel(rideFare);
  }

  /**
   * Delete ride fare by ID (soft delete)
   */
  async deleteRideFare(id: string): Promise<void> {
    await this.prisma.rideFare.update({
      where: { id },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Delete all ride fares for a ride (soft delete)
   */
  async deleteByRideId(rideId: string): Promise<void> {
    await this.prisma.rideFare.updateMany({
      where: { rideId },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Get ride fare summary for a ride
   */
  async getRideFareSummary(rideId: string): Promise<RideFareSummary | null> {
    const rideFares = await this.findByRideId(rideId);

    if (rideFares.length === 0) {
      return null;
    }

    const latestFare = rideFares[rideFares.length - 1];

    const summary: RideFareSummary = {
      rideId,
      totalFareComponents: rideFares.length,
      totalPassengerFare: latestFare.passengerFare,
      totalDriverEarnings: latestFare.driverEarnings,
      totalPlatformRevenue: latestFare.platformRevenue,
      totalTaxes: latestFare.totalTaxes,
      totalCommissions: latestFare.totalCommissions,
      currency: latestFare.currency,
      faresByCharge: {},
      faresByType: {},
      calculatedAt: latestFare.calculatedAt,
    };

    // Group fares by charge and type
    rideFares.forEach((fare) => {
      if (fare.chargeId) {
        summary.faresByCharge[fare.chargeId] =
          (summary.faresByCharge[fare.chargeId] || 0) + fare.fare;
      }

      const fareType = fare.charge?.chargeType || 'unknown';
      summary.faresByType[fareType] =
        (summary.faresByType[fareType] || 0) + fare.fare;
    });

    return summary;
  }

  /**
   * Check if ride fare exists for a ride
   */
  async existsForRide(rideId: string): Promise<boolean> {
    const count = await this.prisma.rideFare.count({
      where: {
        rideId,
        deletedAt: null,
      },
    });

    return count > 0;
  }

  /**
   * Map Prisma result to model
   */
  private mapToModel(data: any): RideFare {
    return {
      id: data.id,
      rideId: data.rideId,
      chargeId: data.chargeId,
      fare: parseFloat(data.fare.toString()),
      taxId: data.taxId,
      commissionId: data.commissionId,
      totalFare: parseFloat(data.totalFare.toString()),
      currency: data.currency,
      cityProductId: data.cityProductId,
      cityProductFareId: data.cityProductFareId,
      subtotal: parseFloat(data.subtotal.toString()),
      totalTaxes: parseFloat(data.totalTaxes.toString()),
      totalCommissions: parseFloat(data.totalCommissions.toString()),
      passengerFare: parseFloat(data.passengerFare.toString()),
      driverEarnings: parseFloat(data.driverEarnings.toString()),
      platformRevenue: parseFloat(data.platformRevenue.toString()),
      calculatedAt: data.calculatedAt,
      fareBreakdown: data.fareBreakdown,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      ride: data.ride,
      charge: data.charge,
      cityProduct: data.cityProduct,
      cityProductFare: data.cityProductFare,
    };
  }
}

import {
  BookFor,
  PickupType,
  Ride,
  LocationPoint,
} from '@shared/shared/repositories/models/ride.model';
import { CreateRideData, AdminCreateRideData } from '../ride.service';
import {
  BadRequestException,
  NotFoundException,
  Injectable,
  Logger,
} from '@nestjs/common';
import {
  RideRepository,
  RoleRepository,
  UserRepository,
} from '@shared/shared/repositories';
import { RideStatus } from '../../ride-matching/constants';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { CityProductRepository } from '@shared/shared/repositories/city-product.repository';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

@Injectable()
export class RideUtilsService {
  private readonly logger = new Logger(RideUtilsService.name);

  constructor(
    private readonly rideRepository: RideRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly cityProductRepository: CityProductRepository,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
  ) {}

  async validateRideRequest(
    data: CreateRideData | AdminCreateRideData,
  ): Promise<boolean> {
    const rideMode = data.pickupType || PickupType.NOW;

    if (data.bookFor === BookFor.OTHER && !data.riderMeta) {
      throw new BadRequestException(
        'riderMeta is required when booking for another person',
      );
    }

    // Validate pickup time for later rides
    if (rideMode === PickupType.LATER) {
      if (!data.pickupTime) {
        throw new BadRequestException(
          'Pickup time is required when pickupType is later',
        );
      }
      const pickupTime = new Date(data.pickupTime);
      const now = new Date();

      if (pickupTime <= now) {
        throw new BadRequestException('Pickup time must be in the future');
      }
    }
    return true;
  }

  /**
   * Validate and fetch rider profile
   */
  async validateAndFetchRiderProfile(riderId: string): Promise<any> {
    const riderProfile = await this.userProfileRepository.findById(riderId);
    if (!riderProfile) {
      throw new NotFoundException(`Rider with ID ${riderId} not found`);
    }
    return riderProfile;
  }

  /**
   * Validate and fetch city product
   */
  async validateAndFetchCityProduct(cityProductId: string): Promise<any> {
    const cityProduct =
      await this.cityProductRepository.findCityProductById(cityProductId);
    if (!cityProduct) {
      throw new NotFoundException(
        `City product with ID ${cityProductId} not found`,
      );
    }
    return cityProduct;
  }

  /**
   * Validate product from city product
   */
  validateProduct(cityProduct: any, productId?: string): any {
    const product = cityProduct.product;
    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }
    return product;
  }

  /**
   * Generate verification code
   */
  generateVerificationCode(): string {
    return Math.floor(1000 + Math.random() * 9000).toString();
  }

  /**
   * Ensure rider has OTP, generate if needed
   */
  async ensureRiderOtp(riderProfile: any): Promise<string> {
    if (!riderProfile?.rideOtp) {
      const rideOtp = this.generateVerificationCode();
      await this.userProfileRepository.updateById(riderProfile.id, {
        rideOtp,
      });
      return rideOtp;
    }
    return riderProfile.rideOtp;
  }

  async validateActiveRideExists(riderId?: string) {
    if (!riderId) {
      return null;
    }
    const activeRide = await this.rideRepository.findUnique({
      where: {
        riderId,
        status: {
          notIn: [
            RideStatus.TRIP_COMPLETED,
            RideStatus.CANCELLED,
            RideStatus.UNASSIGNED,
            RideStatus.SCHEDULED,
          ],
        },
      },
    });
    if (activeRide) {
      throw new BadRequestException(
        'An active ride exists for this number. Please complete or cancel it before requesting a new one.',
      );
    }
  }

  /**
   * Resolve rider ID for booking scenarios
   * For normal rides: use provided riderId
   * For admin rides booking for others: find or create rider based on riderMeta
   */
  async resolveRiderId(
    data: CreateRideData | AdminCreateRideData,
    isAdminRequest: boolean = false,
  ): Promise<string> {
    // For normal rides, riderId is always provided
    if ('riderId' in data && data.riderId && !isAdminRequest) {
      return data.riderId;
    }

    // For admin requests or booking for others
    if (data.bookFor === BookFor.OTHER && data.riderMeta) {
      const { phoneNumber } = data.riderMeta;

      // Try to find existing rider profile
      const existingRiderProfile = await this.userProfileRepository.findUnique({
        where: {
          role: {
            name: 'rider',
          },
          user: {
            phoneNumber,
          },
        },
      });

      if (existingRiderProfile) {
        this.logger.log(
          `Found existing rider profile for phone ${phoneNumber}: ${existingRiderProfile.id}`,
        );
        return existingRiderProfile.id;
      }

      // For admin requests, if rider doesn't exist, we should create one
      if (isAdminRequest) {
        this.logger.log(
          `Creating new rider profile for admin request with phone ${phoneNumber}`,
        );
        // TODO: Implement rider creation logic here

        // This would involve creating a new user and rider profile
        if (phoneNumber) {
          let user = await this.userRepository.findByPhoneNumber(phoneNumber);
          if (!user) {
            user = await this.userRepository.create({
              phoneNumber,
              email: null,
              emailVerifiedAt: null,
              phoneVerifiedAt: null,
              isPolicyAllowed: null,
              otpSecret: null,
            });
          }
          const riderRole = await this.roleRepository.findByIdentifier('rider');
          if (riderRole) {
            const newRideUserProfile =
              await this.userProfileRepository.createUserProfile(
                user.id,
                riderRole.id,
                {
                  firstName: data.riderMeta.name || '',
                  lastName: '',
                  status: UserProfileStatus.ACTIVE,
                  referralCode: this.generateVerificationCode(),
                } as any,
              );

            return newRideUserProfile.id;
          }
        }
      }
    }

    // Fallback to provided riderId for admin requests
    if ('riderId' in data && data.riderId) {
      return data.riderId;
    }

    throw new BadRequestException('Unable to resolve rider ID for the request');
  }

  /**
   * Validate admin ride request specific requirements
   */
  validateAdminRideRequest(data: AdminCreateRideData): void {
    if (!data.riderMeta) {
      throw new BadRequestException(
        'Admin must provide riderMeta when creating rides',
      );
    }

    if (!data.riderMeta.phoneNumber) {
      throw new BadRequestException(
        'Phone number is required in riderMeta for admin ride requests',
      );
    }
  }

  /**
   * Calculate distance and duration for a route
   */
  async calculateDistanceAndDuration(
    pickup: LocationPoint,
    destination: LocationPoint,
    stops?: LocationPoint[],
  ): Promise<{ duration: number | null; distance: number | null }> {
    try {
      // Convert stops to the format expected by GoogleRouteMatrixService
      const intermediates = stops?.map((stop) => ({
        lat: stop.lat,
        lng: stop.lng,
      }));

      const result =
        await this.googleRouteMatrixService.computeDistanceAndDuration(
          { lat: pickup.lat, lng: pickup.lng },
          { lat: destination.lat, lng: destination.lng },
          intermediates,
        );

      if (!result) {
        this.logger.warn('No route data returned from Google service.');
        return { duration: null, distance: null };
      }

      const durationSeconds = result.duration;
      const distanceMeters = result.distance;

      this.logger.log(
        `Route calculated: ${distanceMeters}m, ${durationSeconds}s${
          stops ? ` with ${stops.length} stops` : ''
        }`,
      );

      return {
        duration: durationSeconds,
        distance: distanceMeters,
      };
    } catch (error: any) {
      this.logger.warn(
        `Failed to calculate route: ${error?.message || 'Unknown error'}`,
      );
      return { duration: null, distance: null };
    }
  }

  /**
   * Create ride data object
   */
  createRideDataObject(
    data: CreateRideData | AdminCreateRideData,
    riderId: string,
    product: any,
    rideMode: PickupType,
    duration: number | null,
    distance: number | null,
    fareSpec: any,
    verificationCode: string | null,
    pickupTime: Date | null = null,
  ): Omit<Ride, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> {
    return {
      riderId: riderId,
      productId: product.id,
      cityProductId: data.cityProductId,
      status:
        rideMode === PickupType.LATER
          ? RideStatus.SCHEDULED
          : RideStatus.REQUESTED,
      pickupLocation: data.pickup as LocationPoint,
      destinationLocation: data.destination as LocationPoint,
      stops: data.stops || null,
      driverId: null,
      completedAt: null,
      duration,
      distance,
      fareSpec,
      riderMeta: data.riderMeta || null,
      bookFor: data.bookFor ?? BookFor.ME,
      createdBy: data.createdBy ?? null,
      verificationCode: verificationCode,
      pickupType: rideMode,
      pickupTime: pickupTime,
    };
  }

  /**
   * Create lifecycle data object
   */
  createLifecycleDataObject(
    rideMode: PickupType,
    pickupTime: Date | null,
    startTime: number,
    fareCalculated: boolean,
  ): any {
    return {
      status:
        rideMode === PickupType.LATER
          ? RideStatus.SCHEDULED
          : RideStatus.REQUESTED,
      meta: {
        notes: `Ride ${
          rideMode === PickupType.LATER ? 'scheduled' : 'requested'
        } by rider`,
        mode: rideMode,
        pickupTime: pickupTime,
        processingStartTime: startTime,
        fareCalculated: fareCalculated,
      },
    };
  }

  /**
   * Parse pickup time string to Date object
   */
  parsePickupTime(pickupTimeString?: string): Date | null {
    if (!pickupTimeString) {
      return null;
    }
    try {
      return new Date(pickupTimeString);
    } catch (error) {
      this.logger.warn(`Failed to parse pickup time: ${pickupTimeString}`);
      return null;
    }
  }

  /**
   * Log ride creation success
   */
  logRideCreationSuccess(
    rideId: string,
    processingTime: number,
    rideMode: PickupType,
  ): void {
    this.logger.log(
      `Ride ${rideId} created successfully in ${processingTime}ms (mode: ${rideMode})`,
    );
  }

  /**
   * Log ride creation error
   */
  logRideCreationError(
    riderId: string,
    processingTime: number,
    error: any,
  ): void {
    this.logger.error(
      `Failed to create ride for rider ${riderId} after ${processingTime}ms:`,
      error,
    );
  }
}

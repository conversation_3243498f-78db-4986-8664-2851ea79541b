import { Test, TestingModule } from '@nestjs/testing';
import { RideService } from './ride.service';
import { PaymentService } from '../payment/payment.service';
import { DriverAccountService } from '../driver-account/driver-account.service';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { PaymentRepository } from '@shared/shared/repositories/payment.repository';
import { DriverAccountRepository } from '@shared/shared/repositories/driver-account.repository';
import { PaymentType } from '@shared/shared/repositories/models/payment.model';
import { TransactionType } from '@shared/shared/repositories/models/driverAccount.model';
import { RideStatus } from '../ride-matching/constants';

describe('Ride Completion Integration', () => {
  let rideService: RideService;
  let paymentService: PaymentService;
  let driverAccountService: DriverAccountService;
  let rideRepository: RideRepository;
  let paymentRepository: PaymentRepository;
  let driverAccountRepository: DriverAccountRepository;

  // Mock data
  const mockRideId = 'test-ride-123';
  const mockDriverId = 'test-driver-456';
  const mockRiderId = 'test-rider-789';

  const mockFareSpec = {
    passengerFare: 150,
    totalTaxes: 20,
    totalCommissions: 30,
    grandTotal: 150,
  };

  const mockRide = {
    id: mockRideId,
    riderId: mockRiderId,
    driverId: mockDriverId,
    status: RideStatus.TRIP_COMPLETED,
    fareSpec: mockFareSpec,
    pickupLocation: { lat: 12.9716, lng: 77.5946 },
    destinationLocation: { lat: 12.9716, lng: 77.5946 },
    distance: 5000,
    duration: 1200,
    completedAt: new Date(),
  };

  const mockPayment = {
    id: 'payment-123',
    rideId: mockRideId,
    riderId: mockRiderId,
    driverId: mockDriverId,
    amount: 150,
    paymentType: PaymentType.CASH,
    receivedAt: new Date(),
  };

  const mockDriverAccount = {
    id: 'account-123',
    driverId: mockDriverId,
    availableBalance: 1000,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RideService,
        PaymentService,
        DriverAccountService,
        {
          provide: RideRepository,
          useValue: {
            findById: jest.fn(),
            updateById: jest.fn(),
          },
        },
        {
          provide: PaymentRepository,
          useValue: {
            createPayment: jest.fn(),
            findByRideId: jest.fn(),
            confirmPayment: jest.fn(),
          },
        },
        {
          provide: DriverAccountRepository,
          useValue: {
            findByDriverId: jest.fn(),
            updateBalance: jest.fn(),
            getOrCreateDriverAccount: jest.fn(),
          },
        },
        // Add other required dependencies as mocks
        {
          provide: 'RideLifecycleRepository',
          useValue: { createRideLifecycle: jest.fn() },
        },
        {
          provide: 'UserProfileRepository',
          useValue: { findById: jest.fn() },
        },
        {
          provide: 'RabbitMQEventPublisher',
          useValue: { publishRideCompleted: jest.fn() },
        },
        // Add more mocks as needed...
      ],
    }).compile();

    rideService = module.get<RideService>(RideService);
    paymentService = module.get<PaymentService>(PaymentService);
    driverAccountService =
      module.get<DriverAccountService>(DriverAccountService);
    rideRepository = module.get<RideRepository>(RideRepository);
    paymentRepository = module.get<PaymentRepository>(PaymentRepository);
    driverAccountRepository = module.get<DriverAccountRepository>(
      DriverAccountRepository,
    );
  });

  describe('Payment Creation and Driver Earnings', () => {
    it('should calculate correct earnings breakdown', () => {
      const earningsBreakdown =
        driverAccountService.calculateEarnings(mockFareSpec);

      expect(earningsBreakdown).toEqual({
        totalCollected: 150,
        fareTax: 20,
        commission: 30,
        taxOnCommission: 5.4, // 30 * 0.18
        driverNetEarnings: 94.6, // 150 - (20 + 30 + 5.4)
        tukxiEarnings: 55.4, // 20 + 30 + 5.4
      });
    });

    it('should create payment record during ride completion', async () => {
      jest
        .spyOn(paymentRepository, 'createPayment')
        .mockResolvedValue(mockPayment);

      const payment = await paymentService.createPayment({
        rideId: mockRideId,
        riderId: mockRiderId,
        driverId: mockDriverId,
        amount: 150,
      });

      expect(paymentRepository.createPayment).toHaveBeenCalledWith({
        rideId: mockRideId,
        riderId: mockRiderId,
        driverId: mockDriverId,
        amount: 150,
      });
      expect(payment).toEqual(mockPayment);
    });

    it('should update driver balance correctly for cash payment', async () => {
      const earningsBreakdown = {
        totalCollected: 150,
        fareTax: 20,
        commission: 30,
        taxOnCommission: 5.4,
        driverNetEarnings: 94.6,
        tukxiEarnings: 55.4,
      };

      jest
        .spyOn(driverAccountRepository, 'getOrCreateDriverAccount')
        .mockResolvedValue(mockDriverAccount);
      jest.spyOn(driverAccountRepository, 'updateBalance').mockResolvedValue({
        ...mockDriverAccount,
        availableBalance: 944.6, // 1000 - 55.4
      });

      const result = await driverAccountService.updateDriverBalance(
        mockDriverId,
        mockRideId,
        PaymentType.CASH,
        earningsBreakdown,
      );

      expect(result.availableBalance).toBe(944.6);
    });

    it('should update driver balance correctly for online payment', async () => {
      const earningsBreakdown = {
        totalCollected: 150,
        fareTax: 20,
        commission: 30,
        taxOnCommission: 5.4,
        driverNetEarnings: 94.6,
        tukxiEarnings: 55.4,
      };

      jest
        .spyOn(driverAccountRepository, 'getOrCreateDriverAccount')
        .mockResolvedValue(mockDriverAccount);
      jest.spyOn(driverAccountRepository, 'updateBalance').mockResolvedValue({
        ...mockDriverAccount,
        availableBalance: 1094.6, // 1000 + 94.6
      });

      const result = await driverAccountService.updateDriverBalance(
        mockDriverId,
        mockRideId,
        PaymentType.ONLINE,
        earningsBreakdown,
      );

      expect(result.availableBalance).toBe(1094.6);
    });
  });

  describe('RideMeter and RideFare Updates', () => {
    it('should update trip duration in RideMeter when ride ends', async () => {
      const mockMeters = [
        { id: 'meter-1', name: 'trip_duration', value: 1200, unit: 'seconds' },
        { id: 'meter-2', name: 'trip_distance', value: 5.5, unit: 'km' },
      ];

      jest
        .spyOn(rideService as any, 'rideMeterService', 'get')
        .mockReturnValue({
          getRideMetersByRideId: jest.fn().mockResolvedValue(mockMeters),
          updateRideMeter: jest.fn().mockResolvedValue({}),
        });

      await (rideService as any).updateActualTripDuration(mockRideId, 1500);

      expect(
        rideService['rideMeterService'].updateRideMeter,
      ).toHaveBeenCalledWith('meter-1', {
        value: 1500,
      });
    });

    it('should update RideFare table with final fare calculation', async () => {
      const mockFareSpec = {
        passengerFare: 150,
        totalTaxes: 20,
        totalCommissions: 30,
      };

      jest.spyOn(rideService as any, 'rideFareService', 'get').mockReturnValue({
        createRideFareFromCalculation: jest.fn().mockResolvedValue({}),
      });

      await (rideService as any).updateRideFareRecord(mockRideId, mockFareSpec);

      expect(
        rideService['rideFareService'].createRideFareFromCalculation,
      ).toHaveBeenCalledWith({
        rideId: mockRideId,
        fareCalculationResult: mockFareSpec,
      });
    });
  });

  describe('Payment Confirmation Flow', () => {
    it('should confirm payment and update driver balance', async () => {
      jest.spyOn(rideRepository, 'findById').mockResolvedValue(mockRide);
      jest
        .spyOn(paymentService, 'confirmPayment')
        .mockResolvedValue(mockPayment);
      jest.spyOn(driverAccountService, 'calculateEarnings').mockReturnValue({
        totalCollected: 150,
        fareTax: 20,
        commission: 30,
        taxOnCommission: 5.4,
        driverNetEarnings: 94.6,
        tukxiEarnings: 55.4,
      });
      jest
        .spyOn(driverAccountService, 'updateDriverBalance')
        .mockResolvedValue(mockDriverAccount);

      const result = await rideService.confirmRidePayment(
        mockRideId,
        PaymentType.CASH,
      );

      expect(result.payment).toEqual(mockPayment);
      expect(result.driverAccount).toEqual(mockDriverAccount);
      expect(paymentService.confirmPayment).toHaveBeenCalledWith({
        rideId: mockRideId,
        paymentType: PaymentType.CASH,
        amount: 150,
      });
    });
  });
});

import { Module } from '@nestjs/common';
import { DriverCityProductController } from './driver-city-product.controller';
import { DriverCityProductModule as SharedDriverCityProductModule } from '@shared/shared/modules/driver-city-product/driver-city-product.module';

@Module({
  imports: [SharedDriverCityProductModule],
  controllers: [DriverCityProductController],
})
export class ApiDriverCityProductModule {}

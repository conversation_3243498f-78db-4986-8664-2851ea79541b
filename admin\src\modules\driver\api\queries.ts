import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   DriverResponse,
   ListDriverParams,
   ListDriverResponse,
   KycDocumentsResponse,
   DriverVehiclesResponse,
   VehicleDocumentsResponse,
} from '../types/driver';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListDriver = ({
   page = 1,
   limit = 10,
   search,
   sortBy,
   sortOrder,
   cityId,
   name,
   email,
   phoneNumber,
   status,
   vehicleTypeId,
}: ListDriverParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      enabled: hasPermission(RBAC_PERMISSIONS.DRIVER.LIST),
      placeholderData: keepPreviousData,
      queryKey: [
         'drivers',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
         cityId,
         name,
         email,
         phoneNumber,
         status,
         vehicleTypeId,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListDriverResponse> => {
         return apiClient.get('/drivers/admin', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
               search,
               cityId,
               name,
               email,
               phoneNumber,
               status,
               vehicleTypeId,
            },
         });
      },
   });
};

export const useGetDriver = (id: string | null) => {
   return useQuery({
      queryKey: ['driver', id],
      queryFn: (): Promise<DriverResponse> => {
         return apiClient.get(`/drivers/admin/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for fetching KYC documents for a driver
 */
export const useGetDriverKycDocuments = (profileId: string) => {
   return useQuery({
      queryKey: ['driver-kyc-documents', profileId],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<KycDocumentsResponse> => {
         return apiClient.get(`/driver-kyc/admin/list-documents/user-profile/${profileId}`);
      },
      enabled: !!profileId,
   });
};

// Re-export language hook from language module
export { useListLanguage as useLanguages } from '@/modules/language/api/queries';

// Re-export vehicle types hook from vehicle-category module
export { useListVehicleCategory as useVehicleTypes } from '@/modules/vehicle-category/api/queries';

// Re-export cities hook from city module
export { useAllCities as useCities } from '@/modules/city/api/queries';

/**
 * Hook for fetching driver vehicles by profile ID
 */
export const useDriverVehicles = (profileId: string) => {
   return useQuery({
      queryKey: ['driver-vehicles', profileId],
      queryFn: (): Promise<DriverVehiclesResponse> => {
         return apiClient.get(`/driver-vehicles/admin/list/user-profile/${profileId}`);
      },
      enabled: !!profileId,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for fetching vehicle documents by vehicle ID
 */
export const useVehicleDocuments = (vehicleId: string) => {
   return useQuery({
      queryKey: ['vehicle-documents', vehicleId],
      queryFn: (): Promise<VehicleDocumentsResponse> => {
         return apiClient.get(`/driver-vehicles/${vehicleId}/documents`);
      },
      enabled: !!vehicleId,
      refetchOnWindowFocus: false,
   });
};

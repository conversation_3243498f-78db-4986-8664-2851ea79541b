import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { RiderDetailsResponse, ListRidersParams, RiderListResponse } from '../types/rider';

/**
 * Hook for listing riders with pagination and filters
 */
export const useListRiders = ({ page, limit, search, name, email, phoneNumber, cityId, status, sortBy, sortOrder }: ListRidersParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.RIDER.LIST),
    queryKey: ['riders', page, limit, search, name, email, phoneNumber, cityId, status, sortBy, sortOrder],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<RiderListResponse> => {
      return apiClient.get('/admin/riders', {
        params: { page, limit, search, name, email, phoneNumber, cityId, status, sortBy, sortOrder },
      });
    },
  });
};

/**
 * Hook for getting a single rider by ID (for details page)
 */
export const useGetRider = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['rider', id],
    queryFn: (): Promise<RiderDetailsResponse> => {
      return apiClient.get(`/admin/riders/${id || ''}`);
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.RIDER.VIEW),
    refetchOnWindowFocus: false,
  });
};

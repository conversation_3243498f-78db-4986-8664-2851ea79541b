'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Sheet,
   SheetContent,
   SheetDescription,
   SheetFooter,
   SheetHeader,
   SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { UserPlus } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQueryClient } from '@tanstack/react-query';
import { useListRole } from '@/modules/role/api/queries';
import { useAllCities } from '@/modules/city/api/queries';
import { useInviteAdmin, useInviteCityAdmin } from '../api/mutations';
import { toast } from '@/lib/toast';
import { Role } from '@/modules/role/types/role';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';

const createInviteSchema = (roles: Role[]) =>
   z
      .object({
         firstName: z
            .string()
            .min(1, 'First name is required')
            .max(20, 'First name cannot exceed 20 characters')
            .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
         lastName: z
            .string()
            .min(1, 'Last name is required')
            .max(20, 'Last name cannot exceed 20 characters')
            .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
         email: z
            .string()
            .min(1, 'Email is required')
            .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address'),
         roleId: z.string().min(1, 'Please select a role'),
         cityIds: z.array(z.string()).optional(),
      })
      .refine(
         data => {
            const selectedRole = roles.find(role => role.id === data.roleId);
            if (selectedRole?.identifier === 'city_admin') {
               return data.cityIds && data.cityIds.length > 0;
            }
            return true;
         },
         {
            message: 'At least one city must be selected for city admin role',
            path: ['cityIds'],
         }
      );

type InviteFormValues = z.infer<ReturnType<typeof createInviteSchema>>;

interface InviteAdminSheetProps {
   selectedCity?: string;
   selectedAdminType?: string;
   label?: string;
}

export const InviteAdminSheet = ({
   selectedCity,
   selectedAdminType,
   label,
}: InviteAdminSheetProps = {}) => {
   const [open, setOpen] = useState(false);
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [selectedCityOptions, setSelectedCityOptions] = useState<Option[]>([]);

   const queryClient = useQueryClient();
   const rolesQuery = useListRole({ limit: 100 });
   const citiesQuery = useAllCities();
   const { withPermission, hasPermission } = useRoleBasedAccess();
   const hasCityAdminPermission = hasPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE);
   const hasSubAdminPermission = hasPermission(RBAC_PERMISSIONS.SUB_ADMIN.CREATE);

   const inviteAdminMutation = useInviteAdmin();
   const inviteCityAdminMutation = useInviteCityAdmin();

   const roles = useMemo(() => {
      const allRoles = rolesQuery.data?.data || [];

      // If selectedAdminType is provided, filter to only show that specific role
      if (selectedAdminType) {
         return allRoles.filter(role => role.identifier === selectedAdminType);
      }

      // If selectedCity is provided, filter to only show city_admin role
      if (selectedCity) {
         return allRoles.filter(role => role.identifier === ROLE_IDENTIFIERS.CITY_ADMIN);
      } else {
         // Case 1: if neither CITY_ADMIN nor SUB_ADMIN permission
         if (!hasCityAdminPermission && !hasSubAdminPermission) {
            return allRoles.filter(
               role =>
                  role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN &&
                  role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN
            );
         }

         // Case 2: has only CITY_ADMIN permission, so block sub_admins
         if (hasCityAdminPermission && !hasSubAdminPermission) {
            return allRoles.filter(role => role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN);
         }

         // Case 3: has only SUB_ADMIN permission, so block city_admins
         if (!hasCityAdminPermission && hasSubAdminPermission) {
            return allRoles.filter(role => role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN);
         }

         // Case 4: has both CITY_ADMIN and SUB_ADMIN permission
         return allRoles;
      }
   }, [
      hasCityAdminPermission,
      hasSubAdminPermission,
      rolesQuery.data?.data,
      selectedCity,
      selectedAdminType,
   ]);

   const inviteSchema = useMemo(() => createInviteSchema(roles), [roles]);

   const form = useForm<InviteFormValues>({
      resolver: zodResolver(inviteSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         email: '',
         roleId: '',
         cityIds: [],
      },
   });

   const {
      formState: { errors },
      reset: resetForm,
      control,
      watch,
      setValue,
   } = form;

   const watchedRoleId = watch('roleId');
   const watchedCityIds = watch('cityIds');

   const cities = useMemo(() => citiesQuery.data?.data || [], [citiesQuery.data?.data]);

   // Convert cities to MultipleSelector Option format
   const cityOptions: Option[] = useMemo(() => {
      return cities.map(city => ({
         value: city.id,
         label: city.name,
      }));
   }, [cities]);

   // Set default role when roles are loaded
   useEffect(() => {
      if (roles.length > 0 && !watchedRoleId) {
         setValue('roleId', roles[0].id);
      }
   }, [roles, watchedRoleId, setValue]);

   // Pre-populate cityIds when selectedCity is provided
   useEffect(() => {
      if (selectedCity && (!watchedCityIds || watchedCityIds.length === 0)) {
         setValue('cityIds', [selectedCity]);
         const selectedCityOption = cityOptions.find(option => option.value === selectedCity);
         if (selectedCityOption) {
            setSelectedCityOptions([selectedCityOption]);
         }
      }
   }, [selectedCity, watchedCityIds, setValue, cityOptions]);

   // Sync cityIds form value with selectedCityOptions
   useEffect(() => {
      if (watchedCityIds) {
         const options = cityOptions.filter(option => watchedCityIds.includes(option.value));
         setSelectedCityOptions(options);
      }
   }, [watchedCityIds, cityOptions]);

   // Handle city selection change
   const handleCitySelectionChange = (options: Option[]) => {
      setSelectedCityOptions(options);
      setValue(
         'cityIds',
         options.map(option => option.value)
      );
   };

   // Reset all errors when modal opens
   useEffect(() => {
      form.clearErrors();
   }, [form, open]);

   const handleSubmit = async (data: InviteFormValues) => {
      setIsSubmitting(true);

      try {
         const selectedRole = roles.find(role => role.id === data.roleId);
         if (!selectedRole) {
            toast.error('Please select a valid role');
            return;
         }

         // Check if city is required for city_admins
         if (
            (selectedRole.identifier === 'city_admin' || selectedCity) &&
            (!data.cityIds || data.cityIds.length === 0)
         ) {
            toast.error('At least one city is required for city admins');
            return;
         }

         // Determine which API to use based on role
         if (selectedRole.identifier === 'city_admin') {
            if (!data.cityIds || data.cityIds.length === 0) {
               throw new Error('At least one city is required for city admin role');
            }

            const response = await inviteCityAdminMutation.mutateAsync({
               firstName: data.firstName,
               lastName: data.lastName,
               email: data.email,
               roleId: data.roleId,
               cityIds: data.cityIds,
            });

            // Show success message
            toast.success(response.message);
         } else {
            const response = await inviteAdminMutation.mutateAsync({
               firstName: data.firstName,
               lastName: data.lastName,
               email: data.email,
               roleId: data.roleId,
            });

            // Show success message
            toast.success(response.message);
         }

         // Success - invalidate queries to refresh admin lists
         queryClient.invalidateQueries({ queryKey: ['admins', 'role'] });
         queryClient.invalidateQueries({ queryKey: ['admins', 'role', data.roleId] });

         // Invalidate city admins queries if applicable
         if (selectedCity) {
            queryClient.invalidateQueries({ queryKey: ['city-admins', selectedCity] });
            queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         }

         // Also invalidate for all cities that were assigned to city admins
         if (selectedRole.identifier === 'city_admin' && data.cityIds) {
            data.cityIds.forEach(cityId => {
               queryClient.invalidateQueries({ queryKey: ['city-admins', cityId] });
            });
            queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         }

         // Reset state and close modal
         handleClose();
      } catch (error: any) {
         console.error('Error sending invitation:', error);
         toast.error(
            error?.response?.data?.message ||
               error?.message ||
               'Failed to send invitation. Please try again.'
         );
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleClose = () => {
      setOpen(false);
      setSelectedCityOptions([]);
      resetForm();
      // Reset to first role again
      if (roles.length > 0) {
         setValue('roleId', roles[0].id);
      }
      setValue('cityIds', []);
   };

   return (
      <>
         <Button
            className='cursor-pointer'
            variant='outline'
            onClick={() => {
               if (selectedCity) {
                  withPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE, () => setOpen(true));
               } else if (selectedAdminType === ROLE_IDENTIFIERS.CITY_ADMIN) {
                  withPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE, () => setOpen(true));
               } else if (selectedAdminType === ROLE_IDENTIFIERS.SUB_ADMIN) {
                  withPermission(RBAC_PERMISSIONS.SUB_ADMIN.CREATE, () => setOpen(true));
               } else {
                  if (hasCityAdminPermission || hasSubAdminPermission) {
                     setOpen(true);
                  } else {
                     toast.error("You don't have permission for this action");
                  }
               }
            }}
         >
            <UserPlus />
            {label ? label : 'Invite Admin'}
         </Button>
         <Sheet open={open} onOpenChange={setOpen}>
            <SheetContent preventOutsideClickClose={true} className='w-[441px] sm:max-w-[441px]'>
               <SheetHeader className='pb-0'>
                  <SheetTitle>Invite Admin</SheetTitle>
                  <SheetDescription>
                     Fill in the admin details and send them an invite link to join as an
                     administrator
                  </SheetDescription>
               </SheetHeader>

               <div className='flex flex-col h-full'>
                  <ScrollArea
                     scrollThumbClassName='bg-gray-400'
                     type='always'
                     className='flex-1 px-6'
                     scrollViewPortClassName=''
                  >
                     <div className='space-y-6 pb-4'>
                        <div className='grid grid-cols-2 gap-4'>
                           <div>
                              <Label className='text-sm font-medium'>First Name</Label>
                              <Controller
                                 control={control}
                                 name='firstName'
                                 render={({ field }) => (
                                    <Input placeholder='John' {...field} className='mt-1' />
                                 )}
                              />
                              {errors.firstName && <ErrorMessage error={errors.firstName} />}
                           </div>
                           <div>
                              <Label className='text-sm font-medium'>Last Name</Label>
                              <Controller
                                 control={control}
                                 name='lastName'
                                 render={({ field }) => (
                                    <Input placeholder='Doe' {...field} className='mt-1' />
                                 )}
                              />
                              {errors.lastName && <ErrorMessage error={errors.lastName} />}
                           </div>
                        </div>

                        <div className='space-y-4'>
                           <div>
                              <Label className='text-sm font-medium'>Email address</Label>
                              <Controller
                                 control={control}
                                 name='email'
                                 render={({ field }) => (
                                    <Input
                                       placeholder='<EMAIL>'
                                       {...field}
                                       className='mt-1'
                                    />
                                 )}
                              />
                              {errors.email && <ErrorMessage error={errors.email} />}
                           </div>

                           <div>
                              <Label className='text-sm font-medium'>Type of Admin</Label>
                              <Controller
                                 control={control}
                                 name='roleId'
                                 render={({ field }) => (
                                    <Select
                                       value={field.value}
                                       onValueChange={field.onChange}
                                       disabled={!!selectedCity || !!selectedAdminType}
                                    >
                                       <SelectTrigger className='mt-1 w-full'>
                                          <SelectValue placeholder='Select Admin Type' />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {roles.map(role => (
                                             <SelectItem key={role.id} value={role.id}>
                                                {role.name.replaceAll('_', ' ')}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 )}
                              />
                              {errors.roleId && <ErrorMessage error={errors.roleId} />}
                           </div>

                           {(watchedRoleId &&
                              roles.find(role => role.id === watchedRoleId)?.identifier ===
                                 'city_admin') ||
                           selectedCity ? (
                              <div>
                                 <Label className='text-sm font-medium'>Cities</Label>
                                 <MultipleSelector
                                    value={selectedCityOptions}
                                    onChange={handleCitySelectionChange}
                                    defaultOptions={cityOptions}
                                    placeholder='Select cities...'
                                    disabled={!!selectedCity}
                                    className='mt-1'
                                    emptyIndicator={
                                       <p className='text-center text-sm text-gray-500'>
                                          No cities found
                                       </p>
                                    }
                                    commandProps={{
                                       label: 'Select cities',
                                    }}
                                 />
                                 {errors.cityIds && <ErrorMessage error={errors.cityIds} />}
                              </div>
                           ) : null}
                        </div>
                     </div>
                  </ScrollArea>
               </div>

               <SheetFooter className='border-t pt-4 px-6'>
                  <div className='flex gap-3 w-full'>
                     <Button
                        type='button'
                        variant='outline'
                        onClick={handleClose}
                        disabled={isSubmitting}
                        className='flex-1'
                     >
                        Cancel
                     </Button>
                     <Button
                        type='button'
                        onClick={form.handleSubmit(handleSubmit)}
                        disabled={isSubmitting}
                        className='flex-1'
                     >
                        {isSubmitting ? 'Sending...' : 'Send Invite'}
                     </Button>
                  </div>
               </SheetFooter>
            </SheetContent>
         </Sheet>
      </>
   );
};

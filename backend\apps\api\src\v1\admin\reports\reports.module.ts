import { Module } from '@nestjs/common';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';
import { ReportsRepository } from './reports.repository';

@Module({
  providers: [ReportsService, ReportsRepository, PrismaService],
  controllers: [ReportsController],
  exports: [ReportsService],
})
export class ReportsModule {}

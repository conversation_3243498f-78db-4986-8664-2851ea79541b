import { <PERSON><PERSON>rray, IsNotEmpty, IsUUID, ArrayMinSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RemoveCityAdminsDto {
  @ApiProperty({
    description:
      'Array of City Admin UUIDs to permanently remove (at least one required)',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '456e7890-e89b-12d3-a456-************',
    ],
    type: [String],
    required: true,
  })
  @IsNotEmpty({ message: 'City admin IDs array cannot be empty' })
  @IsArray({ message: 'City admin IDs must be an array' })
  @ArrayMinSize(1, { message: 'At least one city admin ID is required' })
  @IsUUID('4', {
    each: true,
    message: 'Each city admin ID must be a valid UUID',
  })
  cityAdminIds!: string[];
}

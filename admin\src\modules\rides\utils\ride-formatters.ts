export function formatStatus(status: string): string {
   return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
}

export function getStatusBadgeClass(status: string): string {
   const normalizedStatus = status.toLowerCase();

   if (normalizedStatus.includes('completed')) {
      return 'bg-green-50 text-green-700 border-green-200';
   }
   if (normalizedStatus.includes('cancelled')) {
      return 'bg-red-50 text-red-700 border-red-200';
   }
   if (normalizedStatus.includes('progress') || normalizedStatus.includes('accepted')) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
   }
   if (normalizedStatus.includes('requested') || normalizedStatus.includes('processing')) {
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
   }
   return 'bg-gray-50 text-gray-700 border-gray-200';
}

export function formatDuration(seconds: number | null | undefined): string {
   if (!seconds) return 'N/A';
   const hours = Math.floor(seconds / 3600);
   const minutes = Math.floor((seconds % 3600) / 60);
   if (hours > 0) {
      return `${hours}h ${minutes}m`;
   }
   return `${minutes}m`;
}

export function formatDistance(meters: number | null | undefined): string {
   if (!meters) return 'N/A';
   const km = meters / 1000;
   if (km >= 1) {
      return `${km.toFixed(2)} km`;
   }
   return `${meters} m`;
}

export function formatCurrency(amount: number | null | undefined, currency: string): string {
   if (amount === null || amount === undefined) return 'N/A';
   return `${currency} ${amount.toFixed(2)}`;
}

export function formatChargeType(type: string): string {
   return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
}

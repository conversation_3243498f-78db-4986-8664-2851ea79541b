import { z } from 'zod';

const envSchema = z.object({
  NEXT_PUBLIC_BASE_URL: z.string().url(),
});

const processEnv = {
  NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
};

const parsed = envSchema.safeParse(processEnv);

if (!parsed.success) {
  // console.error(
  //    '❌ Invalid environment variables:',
  //    JSON.stringify(parsed.error.format(), null, 4)
  // );
  // process.exit(1);
  console.warn('env missing');
}

export const env = parsed.data;

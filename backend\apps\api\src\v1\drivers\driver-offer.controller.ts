// import {
//   Controller,
//   Post,
//   Body,
//   Param,
//   Req,
//   HttpCode,
//   HttpStatus,
//   UseGuards,
//   BadRequestException,
//   NotFoundException,
//   Get,
//   Query,
// } from '@nestjs/common';
// import {
//   ApiTags,
//   ApiOperation,
//   ApiResponse,
//   ApiBearerAuth,
//   ApiParam,
//   ApiQuery,
// } from '@nestjs/swagger';
// import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
// import { RideOfferRepository } from '@shared/shared/repositories/ride-offer.repository';
// import { RideOfferStatus } from '@shared/shared/modules/ride-matching/constants';

// export interface OfferResponseDto {
//   accepted: boolean;
//   reason?: string;
// }

// export interface OfferResponseResultDto {
//   success: boolean;
//   message: string;
//   data: {
//     offerId: string;
//     accepted: boolean;
//     processedAt: string;
//   };
//   timestamp: number;
// }

// export interface DriverOffersListDto {
//   success: boolean;
//   message: string;
//   data: {
//     offers: Array<{
//       id: string;
//       rideId: string;
//       batchNumber: number;
//       score: number;
//       algorithm: string;
//       status: string;
//       offeredAt: string;
//       expiresAt: string;
//       responseAt?: string;
//       rejectionReason?: string;
//       ride?: {
//         pickupLocation: any;
//         destinationLocation: any;
//         riderId: string;
//       };
//     }>;
//     total: number;
//     page: number;
//     limit: number;
//   };
//   timestamp: number;
// }

// @ApiTags('Driver Offers')
// @Controller('drivers/offers')
// @UseGuards(JwtAuthGuard)
// @ApiBearerAuth()
// export class DriverOfferController {
//   constructor(
//     private readonly rideOfferRepository: RideOfferRepository,
//   ) { }

//   @Post(':offerId/accept')
//   @HttpCode(HttpStatus.OK)
//   @ApiOperation({
//     summary: 'Accept a ride offer',
//     description:
//       'Driver accepts a ride offer. The offer must be valid and not expired.',
//   })
//   @ApiParam({
//     name: 'offerId',
//     description: 'ID of the offer to accept',
//     example: 'offer-uuid-123',
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'Offer accepted successfully',
//     schema: {
//       type: 'object',
//       properties: {
//         success: { type: 'boolean', example: true },
//         message: { type: 'string', example: 'Offer accepted successfully' },
//         data: {
//           type: 'object',
//           properties: {
//             offerId: { type: 'string', example: 'offer-uuid-123' },
//             accepted: { type: 'boolean', example: true },
//             processedAt: { type: 'string', example: '2024-01-01T12:00:00Z' },
//           },
//         },
//         timestamp: { type: 'number', example: 1640995200000 },
//       },
//     },
//   })
//   @ApiResponse({
//     status: 400,
//     description: 'Bad request - offer expired, already processed, or invalid',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Offer not found',
//   })
//   async acceptOffer(
//     @Param('offerId') offerId: string,
//     @Req() req: any,
//   ): Promise<OfferResponseResultDto> {
//     const driverId = req.user.profileId;

//     if (!driverId) {
//       throw new BadRequestException('Driver profile not found');
//     }

//     try {
//       const result = await this.rideOfferService.processOfferResponse(
//         offerId,
//         driverId,
//         true,
//       );

//       return {
//         success: true,
//         message: 'Offer accepted successfully',
//         data: {
//           offerId: result.offerId,
//           accepted: result.accepted,
//           processedAt: new Date().toISOString(),
//         },
//         timestamp: Date.now(),
//       };
//     } catch (error) {
//       if (error instanceof Error) {
//         if (error.message.includes('not found')) {
//           throw new NotFoundException(error.message);
//         }
//         throw new BadRequestException(error.message);
//       }
//       throw new BadRequestException('Failed to process offer acceptance');
//     }
//   }

//   @Post(':offerId/reject')
//   @HttpCode(HttpStatus.OK)
//   @ApiOperation({
//     summary: 'Reject a ride offer',
//     description: 'Driver rejects a ride offer with an optional reason.',
//   })
//   @ApiParam({
//     name: 'offerId',
//     description: 'ID of the offer to reject',
//     example: 'offer-uuid-123',
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'Offer rejected successfully',
//   })
//   @ApiResponse({
//     status: 400,
//     description: 'Bad request - offer expired, already processed, or invalid',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Offer not found',
//   })
//   async rejectOffer(
//     @Param('offerId') offerId: string,
//     @Body() body: OfferResponseDto,
//     @Req() req: any,
//   ): Promise<OfferResponseResultDto> {
//     const driverId = req.user.profileId;

//     if (!driverId) {
//       throw new BadRequestException('Driver profile not found');
//     }

//     try {
//       const result = await this.rideOfferService.processOfferResponse(
//         offerId,
//         driverId,
//         false,
//         body.reason,
//       );

//       return {
//         success: true,
//         message: 'Offer rejected successfully',
//         data: {
//           offerId: result.offerId,
//           accepted: result.accepted,
//           processedAt: new Date().toISOString(),
//         },
//         timestamp: Date.now(),
//       };
//     } catch (error) {
//       if (error instanceof Error) {
//         if (error.message.includes('not found')) {
//           throw new NotFoundException(error.message);
//         }
//         throw new BadRequestException(error.message);
//       }
//       throw new BadRequestException('Failed to process offer rejection');
//     }
//   }

//   @Get('my-offers')
//   @ApiOperation({
//     summary: 'Get driver offers',
//     description: 'Get paginated list of offers for the authenticated driver',
//   })
//   @ApiQuery({
//     name: 'status',
//     required: false,
//     description: 'Filter by offer status',
//     enum: ['pending', 'accepted', 'rejected', 'expired', 'cancelled'],
//   })
//   @ApiQuery({
//     name: 'page',
//     required: false,
//     description: 'Page number',
//     example: 1,
//   })
//   @ApiQuery({
//     name: 'limit',
//     required: false,
//     description: 'Items per page',
//     example: 10,
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'Driver offers retrieved successfully',
//   })
//   async getMyOffers(
//     @Req() req: any,
//     @Query('status') status?: string,
//     @Query('page') page: number = 1,
//     @Query('limit') limit: number = 10,
//   ): Promise<DriverOffersListDto> {
//     const driverId = req.user?.profileId;

//     if (!driverId) {
//       throw new BadRequestException('Driver profile not found');
//     }

//     try {
//       // Parse status string to RideOfferStatus array if provided
//       let statusFilter: RideOfferStatus[] | undefined;
//       if (status) {
//         const statusArray = status
//           .split(',')
//           .map((s) => s.trim()) as RideOfferStatus[];
//         statusFilter = statusArray;
//       }

//       const result = await this.rideOfferRepository.findDriverOffers(
//         driverId,
//         statusFilter,
//         page,
//         limit,
//       );

//       return {
//         success: true,
//         message: 'Driver offers retrieved successfully',
//         data: {
//           offers: result.offers.map((offer) => ({
//             id: offer.id,
//             rideId: offer.rideId,
//             batchNumber: offer.batchNumber,
//             score: parseFloat(offer.score?.toString() || '0'),
//             algorithm: offer.algorithm,
//             status: offer.status.toString(),
//             offeredAt: offer.offeredAt.toISOString(),
//             expiresAt: offer.expiresAt.toISOString(),
//             ...(offer.responseAt && {
//               responseAt: offer.responseAt.toISOString(),
//             }),
//             ...(offer.rejectionReason && {
//               rejectionReason: offer.rejectionReason,
//             }),
//             ...(offer.ride && {
//               ride: {
//                 pickupLocation: offer.ride.pickupLocation,
//                 destinationLocation: offer.ride.destinationLocation,
//                 riderId: offer.ride.riderId,
//               },
//             }),
//           })),
//           total: result.total,
//           page,
//           limit,
//         },
//         timestamp: Date.now(),
//       };
//     } catch (error) {
//       throw new BadRequestException('Failed to retrieve driver offers');
//     }
//   }

//   @Get(':offerId')
//   @ApiOperation({
//     summary: 'Get offer details',
//     description: 'Get detailed information about a specific offer',
//   })
//   @ApiParam({
//     name: 'offerId',
//     description: 'ID of the offer to retrieve',
//     example: 'offer-uuid-123',
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'Offer details retrieved successfully',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Offer not found',
//   })
//   async getOfferDetails(@Param('offerId') offerId: string, @Req() req: any) {
//     const driverId = req.user?.profileId;

//     if (!driverId) {
//       throw new BadRequestException('Driver profile not found');
//     }

//     try {
//       const offer = await this.rideOfferRepository.findByIdWithRide(offerId);

//       if (!offer) {
//         throw new NotFoundException(`Offer ${offerId} not found`);
//       }

//       // Ensure driver can only see their own offers
//       if (offer.driverId !== driverId) {
//         throw new BadRequestException('You can only view your own offers');
//       }

//       return {
//         success: true,
//         message: 'Offer details retrieved successfully',
//         data: {
//           id: offer.id,
//           rideId: offer.rideId,
//           driverId: offer.driverId,
//           batchNumber: offer.batchNumber,
//           score: parseFloat(offer.score?.toString() || '0'),
//           algorithm: offer.algorithm,
//           status: offer.status,
//           offeredAt: offer.offeredAt.toISOString(),
//           expiresAt: offer.expiresAt.toISOString(),
//           responseAt: offer.responseAt?.toISOString(),
//           rejectionReason: offer.rejectionReason || undefined,
//           metadata: offer.metadata,
//           ride: offer.ride
//             ? {
//               id: offer.ride.id,
//               riderId: offer.ride.riderId,
//               productId: offer.ride.productId,
//               status: offer.ride.status,
//               pickupLocation: offer.ride.pickupLocation,
//               destinationLocation: offer.ride.destinationLocation,
//               stops: offer.ride.stops,
//               createdAt: offer.ride.createdAt.toISOString(),
//             }
//             : undefined,
//         },
//         timestamp: Date.now(),
//       };
//     } catch (error) {
//       if (
//         error instanceof NotFoundException ||
//         error instanceof BadRequestException
//       ) {
//         throw error;
//       }
//       throw new BadRequestException('Failed to retrieve offer details');
//     }
//   }
// }

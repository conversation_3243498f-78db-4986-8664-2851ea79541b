'use client';

import { useState, useEffect } from 'react';
import { format, startOfToday } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';
import { toast } from '@/lib/toast';
import { downloadAggregatedCSV, downloadDailyCSV } from '../api/export';
import { useListCities } from '@/modules/city/api/queries';

interface DriverEarningsExportModalProps {
   isOpen: boolean;
   onClose: () => void;
   mode: 'aggregated' | 'daily';
   driverId?: string;
}

export function DriverEarningsExportModal({
   isOpen,
   onClose,
   mode,
   driverId,
}: DriverEarningsExportModalProps) {
   const [dateRange, setDateRange] = useState<DateRange | undefined>(() => ({
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date(),
   }));
   const [cityId, setCityId] = useState<string>('all');
   const [isExporting, setIsExporting] = useState(false);

   // Fetch cities for aggregated mode
   const citiesQuery = useListCities({
      page: 1,
      limit: 100,
      status: 'active',
   });

   // Reset form when modal opens
   useEffect(() => {
      if (isOpen) {
         setDateRange({
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date(),
         });
         setCityId('all');
      }
   }, [isOpen]);

   const handleDateRangeChange = (range: DateRange | undefined) => {
      setDateRange(range);
   };

   const handleExport = async () => {
      // Validate required fields
      if (!dateRange?.from || !dateRange?.to) {
         toast.error('Please select both start and end dates');
         return;
      }

      // Validate date range
      if (dateRange.to < dateRange.from) {
         toast.error('End date must be after start date');
         return;
      }

      // For daily mode, validate driverId
      if (mode === 'daily' && !driverId) {
         toast.error('Driver ID is required');
         return;
      }

      setIsExporting(true);

      try {
         const fromDate = format(dateRange.from, 'yyyy-MM-dd');
         const toDate = format(dateRange.to, 'yyyy-MM-dd');

         if (mode === 'aggregated') {
            await downloadAggregatedCSV({
               fromDate,
               toDate,
               cityId: cityId && cityId !== 'all' ? cityId : undefined,
            });
            toast.success('Aggregated earnings CSV downloaded successfully');
         } else {
            await downloadDailyCSV({
               driverId: driverId!,
               fromDate,
               toDate,
            });
            toast.success('Daily earnings CSV downloaded successfully');
         }

         onClose();
      } catch (error: any) {
         console.error('Export error:', error);
         toast.error(error?.message || 'Failed to download CSV');
      } finally {
         setIsExporting(false);
      }
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='max-w-md'>
            <DialogHeader>
               <DialogTitle>Export Earnings to CSV</DialogTitle>
               <DialogDescription>
                  {mode === 'aggregated'
                     ? 'Export aggregated driver earnings for the selected period'
                     : 'Export daily earnings for the selected driver and period'}
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-4 py-4'>
               {/* Date Range Picker */}
               <div className='flex flex-col gap-2'>
                  <Label>
                     Date Range <span className='text-red-500'>*</span>
                  </Label>
                  <div className='relative'>
                     <Popover>
                        <PopoverTrigger asChild>
                           <Button
                              variant='outline'
                              className={cn(
                                 'w-full justify-start text-left font-normal',
                                 !dateRange && 'text-muted-foreground'
                              )}
                           >
                              <CalendarIcon className='mr-2 h-4 w-4' />
                              {dateRange?.from ? (
                                 dateRange.to ? (
                                    <>
                                       {format(dateRange.from, 'MMM dd, yyyy')} -{' '}
                                       {format(dateRange.to, 'MMM dd, yyyy')}
                                    </>
                                 ) : (
                                    format(dateRange.from, 'MMM dd, yyyy')
                                 )
                              ) : (
                                 'Pick a date range'
                              )}
                           </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-2' align='start'>
                           <Calendar
                              mode='range'
                              selected={dateRange}
                              onSelect={handleDateRangeChange}
                              disabled={{ after: startOfToday() }}
                           />
                        </PopoverContent>
                     </Popover>
                  </div>
               </div>

               {/* City Selector - Only for Aggregated Mode */}
               {mode === 'aggregated' && (
                  <div className='flex flex-col gap-2'>
                     <Label>City (Optional)</Label>
                     <Select value={cityId} onValueChange={setCityId}>
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='All Cities' />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value='all'>All Cities</SelectItem>
                           {citiesQuery.data?.data?.map(city => (
                              <SelectItem key={city.id} value={city.id}>
                                 {city.name}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>
               )}
            </div>

            <DialogFooter className='!flex !gap-2'>
               <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={isExporting}
                  className=''
               >
                  Cancel
               </Button>

               <Button type='button' onClick={handleExport} disabled={isExporting} className=''>
                  {isExporting ? (
                     <>
                        Exporting...
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     'Export CSV'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

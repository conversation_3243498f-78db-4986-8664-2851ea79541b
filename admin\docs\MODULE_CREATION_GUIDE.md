# Complete Module Creation Guide

## Overview

This guide provides a comprehensive, step-by-step process for creating a new admin dashboard module from scratch. By following this guide and providing only the backend API controller, you can quickly scaffold a complete, production-ready module with CRUD operations, RBAC, and modern UI components.

---

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start Checklist](#quick-start-checklist)
3. [Module Architecture](#module-architecture)
4. [Step-by-Step Implementation](#step-by-step-implementation)
5. [File Structure Reference](#file-structure-reference)
6. [Code Templates](#code-templates)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

---

## Prerequisites

Before creating a new module, ensure you have:

1. ✅ **Backend API Controller** - The NestJS controller with all CRUD endpoints
2. ✅ **API Running Locally** - Backend accessible at `http://localhost:3000/api/v1`
3. ✅ **Swagger Docs** - Available at `http://localhost:3000/docs` for API reference
4. ✅ **Basic Understanding** of React, TypeScript, and Next.js

---

## Quick Start Checklist

Use this checklist when creating a new module:

### Phase 1: Setup & Configuration
- [ ] 1. Define RBAC permissions in `permissions.ts`
- [ ] 2. Add route URL constant to `route-urls.ts`
- [ ] 3. Add sidebar navigation entry to `sidebar-routes.ts`
- [ ] 4. Add page route permission to `page-permissions.ts`
- [ ] 5. Create module folder structure

### Phase 2: Type Definitions
- [ ] 6. Create TypeScript types from API controller DTOs
- [ ] 7. Define request/response interfaces

### Phase 3: API Integration
- [ ] 8. Create query hooks (list, get, getActive)
- [ ] 9. Create mutation hooks (create, update, delete)

### Phase 4: Components
- [ ] 10. Create main page component
- [ ] 11. Create table component with columns
- [ ] 12. Create modal form (create/edit)
- [ ] 13. Create filters component
- [ ] 14. Create loading/empty state components
- [ ] 15. Create delete confirmation modal (if needed)

### Phase 5: Routing
- [ ] 16. Create Next.js page route in `app/dashboard/`

### Phase 6: Testing & RBAC
- [ ] 17. Test all CRUD operations
- [ ] 18. Verify RBAC permissions work correctly
- [ ] 19. Test pagination and search
- [ ] 20. Verify error handling

---

## Module Architecture

### Folder Structure

Every module follows this standard structure:

```
admin/src/modules/[module-name]/
├── api/
│   ├── queries.ts         # React Query hooks for GET requests
│   └── mutations.ts       # React Query hooks for POST/PUT/DELETE
├── components/
│   ├── [module]-filters.tsx        # Search/filter component
│   ├── [module]-modal.tsx          # Create/Edit modal form
│   ├── [module]-table.tsx          # Data table with actions
│   ├── [module]-table-loading.tsx  # Loading skeleton
│   ├── [module]-table-empty.tsx    # Empty state
│   └── [module]-delete-modal.tsx   # Delete confirmation (optional)
├── pages/
│   └── [module]-page.tsx           # Main page component
└── types/
    └── [module].ts                 # TypeScript interfaces
```

### Naming Conventions

- **Files**: kebab-case (e.g., `tax-group-modal.tsx`)
- **Components**: PascalCase (e.g., `TaxGroupModal`)
- **Interfaces**: PascalCase (e.g., `TaxGroup`, `CreateTaxGroupRequest`)
- **Hooks**: camelCase with `use` prefix (e.g., `useListTaxGroup`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `RBAC_PERMISSIONS`)

---

## Step-by-Step Implementation

### Step 1: Define RBAC Permissions

**File:** `/src/role-based-access/permissions.ts`

Add your module permissions following the established pattern:

```typescript
export const RBAC_PERMISSIONS = {
  // ... existing permissions

  YOUR_MODULE: {
    CREATE: 'your_module:create',
    EDIT: 'your_module:edit',
    LIST: 'your_module:list',
    DELETE: 'your_module:delete',
    STATUS_UPDATE: 'your_module:status_update',  // Optional
    MANAGE: 'your_module:manage',                // Optional
  },
} as const;
```

**Key Points:**
- Use snake_case for permission names (e.g., `tax_group:create`)
- Permissions must match backend exactly
- Common permissions: `CREATE`, `EDIT`, `LIST`, `DELETE`
- Optional: `STATUS_UPDATE` (for activate/deactivate), `MANAGE` (for admin-level actions)

---

### Step 2: Add Route URL

**File:** `/src/data/route-urls.ts`

```typescript
export const ROUTE_URLS = {
  // ... existing routes

  DASHBOARD_YOUR_MODULE: '/dashboard/your-module',
} as const;
```

**Pattern:**
- Prefix with `DASHBOARD_`
- Use UPPER_SNAKE_CASE
- URL uses kebab-case

---

### Step 3: Add Sidebar Navigation

**File:** `/src/data/sidebar-routes.ts`

```typescript
import { YourIcon } from 'lucide-react'; // Choose appropriate icon

export const SIDEBAR_ROUTES = [
  // ... existing routes

  {
    title: 'Your Module',
    url: ROUTE_URLS.DASHBOARD_YOUR_MODULE,
    icon: YourIcon,
    isActive: false,
    pagePermission: RBAC_PERMISSIONS.YOUR_MODULE,
  },
];
```

**Icon Selection:**
- Use icons from `lucide-react` library
- Choose icons that represent your module's purpose
- Examples: `Receipt`, `Coins`, `BadgeDollarSign`, `Package`, `Target`

---

### Step 4: Add Page Route Permission

**File:** `/src/role-based-access/page-permissions.ts`

Add your module's route and permission mapping:

```typescript
export const PAGE_ROUTE_PERMISSIONS = [
  // ... existing routes

  {
    url: ROUTE_URLS.DASHBOARD_YOUR_MODULE,
    pagePermission: RBAC_PERMISSIONS.YOUR_MODULE,
  },
];
```

**Key Points:**
- This mapping is used by the RBAC system to protect routes
- The URL must match the one defined in `route-urls.ts`
- The permission must match the one defined in `permissions.ts`
- This ensures users without proper permissions cannot access the page

---

### Step 5: Create Module Folder Structure

```bash
mkdir -p admin/src/modules/your-module/{api,components,pages,types}
```

---

### Step 6: Create TypeScript Types

**File:** `/src/modules/your-module/types/your-module.ts`

**How to Create Types from API Controller:**

1. **Open Backend Controller** (e.g., `/backend/apps/api/src/v1/your-module/your-module.controller.ts`)
2. **Identify DTOs** from imports and decorators
3. **Map to TypeScript interfaces**

**Template Pattern:**

```typescript
// ============================================
// CORE ENTITY INTERFACE
// ============================================
export interface YourModule {
  id: string;
  name: string;
  description?: string | null;
  // Add fields from your entity
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================
export interface ListYourModuleResponse {
  success: boolean;
  message: string;
  data: YourModule[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  timestamp: number;
}

export interface YourModuleResponse {
  success: boolean;
  message: string;
  data: YourModule;
  timestamp: number;
}

// ============================================
// REQUEST PAYLOADS
// ============================================
export interface CreateYourModuleRequest {
  name: string;
  description?: string;
  // Add required fields from CreateDto
}

export interface UpdateYourModuleRequest {
  name?: string;
  description?: string;
  // All fields optional (Partial) from UpdateDto
}

// ============================================
// QUERY PARAMETERS
// ============================================
export interface ListYourModuleParams {
  page?: number;
  limit?: number;
  search?: string;
  // Add any additional filter params
}
```

**Type Mapping Guide:**

| Backend DTO | Frontend Type | Notes |
|-------------|---------------|-------|
| `string` | `string` | Direct mapping |
| `number` | `number` | Direct mapping |
| `boolean` | `boolean` | Direct mapping |
| `Date` | `string` | API sends ISO string |
| `Decimal` | `string` | Prisma Decimal → string |
| `enum` | `string \| type union` | Map to string or union |
| `relations` | Nested interface | Create sub-interfaces |
| `@IsOptional()` | `field?:` or `\| null` | Optional fields |

**Example from Tax Group:**

```typescript
// Backend has subcategories relation
export interface TaxSubcategoryResponse {
  id: string;
  taxGroupId: string;
  name: string;
  percentage: string;  // Prisma Decimal → string
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TaxGroup {
  id: string;
  name: string;
  description?: string | null;
  totalPercentage: string;  // Decimal → string
  subcategories: TaxSubcategoryResponse[];  // Relation
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}
```

---

### Step 7: Create API Query Hooks

**File:** `/src/modules/your-module/api/queries.ts`

```typescript
import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
  YourModuleResponse,
  ListYourModuleParams,
  ListYourModuleResponse,
} from '../types/your-module';

/**
 * Hook for listing items with pagination and filters
 */
export const useListYourModule = ({
  page,
  limit,
  search,
}: ListYourModuleParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST),
    queryKey: ['your-modules', page, limit, search],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListYourModuleResponse> => {
      return apiClient.get('/your-modules/paginated', {
        params: { page, limit, search },
      });
    },
  });
};

/**
 * Hook for getting a single item by ID (for edit mode)
 */
export const useGetYourModule = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['your-module', id],
    queryFn: (): Promise<YourModuleResponse> => {
      return apiClient.get(`/your-modules/${id || ''}`);
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT),
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting all active items (for dropdowns/selects)
 */
export const useGetActiveYourModules = () => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    enabled: hasPermission(RBAC_PERMISSIONS.YOUR_MODULE.LIST),
    queryKey: ['your-modules-active'],
    queryFn: (): Promise<{ success: boolean; message: string; data: any[]; timestamp: number }> => {
      return apiClient.get('/your-modules/active');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - active items don't change often
  });
};
```

**Key Points:**
- Always add RBAC permission check in `enabled` prop
- Use `keepPreviousData` for paginated queries to prevent loading flickers
- `refetchOnWindowFocus: false` to reduce unnecessary API calls
- Include all query params in `queryKey` for proper caching

---

### Step 8: Create API Mutation Hooks

**File:** `/src/modules/your-module/api/mutations.ts`

```typescript
import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  CreateYourModuleRequest,
  YourModuleResponse,
  UpdateYourModuleRequest,
} from '../types/your-module';

/**
 * Hook for creating a new item
 */
export const useCreateYourModule = () => {
  return useMutation({
    mutationFn: async (data: CreateYourModuleRequest): Promise<YourModuleResponse> => {
      return apiClient.post('/your-modules', data);
    },
  });
};

/**
 * Hook for updating an item
 */
export const useUpdateYourModule = () => {
  return useMutation({
    mutationFn: async (
      data: { id: string } & UpdateYourModuleRequest
    ): Promise<YourModuleResponse> => {
      const { id, ...payload } = data;
      return apiClient.put(`/your-modules/${id}`, payload);
    },
  });
};

/**
 * Hook for deleting an item (soft delete)
 */
export const useDeleteYourModule = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ success: boolean; message: string; timestamp: number }> => {
      return apiClient.delete(`/your-modules/${id}`);
    },
  });
};

/**
 * Hook for toggling active status (if applicable)
 */
export const useToggleYourModuleStatus = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ success: boolean; message: string; data: any; timestamp: number }> => {
      return apiClient.patch(`/your-modules/${id}/toggle-status`);
    },
  });
};
```

**Pattern Notes:**
- Mutations don't need permission checks (handled at button/action level)
- Extract `id` from payload for update/delete operations
- Match HTTP methods to backend: POST (create), PUT (update), DELETE (delete), PATCH (partial update)

---

### Step 9: Create Main Page Component

**File:** `/src/modules/your-module/pages/your-module-page.tsx`

```typescript
'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListYourModule } from '../api/queries';
import { YourModuleFilters } from '../components/your-module-filters';
import { YourModuleModal } from '../components/your-module-modal';
import { YourModuleTable } from '../components/your-module-table';

export function YourModulePage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState('');

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setSearch('');
    setPage(1);
  };

  const listQuery = useListYourModule({
    page,
    limit,
    search: search || undefined,
  });

  return (
    <div className='flex flex-1 flex-col gap-4 p-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-2xl font-semibold text-gray-900'>Your Module Title</h2>
        <div className='flex items-center gap-4'>
          <YourModuleModal mode='create' />
        </div>
      </div>

      <Card className='overflow-hidden py-4 px-4 rounded-sm'>
        <YourModuleFilters
          search={search}
          onSearchChange={handleSearchChange}
          isLoading={listQuery.isFetching && !listQuery.isLoading}
        />

        <YourModuleTable
          data={listQuery.data}
          isLoading={listQuery.isLoading}
          currentPage={page}
          onPageChange={(newPage: number) => setPage(newPage)}
          hasFilters={!!search}
          hasSearch={!!search}
          onClearFilters={handleClearFilters}
        />
      </Card>
    </div>
  );
}
```

**Pattern:**
- Always use `'use client'` directive (Next.js 15 App Router)
- Manage pagination and filter state at page level
- Pass state down to table and filters
- Reset page to 1 when filters change

---

### Step 10: Create Table Component

**File:** `/src/modules/your-module/components/your-module-table.tsx`

```typescript
'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteYourModule } from '../api/mutations';
import { ListYourModuleResponse, YourModule } from '../types/your-module';
import { YourModuleModal } from './your-module-modal';
import { YourModuleTableEmpty } from './your-module-table-empty';
import { YourModuleTableLoading } from './your-module-table-loading';
import { YourModuleDeleteModal } from './your-module-delete-modal';
import {
  USE_ROLE_BASED_ACCESS,
  useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

// ============================================
// COLUMN DEFINITIONS
// ============================================
const getColumns = ({
  deleteYourModuleMutation,
  handleEditClick,
  handleDeleteClick,
  itemToDelete,
  withPermission,
}: {
  handleEditClick: (id: string) => void;
  handleDeleteClick: (item: YourModule) => void;
  deleteYourModuleMutation: any;
  itemToDelete: YourModule | null;
  withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<YourModule>[] => [
  {
    accessorKey: 'name',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
    cell: ({ row }) => {
      const item = row.original as YourModule;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm font-medium break-words'>{item.name}</div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'description',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>,
    cell: ({ row }) => {
      const item = row.original as YourModule;
      return (
        <div className='text-left max-w-[250px]'>
          <div className='text-sm text-gray-600 break-words'>
            {item.description || 'No description'}
          </div>
        </div>
      );
    },
    size: 250,
  },
  {
    accessorKey: 'isActive',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
    cell: ({ row }) => {
      const item = row.original as YourModule;
      return (
        <div className='flex justify-center'>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
          >
            {item.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'createdAt',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Created Date</div>,
    cell: ({ row }) => {
      const item = row.original as YourModule;
      const date = new Date(item.createdAt);
      return (
        <div className='text-left'>
          <div className='text-sm text-gray-600'>
            {date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
          </div>
        </div>
      );
    },
    size: 120,
  },
  {
    id: 'actions',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
    cell: ({ row }) => {
      const item = row.original as YourModule;
      const isDeleting = itemToDelete?.id === item.id && deleteYourModuleMutation.isPending;

      return (
        <div className='flex justify-center gap-1'>
          <button
            className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.YOUR_MODULE.EDIT, () => handleEditClick(item.id));
            }}
            disabled={isDeleting}
          >
            Edit
          </button>
          <button
            className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.YOUR_MODULE.DELETE, () => handleDeleteClick(item));
            }}
            disabled={isDeleting}
          >
            {isDeleting ? '...' : 'Delete'}
          </button>
        </div>
      );
    },
    size: 150,
  },
];

// ============================================
// TABLE COMPONENT
// ============================================
interface YourModuleTableProps {
  data: ListYourModuleResponse | undefined;
  isLoading: boolean;
  currentPage: number;
  onPageChange: (page: number) => void;
  hasFilters?: boolean;
  hasSearch?: boolean;
  onClearFilters?: () => void;
}

export function YourModuleTable({
  data,
  isLoading,
  currentPage,
  onPageChange,
  hasFilters: _hasFilters,
  hasSearch: _hasSearch,
  onClearFilters: _onClearFilters,
}: YourModuleTableProps) {
  const [itemToEdit, setItemToEdit] = useState<string | null>(null);
  const [itemToDelete, setItemToDelete] = useState<YourModule | null>(null);
  const deleteMutation = useDeleteYourModule();
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  const handleEditClick = (id: string) => {
    setItemToEdit(id);
  };

  const handleDeleteClick = (item: YourModule) => {
    setItemToDelete(item);
  };

  const handleDeleteConfirm = () => {
    if (!itemToDelete) return;

    deleteMutation.mutate(itemToDelete.id, {
      onSuccess: () => {
        toast.success('Item deleted successfully');
        queryClient.invalidateQueries({ queryKey: ['your-modules'] });
      },
      onSettled: () => {
        setItemToDelete(null);
      },
    });
  };

  const columns = getColumns({
    deleteYourModuleMutation: deleteMutation,
    handleEditClick,
    handleDeleteClick,
    itemToDelete,
    withPermission,
  });

  const table = useReactTable({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <YourModuleTableLoading />;
  }

  if (!data?.data?.length) {
    return <YourModuleTableEmpty />;
  }

  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full table-fixed'>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id} className='border-b bg-gray-50'>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className='h-11 px-4 text-left align-middle'
                      style={{ width: header.getSize(), maxWidth: header.getSize() }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map(row => (
                <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                  {row.getVisibleCells().map(cell => (
                    <td
                      key={cell.id}
                      className='px-4 py-3 align-middle'
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {data && data.meta && data.meta.totalPages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.meta.totalPages}
          onPageChange={onPageChange}
          hasNext={data.meta.hasNextPage}
          hasPrev={data.meta.hasPrevPage}
        />
      )}

      {/* Delete Confirmation Modal */}
      <YourModuleDeleteModal
        isOpen={!!itemToDelete}
        onClose={() => setItemToDelete(null)}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteMutation.isPending}
        itemName={itemToDelete?.name || ''}
      />

      {/* Edit Modal */}
      <YourModuleModal
        mode='edit'
        itemId={itemToEdit}
        isOpen={!!itemToEdit}
        onClose={() => setItemToEdit(null)}
      />
    </div>
  );
}
```

**Column Definition Tips:**
- Define columns in a separate function outside component
- Use `size` property for column widths (helps with responsive tables)
- Use `max-w-[Npx]` and `break-words` for long text
- Wrap action buttons with `withPermission()` for RBAC
- Disable buttons during delete operations
- Use semantic class names for styling

---

### Step 11: Create Modal Form Component

**File:** `/src/modules/your-module/components/your-module-modal.tsx`

```typescript
'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateYourModule, useUpdateYourModule } from '../api/mutations';
import { useGetYourModule } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

// ============================================
// ZOD VALIDATION SCHEMA
// ============================================
const yourModuleSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must not exceed 100 characters'),
  description: z
    .string()
    .max(500, 'Description must not exceed 500 characters')
    .optional(),
  // Add your fields with validation
});

type YourModuleFormValues = z.infer<typeof yourModuleSchema>;

// ============================================
// COMPONENT PROPS
// ============================================
interface YourModuleModalProps {
  itemId?: string | null;
  isOpen?: boolean;
  onClose?: () => void;
  mode?: 'create' | 'edit';
}

// ============================================
// MODAL COMPONENT
// ============================================
export const YourModuleModal = ({
  itemId,
  isOpen,
  onClose,
  mode = 'create',
}: YourModuleModalProps) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const createMutation = useCreateYourModule();
  const updateMutation = useUpdateYourModule();
  const itemQuery = useGetYourModule(itemId || null);
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  // Use external open state if provided, otherwise use internal state
  const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
  const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

  const form = useForm<YourModuleFormValues>({
    resolver: zodResolver(yourModuleSchema),
    defaultValues: {
      name: '',
      description: '',
      // Add default values for your fields
    },
  });

  const {
    formState: { errors },
    reset,
    control,
    handleSubmit,
  } = form;

  const isDataLoading = itemQuery.isLoading;

  // Reset form when itemId changes or modal opens
  useEffect(() => {
    if (mode === 'edit' && itemQuery.data?.data && modalOpen && !isDataLoading) {
      const item = itemQuery.data.data;
      reset({
        name: item.name,
        description: item.description || '',
        // Map your fields
      });
    } else if (mode === 'create') {
      reset({
        name: '',
        description: '',
        // Reset to default values
      });
    }
  }, [itemQuery.data, reset, mode, modalOpen, isDataLoading]);

  const onSubmit = async (data: YourModuleFormValues) => {
    try {
      const payload = {
        name: data.name,
        description: data.description || undefined,
        // Map your fields
      };

      if (mode === 'create') {
        createMutation.mutate(payload, {
          onSuccess: () => {
            toast.success('Item created successfully');
            handleClose();
            queryClient.invalidateQueries({ queryKey: ['your-modules'] });
          },
        });
      } else if (mode === 'edit' && itemId) {
        updateMutation.mutate(
          { id: itemId, ...payload },
          {
            onSuccess: () => {
              toast.success('Item updated successfully');
              handleClose();
              queryClient.invalidateQueries({ queryKey: ['your-modules'] });
              queryClient.invalidateQueries({ queryKey: ['your-module', itemId] });
            },
          }
        );
      }
    } catch (error: any) {
      console.error('Submit error:', error);
    }
  };

  const handleClose = () => {
    setModalOpen(false);
    reset();
  };

  const isLoading = mode === 'create' ? createMutation.isPending : updateMutation.isPending;

  // Show loading state for edit mode
  if (mode === 'edit' && itemQuery.isLoading) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
            <DialogDescription>Please wait while we load the data.</DialogDescription>
          </DialogHeader>
          <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state for edit mode
  if (mode === 'edit' && itemQuery.error) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>Failed to load data.</DialogDescription>
          </DialogHeader>
          <div className='text-center py-8'>
            <p className='text-red-600'>Failed to load data</p>
            <Button onClick={handleClose} className='mt-4'>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const content = (
    <DialogContent
      onInteractOutside={e => {
        e.preventDefault();
      }}
      className='max-w-2xl max-h-[90vh] overflow-y-auto'
    >
      <DialogHeader>
        <DialogTitle>{mode === 'create' ? 'Create New Item' : 'Edit Item'}</DialogTitle>
        <DialogDescription>
          {mode === 'create'
            ? 'Add a new item to the system'
            : 'Update the item information'}
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit(onSubmit)} className='space-y-4 py-4'>
        {/* Name Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='name'>Name *</Label>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <Input
                id='name'
                placeholder='Enter name'
                {...field}
                className='w-full'
              />
            )}
          />
          {errors.name && <ErrorMessage error={errors.name} />}
        </div>

        {/* Description Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='description'>Description</Label>
          <Controller
            control={control}
            name='description'
            render={({ field }) => (
              <Textarea
                id='description'
                placeholder='Enter description'
                {...field}
                className='w-full min-h-[80px] resize-none'
              />
            )}
          />
          {errors.description && <ErrorMessage error={errors.description} />}
        </div>

        {/* Add your custom fields here */}

        {/* Action Buttons */}
        <div className='flex gap-3 pt-4'>
          <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
            Cancel
          </Button>
          <Button type='submit' disabled={isLoading} className='flex-1'>
            {isLoading ? (
              <>
                {mode === 'create' ? 'Creating...' : 'Updating...'}
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : mode === 'create' ? (
              'Create Item'
            ) : (
              'Update Item'
            )}
          </Button>
        </div>
      </form>
    </DialogContent>
  );

  // For create mode, return button and dialog separately
  if (mode === 'create' && isOpen === undefined) {
    return (
      <>
        <Button
          className='cursor-pointer'
          variant='outline'
          onClick={() =>
            withPermission(RBAC_PERMISSIONS.YOUR_MODULE.CREATE, () => setModalOpen(true))
          }
        >
          <Plus />
          Add Item
        </Button>
        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
          {content}
        </Dialog>
      </>
    );
  }

  // For edit mode or controlled create mode
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      {content}
    </Dialog>
  );
};
```

**Form Field Patterns:**

**Text Input:**
```typescript
<div className='flex flex-col gap-2'>
  <Label htmlFor='fieldName'>Field Label *</Label>
  <Controller
    control={control}
    name='fieldName'
    render={({ field }) => (
      <Input
        id='fieldName'
        placeholder='Placeholder text'
        {...field}
        className='w-full'
      />
    )}
  />
  {errors.fieldName && <ErrorMessage error={errors.fieldName} />}
</div>
```

**Number Input:**
```typescript
<Controller
  control={control}
  name='percentage'
  render={({ field }) => (
    <Input
      {...field}
      type='number'
      step='0.01'
      min='0'
      max='100'
      onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
    />
  )}
/>
```

**Select/Dropdown:**
```typescript
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

<Controller
  control={control}
  name='status'
  render={({ field }) => (
    <Select onValueChange={field.onChange} value={field.value}>
      <SelectTrigger>
        <SelectValue placeholder='Select status' />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value='active'>Active</SelectItem>
        <SelectItem value='inactive'>Inactive</SelectItem>
      </SelectContent>
    </Select>
  )}
/>
```

---

### Step 12: Create Filters Component

**File:** `/src/modules/your-module/components/your-module-filters.tsx`

```typescript
'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { Search, X } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';

export interface YourModuleFiltersProps {
  onSearchChange: (search: string) => void;
  search: string;
}

export function YourModuleFilters({
  onSearchChange,
  search,
  isLoading,
}: YourModuleFiltersProps & { isLoading?: boolean }) {
  const [searchValue, setSearchValue] = useState(search || '');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local search state when props change
  useEffect(() => {
    setSearchValue(search || '');
  }, [search]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Show searching indicator
    setIsSearching(true);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout
    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
      searchTimeoutRef.current = null;
      setIsSearching(false);
    }, 500); // 500ms debounce time
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Clear any pending timeouts
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setIsSearching(false);
    setSearchValue('');
    onSearchChange('');
  };

  // Check if any filters are active
  const hasActiveFilters = !!search;

  return (
    <div className='flex flex-col space-y-4 mb-4'>
      <div className='flex justify-between items-center'>
        {/* Search field */}
        <div className='flex gap-2 items-center'>
          {/* Search Input */}
          <div className='relative w-[200px]'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
            <Input
              placeholder='Search items...'
              value={searchValue}
              onChange={handleSearchChange}
              className='pl-8'
            />
            {(isSearching || (isLoading && searchValue)) && (
              <div className='absolute right-2.5 top-2.5 text-gray-500'>
                <Spinner className='h-4 w-4 text-primary' />
              </div>
            )}
            {searchValue && !isSearching && !isLoading && (
              <button
                onClick={() => {
                  if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                    searchTimeoutRef.current = null;
                  }
                  setIsSearching(false);
                  setSearchValue('');
                  onSearchChange('');
                }}
                className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
              >
                <X className='h-4 w-4' />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Clear Filters Button - Below the filters */}
      {hasActiveFilters && (
        <div className='flex justify-end'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleClearFilters}
            className='text-gray-700 border-gray-300'
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
}
```

**Features:**
- Debounced search (500ms delay)
- Loading indicators
- Clear button (X icon)
- Clear all filters button

---

### Step 13: Create Loading State Component

**File:** `/src/modules/your-module/components/your-module-table-loading.tsx`

```typescript
import { Skeleton } from '@/components/ui/skeleton';

export function YourModuleTableLoading() {
  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-24' />
                </th>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-32' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-16 mx-auto' />
                </th>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-28' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-20 mx-auto' />
                </th>
              </tr>
            </thead>
            <tbody>
              {[...Array(5)].map((_, index) => (
                <tr key={index} className='border-b'>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-32' />
                  </td>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-48' />
                  </td>
                  <td className='px-4 py-3 text-center'>
                    <Skeleton className='h-6 w-16 mx-auto rounded-full' />
                  </td>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-24' />
                  </td>
                  <td className='px-4 py-3'>
                    <div className='flex justify-center gap-1'>
                      <Skeleton className='h-8 w-12' />
                      <Skeleton className='h-8 w-12' />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
```

---

### Step 14: Create Empty State Component

**File:** `/src/modules/your-module/components/your-module-table-empty.tsx`

```typescript
import { Package } from 'lucide-react';

export function YourModuleTableEmpty() {
  return (
    <div className='flex flex-col items-center justify-center py-12 px-4'>
      <div className='rounded-full bg-gray-100 p-4 mb-4'>
        <Package className='h-8 w-8 text-gray-400' />
      </div>
      <h3 className='text-lg font-semibold text-gray-900 mb-1'>No items found</h3>
      <p className='text-sm text-gray-500 text-center max-w-sm'>
        Get started by creating your first item using the button above.
      </p>
    </div>
  );
}
```

---

### Step 15: Create Delete Confirmation Modal

**File:** `/src/modules/your-module/components/your-module-delete-modal.tsx`

```typescript
'use client';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { AlertTriangle } from 'lucide-react';

interface YourModuleDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  itemName: string;
}

export function YourModuleDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  itemName,
}: YourModuleDeleteModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-2'>
            <div className='rounded-full bg-red-100 p-2'>
              <AlertTriangle className='h-5 w-5 text-red-600' />
            </div>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </div>
          <DialogDescription className='pt-3'>
            Are you sure you want to delete <strong>"{itemName}"</strong>?
            <br />
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='gap-2 sm:gap-0'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={isLoading}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant='destructive'
            onClick={onConfirm}
            disabled={isLoading}
            className='flex-1'
          >
            {isLoading ? (
              <>
                Deleting...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

---

### Step 16: Create Next.js Route

**File:** `/src/app/dashboard/your-module/page.tsx`

```typescript
import { YourModulePage } from '@/modules/your-module/pages/your-module-page';

export default function DashboardYourModulePage() {
  return <YourModulePage />;
}
```

**Pattern:**
- Route file location: `/app/dashboard/[module-name]/page.tsx`
- Simply imports and renders the page component from modules
- Follows Next.js 15 App Router conventions

---

## File Structure Reference

After completing all steps, your module structure should look like this:

```
admin/
├── src/
│   ├── app/
│   │   └── dashboard/
│   │       └── your-module/
│   │           └── page.tsx                           # [Step 16]
│   ├── modules/
│   │   └── your-module/
│   │       ├── api/
│   │       │   ├── queries.ts                         # [Step 7]
│   │       │   └── mutations.ts                       # [Step 8]
│   │       ├── components/
│   │       │   ├── your-module-filters.tsx            # [Step 12]
│   │       │   ├── your-module-modal.tsx              # [Step 11]
│   │       │   ├── your-module-table.tsx              # [Step 10]
│   │       │   ├── your-module-table-loading.tsx      # [Step 13]
│   │       │   ├── your-module-table-empty.tsx        # [Step 14]
│   │       │   └── your-module-delete-modal.tsx       # [Step 15]
│   │       ├── pages/
│   │       │   └── your-module-page.tsx               # [Step 9]
│   │       └── types/
│   │           └── your-module.ts                     # [Step 6]
│   ├── data/
│   │   ├── route-urls.ts                              # [Step 2]
│   │   └── sidebar-routes.ts                          # [Step 3]
│   └── role-based-access/
│       ├── permissions.ts                             # [Step 1]
│       └── page-permissions.ts                        # [Step 4]
```

---

## Best Practices

### 1. Naming Conventions

- **Files**: Always use kebab-case
  - ✅ `tax-group-modal.tsx`
  - ❌ `TaxGroupModal.tsx`
  - ❌ `taxGroupModal.tsx`

- **Components**: Always use PascalCase
  - ✅ `export function TaxGroupModal()`
  - ❌ `export function taxGroupModal()`

- **Types/Interfaces**: Use PascalCase
  - ✅ `interface TaxGroup {}`
  - ❌ `interface taxGroup {}`

- **Hooks**: Use camelCase with `use` prefix
  - ✅ `export const useListTaxGroup`
  - ❌ `export const UseListTaxGroup`

### 2. Type Safety

- Always define proper TypeScript types
- Never use `any` type unless absolutely necessary
- Use type inference where possible
- Define separate types for requests and responses

### 3. Error Handling

- Always show toast notifications for success/error
- Provide meaningful error messages
- Handle loading and error states in UI
- Use try-catch blocks in async operations

### 4. Performance Optimization

- Use `keepPreviousData` for paginated queries
- Set `refetchOnWindowFocus: false` to reduce API calls
- Use `staleTime` for rarely changing data
- Implement proper query key structure for caching

### 5. RBAC Implementation

- Always add permission checks to:
  - Query hooks (`enabled` prop)
  - Action buttons (`withPermission` wrapper)
  - Route access (sidebar visibility)
- Keep buttons visible, block actions with toast feedback
- Test with different permission levels

### 6. Form Validation

- Use Zod for schema validation
- Provide clear, user-friendly error messages
- Validate on both frontend and backend
- Show field-level errors immediately

### 7. UI/UX Best Practices

- Show loading states (skeletons, spinners)
- Provide empty states with helpful messages
- Use confirmation modals for destructive actions
- Implement debouncing for search inputs
- Show success/error toast notifications
- Disable buttons during async operations

### 8. Code Organization

- Keep components small and focused
- Separate business logic from UI
- Use composition over inheritance
- Follow the established module structure
- Keep related code together (colocation)

---

## Troubleshooting

### Common Issues

#### 1. TypeScript Errors

**Problem:** Type mismatch between frontend and backend

**Solution:**
- Check Swagger docs for actual API response structure
- Verify DTO field names match exactly
- Pay attention to string vs number types (e.g., Decimal → string)

#### 2. RBAC Not Working

**Problem:** Permission checks always fail

**Solution:**
- Verify permission names match between frontend and backend
- Check user role has the permission assigned in database
- Clear React Query cache and refresh
- Check browser console for permission fetch errors

#### 3. Form Not Submitting

**Problem:** Modal doesn't close or data doesn't save

**Solution:**
- Check mutation response in Network tab
- Verify payload structure matches backend DTO
- Check for Zod validation errors in console
- Ensure `onSuccess` callback invalidates queries

#### 4. Pagination Not Working

**Problem:** Page doesn't change or shows wrong data

**Solution:**
- Verify `page` is included in query key
- Check backend pagination response structure
- Ensure `onPageChange` prop is passed correctly
- Verify `meta` object structure matches expected format

#### 5. Search/Filter Not Working

**Problem:** Search doesn't filter results

**Solution:**
- Check debounce timeout is working
- Verify search param is passed to API
- Check backend search implementation
- Ensure query key includes search term

#### 6. Modal State Issues

**Problem:** Modal shows old data or doesn't reset

**Solution:**
- Check `useEffect` dependencies include `modalOpen`
- Verify `reset()` is called in `handleClose`
- Ensure edit query is enabled only when `id` exists
- Check `mode` prop is passed correctly

---

## Advanced Patterns

### 1. Status Toggle Action

If your module supports activate/deactivate:

```typescript
// In mutations.ts
export const useToggleYourModuleStatus = () => {
  return useMutation({
    mutationFn: async (id: string): Promise<YourModuleResponse> => {
      return apiClient.patch(`/your-modules/${id}/toggle-status`);
    },
  });
};

// In table column
<button
  onClick={() => {
    withPermission(RBAC_PERMISSIONS.YOUR_MODULE.STATUS_UPDATE, () =>
      handleToggleClick(item)
    );
  }}
>
  {item.isActive ? 'Deactivate' : 'Activate'}
</button>
```

### 2. Nested/Related Data

For entities with relations (e.g., subcategories):

```typescript
// In types
export interface TaxSubcategory {
  name: string;
  percentage: number;
}

export interface TaxGroup {
  // ... other fields
  subcategories: TaxSubcategoryResponse[];
}

// In form, use useFieldArray
import { useFieldArray } from 'react-hook-form';

const { fields, append, remove } = useFieldArray({
  control,
  name: 'subcategories',
});
```

### 3. Bulk Actions

For selecting and acting on multiple items:

```typescript
// Add checkbox column
{
  id: 'select',
  header: ({ table }) => (
    <Checkbox
      checked={table.getIsAllPageRowsSelected()}
      onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={(value) => row.toggleSelected(!!value)}
    />
  ),
}

// Add bulk action button
const selectedRows = table.getSelectedRowModel().rows;
if (selectedRows.length > 0) {
  return (
    <Button onClick={() => handleBulkDelete(selectedRows)}>
      Delete {selectedRows.length} items
    </Button>
  );
}
```

---

## Summary

By following this guide, you can create a complete, production-ready module in approximately 2-3 hours. The key is to:

1. **Start with the backend API controller** - understand the endpoints and DTOs
2. **Follow the 15-step checklist** - don't skip steps
3. **Use the provided templates** - copy and modify for your module
4. **Test thoroughly** - verify CRUD, RBAC, pagination, search
5. **Follow naming conventions** - maintain consistency

### Quick Reference Checklist

```
✅ Permissions defined in permissions.ts
✅ Route URL added to route-urls.ts
✅ Sidebar entry added to sidebar-routes.ts
✅ Page route permission added to page-permissions.ts
✅ Module folder structure created
✅ Types created from API DTOs
✅ Query hooks implemented
✅ Mutation hooks implemented
✅ Main page component created
✅ Table component with columns created
✅ Modal form created
✅ Filters component created
✅ Loading state component created
✅ Empty state component created
✅ Delete modal created (if needed)
✅ Next.js route created
✅ RBAC tested
✅ All CRUD operations tested
```

For questions or issues, refer to:
- [RBAC Implementation Guide](./RBAC_IMPLEMENTATION_GUIDE.md)
- [Main Architecture Guide](../CLAUDE.md)
- Backend Swagger docs at `http://localhost:3000/docs`

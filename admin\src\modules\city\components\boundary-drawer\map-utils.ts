import { LatLng } from '../../types/city';

export const calculatePolygonCenter = (polygon: LatLng[]): { lat: number; lng: number } | null => {
  if (polygon.length === 0) return null;
  
  let totalLat = 0;
  let totalLng = 0;
  const points = polygon.slice(0, -1); // Remove last duplicate point
  
  points.forEach(point => {
    totalLat += point.lat;
    totalLng += point.lng;
  });
  
  return {
    lat: totalLat / points.length,
    lng: totalLng / points.length
  };
};
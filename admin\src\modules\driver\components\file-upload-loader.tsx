'use client';

import { useState, useEffect } from 'react';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface ProgressLoaderProps {
  /**
   * Current progress value (0-100)
   */
  value?: number;
  /**
   * Auto increment progress for demo purposes
   */
  autoIncrement?: boolean;
  /**
   * Current status of the loader
   */
  status?: 'loading' | 'complete' | 'error';
  /**
   * Optional label to display
   */
  label?: string;
  /**
   * Show percentage text
   */
  showPercentage?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function ProgressLoader({
  value = 0,
  autoIncrement = false,
  status = 'loading',
  label,
  showPercentage = true,
  className,
}: ProgressLoaderProps) {
  const [progress, setProgress] = useState(value);

  // Handle auto increment for demo purposes
  useEffect(() => {
    if (!autoIncrement || status !== 'loading') return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        const next = prev + Math.random() * 5;
        return next >= 100 ? 100 : next;
      });
    }, 300);

    return () => clearInterval(interval);
  }, [autoIncrement, status]);

  // Update progress when value prop changes
  useEffect(() => {
    if (!autoIncrement) {
      setProgress(value);
    }
  }, [value, autoIncrement]);

  // Complete the progress when status changes to complete
  useEffect(() => {
    if (status === 'complete' && progress < 100) {
      setProgress(100);
    }
  }, [status, progress]);

  // Get appropriate label based on progress
  const getProgressLabel = () => {
    if (label) return label;

    if (progress < 90) {
      return `Uploading files... (${Math.round(progress)}%)`;
    } else if (progress < 100) {
      return 'Processing takeoff...';
    } else {
      return 'Upload complete!';
    }
  };

  return (
    <div className={cn('w-full space-y-2', className)}>
      <Progress
        value={progress}
        className={cn(
          'h-2 transition-all',
          status === 'complete' && 'bg-primary/20',
          status === 'error' && 'bg-destructive/20',
        )}
      />

      <div className="flex items-center justify-between text-sm">
        <span className="text-muted-foreground">{getProgressLabel()}</span>

        <div className="flex items-center gap-2 ml-auto">
          {showPercentage && (
            <span className="text-xs text-muted-foreground">
              {Math.round(progress)}%
            </span>
          )}

          {status === 'loading' && progress < 100 && (
            <Loader2 className="h-3.5 w-3.5 animate-spin text-primary" />
          )}

          {(status === 'loading' && progress >= 100) ||
          status === 'complete' ? (
            <Check className="h-3.5 w-3.5 text-primary" />
          ) : null}

          {status === 'error' && (
            <AlertCircle className="h-3.5 w-3.5 text-destructive" />
          )}
        </div>
      </div>
    </div>
  );
}

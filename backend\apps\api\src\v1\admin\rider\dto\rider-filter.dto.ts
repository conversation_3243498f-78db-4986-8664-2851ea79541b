import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID, IsEnum } from 'class-validator';
import { PaginationDto } from '../../../../common/dto/pagination.dto';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

export class RiderFilterDto extends PaginationDto {
  @ApiProperty({
    description: 'Search by rider name, email, or phone number',
    example: 'john doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  @ApiProperty({
    description: 'Filter by rider name',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Filter by email address',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: 'Filter by phone number',
    example: '+************',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Filter by city ID',
    example: 'city-uuid-123',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiProperty({
    description: 'Filter by rider status',
    enum: UserProfileStatus,
    example: UserProfileStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserProfileStatus)
  status?: UserProfileStatus;
}

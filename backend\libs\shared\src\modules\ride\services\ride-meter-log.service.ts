import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { RideMeterLogRepository } from '@shared/shared/repositories/ride-meter-log.repository';
import { RideMeterRepository } from '@shared/shared/repositories/ride-meter.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import {
  RideMeterLog,
  CreateRideMeterLogData,
} from '@shared/shared/repositories/models/rideMeterLog.model';
import {
  RideMeter,
  RideMeterType,
  RideMeterUnit,
} from '@shared/shared/repositories/models/rideMeter.model';

@Injectable()
export class RideMeterLogService {
  private readonly logger = new Logger(RideMeterLogService.name);

  constructor(
    private readonly rideMeterLogRepository: RideMeterLogRepository,
    private readonly rideMeterRepository: RideMeterRepository,
    private readonly rideRepository: RideRepository,
  ) {}

  /**
   * Start a waiting period for a ride
   */
  async startWaiting(
    rideId: string,
    meterType: RideMeterType.PICKUP_WAIT_TIME | RideMeterType.TRIP_WAIT_TIME,
  ): Promise<RideMeterLog> {
    this.logger.log(
      `Starting waiting period for ride ${rideId}, type: ${meterType}`,
    );

    // Validate inputs
    await this.validateRideExists(rideId);
    this.validateMeterType(meterType);

    // Check for existing active waiting period
    await this.validateNoActiveWaitingPeriod(rideId, meterType);

    // Get or create the corresponding RideMeter
    const rideMeter = await this.getOrCreateRideMeter(rideId, meterType);

    // Create the meter log entry
    const meterLogData: CreateRideMeterLogData = {
      rideId,
      rideMeterId: rideMeter.id,
      name: meterType,
      fromTime: new Date(),
    };

    const meterLog =
      await this.rideMeterLogRepository.createRideMeterLog(meterLogData);

    this.logger.log(
      `Started waiting period for ride ${rideId}, type: ${meterType}, log ID: ${meterLog.id}`,
    );

    return meterLog;
  }

  /**
   * End a waiting period and update the corresponding RideMeter
   */
  async endWaiting(
    meterLogId: string,
    rideId: string,
    meterType: RideMeterType.PICKUP_WAIT_TIME | RideMeterType.TRIP_WAIT_TIME,
  ): Promise<{ meterLog: RideMeterLog; rideMeter: RideMeter }> {
    this.logger.log(
      `Ending waiting period for ride ${rideId}, type: ${meterType}, log ID: ${meterLogId}`,
    );

    // Validate inputs
    await this.validateRideExists(rideId);
    this.validateMeterType(meterType);

    // Find and validate the meter log
    const meterLog = await this.validateAndGetMeterLog(
      meterLogId,
      rideId,
      meterType,
    );

    // End the waiting period
    const endTime = new Date();
    const updatedMeterLog = await this.rideMeterLogRepository.endMeterLog(
      meterLogId,
      endTime,
    );

    // Calculate duration and update the RideMeter
    const durationInSeconds = this.calculateDurationInSeconds(
      meterLog.fromTime,
      endTime,
    );
    const updatedRideMeter = await this.updateRideMeterWithDuration(
      meterLog.rideMeterId,
      durationInSeconds,
    );

    this.logger.log(
      `Ended waiting period for ride ${rideId}, duration: ${durationInSeconds}s, total: ${updatedRideMeter.value}s`,
    );

    return {
      meterLog: updatedMeterLog,
      rideMeter: updatedRideMeter,
    };
  }

  /**
   * Get all meter logs for a ride
   */
  async getRideMeterLogs(rideId: string): Promise<RideMeterLog[]> {
    await this.validateRideExists(rideId);
    return this.rideMeterLogRepository.findByRideId(rideId);
  }

  // Private helper methods

  /**
   * Validate that the ride exists
   */
  private async validateRideExists(rideId: string): Promise<void> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }
  }

  /**
   * Validate meter type is supported for waiting
   */
  private validateMeterType(
    meterType: RideMeterType.PICKUP_WAIT_TIME | RideMeterType.TRIP_WAIT_TIME,
  ): void {
    const validTypes = [
      RideMeterType.PICKUP_WAIT_TIME,
      RideMeterType.TRIP_WAIT_TIME,
    ];
    if (!validTypes.includes(meterType)) {
      throw new BadRequestException(
        `Invalid meter type: ${meterType}. Supported types: ${validTypes.join(', ')}`,
      );
    }
  }

  /**
   * Check that there's no active waiting period for this ride and meter type
   */
  private async validateNoActiveWaitingPeriod(
    rideId: string,
    meterType: string,
  ): Promise<void> {
    const activeLog =
      await this.rideMeterLogRepository.findActiveLogsByRideIdAndMeterType(
        rideId,
        meterType,
      );
    console.log(activeLog);
    if (activeLog) {
      throw new ConflictException(
        `Active waiting period already exists for this ride`,
      );
    }
  }

  /**
   * Get existing RideMeter or create a new one
   */
  private async getOrCreateRideMeter(
    rideId: string,
    meterType: string,
  ): Promise<RideMeter> {
    let rideMeter = await this.rideMeterRepository.findByRideIdAndName(
      rideId,
      meterType,
    );

    if (!rideMeter) {
      // Create new RideMeter with initial value of 0
      rideMeter = await this.rideMeterRepository.createRideMeter({
        rideId,
        name: meterType,
        value: 0,
        unit: RideMeterUnit.SECONDS,
      });

      this.logger.log(
        `Created new RideMeter for ride ${rideId}, type: ${meterType}`,
      );
    }

    return rideMeter;
  }

  /**
   * Validate and get meter log
   */
  private async validateAndGetMeterLog(
    meterLogId: string,
    rideId: string,
    meterType: string,
  ): Promise<RideMeterLog> {
    const meterLog =
      await this.rideMeterLogRepository.findMeterLogById(meterLogId);

    if (!meterLog) {
      throw new NotFoundException(`Meter log with ID ${meterLogId} not found`);
    }

    if (meterLog.rideId !== rideId) {
      throw new BadRequestException(
        `Meter log ${meterLogId} does not belong to ride ${rideId}`,
      );
    }

    if (meterLog.name !== meterType) {
      throw new BadRequestException(
        `Meter log ${meterLogId} is not of type ${meterType}, found: ${meterLog.name}`,
      );
    }

    if (meterLog.toTime !== null) {
      throw new BadRequestException(
        `Meter log ${meterLogId} has already been ended at ${meterLog.toTime}`,
      );
    }

    return meterLog;
  }

  /**
   * Calculate duration in seconds between two dates
   */
  private calculateDurationInSeconds(startTime: Date, endTime: Date): number {
    return Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
  }

  /**
   * Update RideMeter by adding the new duration to existing value
   */
  private async updateRideMeterWithDuration(
    rideMeterId: string,
    durationInSeconds: number,
  ): Promise<RideMeter> {
    const rideMeter = await this.rideMeterRepository.findById(rideMeterId);
    if (!rideMeter) {
      throw new NotFoundException(`RideMeter with ID ${rideMeterId} not found`);
    }

    const newValue = rideMeter.value + durationInSeconds;
    return this.rideMeterRepository.updateRideMeter(rideMeterId, {
      value: newValue,
    });
  }
}

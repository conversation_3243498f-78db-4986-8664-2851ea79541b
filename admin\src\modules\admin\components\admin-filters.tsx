'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, Settings, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';
import { InviteAdminSheet } from './invite-admin-sheet';

export interface AdminFiltersProps {
   onSearchChange: (search: string) => void;
   onStatusChange: (status: 'active' | 'inactive' | 'invited' | undefined) => void;
   search: string;
   status: 'active' | 'inactive' | 'invited' | undefined;
   activeTab?: string;
}

export function AdminFilters({
   onSearchChange,
   onStatusChange,
   search,
   status,
   isLoading,
   activeTab,
}: AdminFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [isSearching, setIsSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }

      setIsSearching(false);
      setSearchValue('');
      onSearchChange('');
      onStatusChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!search || !!status;

   return (
      <div className='p-4 bg-gray-50/50 border-b'>
         {/* Main filters grid */}
         <div className='grid grid-cols-1 sm:grid-cols-12 gap-4 items-center'>
            {/* Search Input */}
            <div className='sm:col-span-4 relative'>
               <div className='relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
                  <Input
                     placeholder='Search first name or last name or email'
                     value={searchValue}
                     onChange={handleSearchChange}
                     className='pl-10 pr-10'
                  />
                  {(isSearching || isLoading) && (
                     <div className='absolute right-8 top-1/2 transform -translate-y-1/2'>
                        <Spinner className='sm' />
                     </div>
                  )}
                  {searchValue && !isSearching && (
                     <button
                        onClick={() => {
                           setSearchValue('');
                           onSearchChange('');
                        }}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Status Filter */}
            <div className='sm:col-span-3'>
               <Select
                  value={status || 'all'}
                  onValueChange={value =>
                     onStatusChange(
                        value === 'all' ? undefined : (value as 'active' | 'inactive' | 'invited')
                     )
                  }
               >
                  <SelectTrigger className='w-full'>
                     <div className='flex items-center gap-2'>
                        <Settings className='h-4 w-4 text-gray-500 flex-shrink-0' />
                        <SelectValue placeholder='All Status' />
                     </div>
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='active'>Active</SelectItem>
                     <SelectItem value='inactive'>Inactive</SelectItem>
                     <SelectItem value='invited'>Invited</SelectItem>
                  </SelectContent>
               </Select>
            </div>

            {/* Clear Filters Button */}
            <div className='sm:col-span-3 flex justify-start'>
               {hasActiveFilters ? (
                  <Button
                     variant='outline'
                     size='sm'
                     onClick={handleClearFilters}
                     className='flex items-center gap-2 whitespace-nowrap'
                  >
                     <X className='h-4 w-4' />
                     Clear Filters
                  </Button>
               ) : (
                  <div className='h-9'></div>
               )}
            </div>

            {/* Tab-specific Invite Buttons */}
            <div className='sm:col-span-2 flex justify-end'>
               {activeTab === ROLE_IDENTIFIERS.SUB_ADMIN && (
                  <InviteAdminSheet
                     selectedAdminType={ROLE_IDENTIFIERS.SUB_ADMIN}
                     label='Invite Sub Admin'
                  />
               )}
               {activeTab === ROLE_IDENTIFIERS.CITY_ADMIN && (
                  <InviteAdminSheet
                     selectedAdminType={ROLE_IDENTIFIERS.CITY_ADMIN}
                     label='Invite City Admin'
                  />
               )}
            </div>
         </div>
      </div>
   );
}

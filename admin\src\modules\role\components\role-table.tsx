'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteRole } from '../api/mutations';
import { ListRoleResponse, Role } from '../types/role';
import { DeleteRoleDialog } from './delete-role-dialog';
import { RoleModal } from './role-modal';
import { RolePermissionSheet } from './role-permission-sheet';
import { RoleTableEmpty } from './role-table-empty';
import { RoleTableLoading } from './role-table-loading';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   // deleteRoleMutation,
   // handleDeleteClick,
   // handleEditClick,
   handleManagePermissionsClick,
   withPermission,
}: // roleToDelete,
{
   handleDeleteClick: (id: string) => void;
   handleEditClick: (id: string) => void;
   handleManagePermissionsClick: (id: string) => void;
   deleteRoleMutation: any;
   roleToDelete: string | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Role>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const role = row.original as Role;
         return (
            <div className='text-left'>
               <div className='text-sm font-semibold'>{role.name}</div>
               {/* {role.identifier && (
                  <div className='text-xs text-gray-500 font-mono'>{role.identifier}</div>
               )} */}
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'identifier',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>,
      cell: ({ row }) => {
         const role = row.original as Role;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{role.identifier}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const role = row.original as Role;
         return (
            <div className='text-left'>
               <div
                  className='text-sm text-gray-600 max-w-xs truncate'
                  title={role.description || 'No description'}
               >
                  {role.description || 'No description'}
               </div>
            </div>
         );
      },
      size: 300,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const role = row.original as Role;
         return (
            <div className='flex justify-center gap-1'>
               {/* <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.ROLES.EDIT, () =>
                        handleEditClick(role.id)
                     );
                  }}
               >
                  Edit
               </button> */}
               <button
                  className='text-sm font-medium text-gray-600 hover:text-green-600 border border-gray-300 hover:border-green-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.ROLES.MANAGE_PERMISSIONS, () =>
                        handleManagePermissionsClick(role.id)
                     );
                  }}
               >
                  Permissions
               </button>
               {/* <button
                  className='text-sm font-medium text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleDeleteClick(role.id)}
                  disabled={deleteRoleMutation.isPending && roleToDelete === role.id}
               >
                  Delete
               </button> */}
            </div>
         );
      },
      size: 250,
   },
];

interface RoleTableProps {
   data: ListRoleResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function RoleTable({ data, isLoading, currentPage, onPageChange }: RoleTableProps) {
   const [roleToDelete, setRoleToDelete] = useState<string | null>(null);
   const [roleToEdit, setRoleToEdit] = useState<string | null>(null);
   const [roleToManagePermissions, setRoleToManagePermissions] = useState<string | null>(null);
   const deleteRoleMutation = useDeleteRole();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleDeleteClick = (id: string) => {
      setRoleToDelete(id);
   };

   const handleEditClick = (id: string) => {
      setRoleToEdit(id);
   };

   const handleManagePermissionsClick = (id: string) => {
      setRoleToManagePermissions(id);
   };

   const handleDeleteConfirm = () => {
      if (!roleToDelete) return;

      deleteRoleMutation.mutate(roleToDelete, {
         onSuccess: () => {
            toast.success('Role deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['roles'] });
         },
         onSettled: () => {
            setRoleToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteRoleMutation,
      handleDeleteClick,
      handleEditClick,
      handleManagePermissionsClick,
      roleToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <RoleTableLoading />;
   }

   if (!data?.data?.length) {
      return <RoleTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         <DeleteRoleDialog
            isOpen={!!roleToDelete}
            onClose={() => setRoleToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteRoleMutation.isPending}
         />

         <RoleModal
            mode='edit'
            roleId={roleToEdit}
            isOpen={!!roleToEdit}
            onClose={() => setRoleToEdit(null)}
         />

         <RolePermissionSheet
            roleId={roleToManagePermissions}
            isOpen={!!roleToManagePermissions}
            onClose={() => setRoleToManagePermissions(null)}
         />
      </div>
   );
}

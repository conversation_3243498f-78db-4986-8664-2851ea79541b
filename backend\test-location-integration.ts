/**
 * Test script to verify the integration between LocationIngestorService and RealtimeGateway
 * This script demonstrates how the processDriverLocationUpdateDev method now calls
 * the handleLocationUpdate method in RealtimeGateway
 */

import { Test, TestingModule } from '@nestjs/testing';
import { LocationIngestorService } from './libs/shared/src/modules/location/location-ingestor.service';
import { RealtimeGateway } from './apps/api/src/v1/realtime/realtime.gateway';
import { DriverLocationReceivedDto } from './libs/shared/src/event-emitter/dto/driver-location-received.dto';

async function testLocationIntegration() {
  console.log('🧪 Testing Location Integration...');

  // Mock data for testing
  const testLocationData: DriverLocationReceivedDto = {
    driverId: 'test-driver-123',
    lat: 12.9716,
    lng: 77.5946,
    timestamp: new Date().toISOString(),
    city: 'bangalore',
    status: 'online' as any,
    rideId: 'test-ride-456',
    bearing: 45,
    speed: 25,
  };

  console.log('📍 Test Location Data:', testLocationData);

  // In a real application, this would be injected through NestJS DI
  // For testing purposes, we're just demonstrating the flow
  console.log('✅ Integration Setup Complete');
  console.log('');
  console.log('🔄 Flow:');
  console.log('1. WebSocket receives location_update message');
  console.log('2. RealtimeGateway.handleWebSocketLocationUpdate() is called');
  console.log('3. RealtimeService.processLocationUpdate() processes the data');
  console.log('4. LocationIngestorService.processDriverLocationUpdateDev() is called');
  console.log('5. LocationIngestorService calls back to RealtimeGateway.handleLocationUpdate()');
  console.log('6. RealtimeGateway broadcasts location to ride subscribers');
  console.log('');
  console.log('🎯 Key Changes Made:');
  console.log('- Added ILocationUpdateCallback interface to LocationIngestorService');
  console.log('- RealtimeGateway implements ILocationUpdateCallback');
  console.log('- LocationIngestorService.processDriverLocationUpdateDev() now calls the callback');
  console.log('- RealtimeModule provides RealtimeGateway as ILocationUpdateCallback');
  console.log('- Removed duplicate broadcast logic from WebSocket handler');
}

// Run the test
testLocationIntegration().catch(console.error);

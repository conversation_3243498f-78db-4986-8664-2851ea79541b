import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsObject,
  IsNumber,
  ValidateNested,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Type } from 'class-transformer';

export class LocationPointDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @IsNotEmpty()
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @IsNotEmpty()
  lng!: number;
}

export class FavoriteLocationMetaDto {
  @ApiProperty({
    description: 'Address of the location',
    example: '123 Main Street, Bangalore, Karnataka, India',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  // Allow additional properties
  [key: string]: any;
}

export class CreateFavoriteLocationDto {
  @ApiProperty({
    description: 'Name of the favorite location',
    example: 'Home',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  name!: string;

  @ApiProperty({
    description: 'Description of the favorite location',
    example: 'My home address in Bangalore',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: 'Location coordinates and address',
    type: LocationPointDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => LocationPointDto)
  location!: LocationPointDto;

  @ApiProperty({
    description: 'Additional metadata for the favorite location',
    type: FavoriteLocationMetaDto,
    required: false,
    example: {
      address: '123 Main Street, Bangalore, Karnataka, India',
    },
  })
  @IsOptional()
  @IsObject()
  meta?: any;
}

'use client';

import { CustomPagination } from '@/components/pagination';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import Link from 'next/link';
import {
   AggregatedEarningsResponse,
   AggregatedDriverEarning,
} from '../types/driver-earnings';
import { AggregatedEarningsTableLoading } from './aggregated-earnings-table-loading';
import { AggregatedEarningsTableEmpty } from './aggregated-earnings-table-empty';

// ============================================
// COLUMN DEFINITIONS
// ============================================
const getColumns = (): ColumnDef<AggregatedDriverEarning>[] => [
   {
      accessorKey: 'driverName',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Driver</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-left max-w-[180px]'>
               <div className='text-sm font-medium truncate'>
                  {earning.driverFirstName} {earning.driverLastName}
               </div>
               <div className='text-xs text-gray-500 mt-0.5 truncate'>ID: {earning.driverId}</div>
            </div>
         );
      },
      size: 180,
   },
   {
      accessorKey: 'city',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600 break-words'>{earning.city}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'totalFare',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Total Fare</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm font-medium'>₹{earning.totalFare.toFixed(2)}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'totalCommission',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Commission</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm text-gray-600'>₹{earning.totalCommission.toFixed(2)}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'totalTaxOnCommission',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Tax on Commission</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm text-gray-600'>₹{earning.totalTaxOnCommission.toFixed(2)}</div>
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'netDriverEarnings',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Net Earnings</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm font-semibold text-green-600'>
                  ₹{earning.netDriverEarnings.toFixed(2)}
               </div>
            </div>
         );
      },
      size: 130,
   },
   {
      accessorKey: 'completedRides',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Rides</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-center'>
               <div className='text-sm font-medium'>{earning.completedRides}</div>
            </div>
         );
      },
      size: 80,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='flex justify-center'>
               <Link
                  href={`/dashboard/driver-earnings?driverId=${earning.driverId}`}
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
               >
                  View Details
               </Link>
            </div>
         );
      },
      size: 140,
   },
];

// ============================================
// TABLE COMPONENT
// ============================================
interface AggregatedEarningsTableProps {
   data: AggregatedEarningsResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   onViewDetails: (driverId: string) => void;
}

export function AggregatedEarningsTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   onViewDetails: _onViewDetails,
}: AggregatedEarningsTableProps) {
   const columns = getColumns();

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <AggregatedEarningsTableLoading />;
   }

   if (!data?.data?.length) {
      return <AggregatedEarningsTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(header.column.columnDef.header, header.getContext())}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={currentPage < data.meta.totalPages}
               hasPrev={currentPage > 1}
            />
         )}
      </div>
   );
}

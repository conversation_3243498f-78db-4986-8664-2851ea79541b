-- CreateTable
CREATE TABLE "driver_earnings" (
    "id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "earnings_date" DATE NOT NULL,
    "total_fare_amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "completed_rides" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_earnings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_driver_earnings_driver_id" ON "driver_earnings"("driver_id");

-- CreateIndex
CREATE INDEX "idx_driver_earnings_date" ON "driver_earnings"("earnings_date");

-- CreateIndex
CREATE INDEX "idx_driver_earnings_driver_date" ON "driver_earnings"("driver_id", "earnings_date");

-- CreateIndex
CREATE UNIQUE INDEX "driver_earnings_driver_id_earnings_date_key" ON "driver_earnings"("driver_id", "earnings_date");

-- AddForeignKey
ALTER TABLE "driver_earnings" ADD CONSTRAINT "driver_earnings_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

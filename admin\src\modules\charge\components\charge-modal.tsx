'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { Plus, Trash2, CheckIcon, ChevronDownIcon } from 'lucide-react';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn, handleNumberInputChange, getNumberDisplayValue } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useCreateStandaloneCharge, useUpdateCharge } from '../api/charge-mutations';
import { useCreateCharge } from '@/modules/charge-group/api/charge-mutations';
import { useListAllCharges, useListCharges } from '../api/charge-queries';
import {
   Charge,
   CHARGE_METER_LABELS,
   CHARGE_TYPE_LABELS,
   ChargeMeter,
   ChargeType,
   ConditionBlock,
   CONDITION_TYPE_LABELS,
   ConditionType,
   PRICE_MODEL_LABELS,
   PriceModel,
   TierConfig,
} from '../types/charge';
import {
   generateJsonLogicFromBlocks,
   parseJsonLogicToBlocks,
   validateConditionBlock,
} from '../utils/condition-helpers';

// Validation schemas
const tierSchema = z.object({
   From: z.preprocess(
      val => (val === '' ? undefined : val),
      z.number().min(0, 'From must be 0 or greater')
   ),
   To: z.union([
      z
         .string()
         .refine(
            val => val === 'inf' || (!isNaN(Number(val)) && Number(val) >= 0),
            'Must be a number greater than or equal to 0, or "inf"'
         ),
      z.number().min(0, 'To must be 0 or greater'),
   ]),
   Flat_fee: z.preprocess(
      val => (val === '' ? undefined : val),
      z.number().min(0, 'Flat fee must be 0 or greater').optional()
   ),
   Rate: z.preprocess(
      val => (val === '' ? undefined : val),
      z.number().min(0, 'Rate must be 0 or greater').optional()
   ),
   currency: z.string().min(1, 'Currency is required'),
});

const timeRangeConfigSchema = z.object({
   startTime: z.string().min(1, 'Start time is required'),
   endTime: z.string().min(1, 'End time is required'),
   operator: z.enum(['or', 'and']),
});

const conditionBlockSchema = z.object({
   type: z.nativeEnum(ConditionType),
   config: z.array(timeRangeConfigSchema), // For BOOKING_TIME type
   operator: z.enum(['or', 'and']),
});

const chargeSchema = z
   .object({
      name: z
         .string()
         .min(1, 'Charge name is required')
         .max(35, 'Charge name must not exceed 35 characters')
         .regex(/^[A-Za-z\s]+$/, 'Charge name must contain only letters and spaces'),
      identifier: z.string().optional(),
      chargeType: z.nativeEnum(ChargeType),
      meter: z.nativeEnum(ChargeMeter).optional().nullable(),
      priceModel: z.nativeEnum(PriceModel),
      // Price config fields - preprocess empty strings to undefined
      amount: z.preprocess(val => (val === '' ? undefined : val), z.number().optional()),
      rate: z.preprocess(val => (val === '' ? undefined : val), z.number().optional()),
      tiers: z.array(tierSchema).optional(),
      // formula: z.string().optional(), // Commented out - not in use
      currency: z.string().optional(),
      // Percentage fields
      percentage: z.preprocess(val => (val === '' ? undefined : val), z.number().optional()),
      percentageOfChargeId: z.string().optional(),
      // Condition fields
      enableCondition: z.boolean().optional().default(false),
      conditionBlocks: z.array(conditionBlockSchema).optional(),
      condition: z.any().optional(),
   })
   .refine(
      data => {
         // If metered, meter is required
         if (data.chargeType === ChargeType.METERED && !data.meter) {
            return false;
         }
         return true;
      },
      { message: 'Meter is required for metered charges', path: ['meter'] }
   )
   .superRefine((data, ctx) => {
      // Validate price configs based on price model
      if (data.priceModel === PriceModel.FLAT_AMOUNT) {
         if (data.amount === undefined || data.amount === null) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Amount is required',
               path: ['amount'],
            });
         } else if (data.amount <= 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Amount must be greater than 0',
               path: ['amount'],
            });
         }
         if (!data.currency) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Currency is required',
               path: ['currency'],
            });
         }
      }
      if (data.priceModel === PriceModel.LINEAR_RATE) {
         if (data.rate === undefined || data.rate === null) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Rate is required',
               path: ['rate'],
            });
         } else if (data.rate <= 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Rate must be greater than 0',
               path: ['rate'],
            });
         }
         if (!data.currency) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Currency is required',
               path: ['currency'],
            });
         }
      }
      if (data.priceModel === PriceModel.TIERED) {
         if (!data.tiers || data.tiers.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one tier is required',
               path: ['tiers'],
            });
         } else {
            // Validate each tier has either Flat_fee or Rate (allow 0 values)
            data.tiers.forEach((tier, index) => {
               const hasFlatFee = tier.Flat_fee !== undefined && tier.Flat_fee !== null;
               const hasRate = tier.Rate !== undefined && tier.Rate !== null;
               if (!hasFlatFee && !hasRate) {
                  ctx.addIssue({
                     code: z.ZodIssueCode.custom,
                     message: 'Either Flat Fee or Rate must be provided',
                     path: ['tiers', index],
                  });
               }
            });

            // Validate tier ranges for continuity and no overlaps
            const sortedTiers = [...data.tiers].sort((a, b) => a.From - b.From);

            sortedTiers.forEach((tier, index) => {
               const toValue = tier.To === 'inf' ? Infinity : Number(tier.To);

               // Validate From < To
               if (tier.From >= toValue) {
                  ctx.addIssue({
                     code: z.ZodIssueCode.custom,
                     message: `Tier ${index + 1}: 'From' (${tier.From}) must be less than 'To' (${
                        tier.To
                     })`,
                     path: ['tiers', index, 'To'],
                  });
               }

               // Validate continuity with previous tier
               if (index > 0) {
                  const prevTier = sortedTiers[index - 1];
                  const prevToValue = prevTier.To === 'inf' ? Infinity : Number(prevTier.To);

                  if (tier.From !== prevToValue) {
                     ctx.addIssue({
                        code: z.ZodIssueCode.custom,
                        message: `Tier ${index + 1}: 'From' (${
                           tier.From
                        }) must equal previous tier's 'To' (${prevTier.To})`,
                        path: ['tiers', index, 'From'],
                     });
                  }
               }

               // Check for overlaps with any other tier
               sortedTiers.forEach((otherTier, otherIndex) => {
                  if (index !== otherIndex) {
                     const otherToValue = otherTier.To === 'inf' ? Infinity : Number(otherTier.To);

                     // Check if ranges overlap
                     const rangesOverlap =
                        tier.From < otherToValue &&
                        toValue > otherTier.From &&
                        !(tier.From === otherTier.From && toValue === otherToValue);

                     if (rangesOverlap && index < otherIndex) {
                        ctx.addIssue({
                           code: z.ZodIssueCode.custom,
                           message: `Tier ${index + 1} (${tier.From}-${
                              tier.To
                           }) overlaps with Tier ${otherIndex + 1} (${otherTier.From}-${
                              otherTier.To
                           })`,
                           path: ['tiers', index],
                        });
                     }
                  }
               });
            });
         }
      }
      if (data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
         if (data.percentage === undefined || data.percentage === null) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Percentage is required',
               path: ['percentage'],
            });
         } else if (data.percentage < 0.0001 || data.percentage > 100) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Percentage must be between 0.0001 and 100',
               path: ['percentage'],
            });
         }
         if (!data.percentageOfChargeId) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Charge reference is required',
               path: ['percentageOfChargeId'],
            });
         }
      }
      // Commented out - FORMULA price model not in use
      // if (data.priceModel === PriceModel.FORMULA) {
      //    if (!data.formula) {
      //       ctx.addIssue({
      //          code: z.ZodIssueCode.custom,
      //          message: 'Formula is required',
      //          path: ['formula'],
      //       });
      //    }
      // }
      // Validate conditions if enabled
      if (data.enableCondition) {
         if (!data.conditionBlocks || data.conditionBlocks.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one condition is required when conditions are enabled',
               path: ['conditionBlocks'],
            });
         } else {
            // Validate each condition block
            data.conditionBlocks.forEach((block, blockIndex) => {
               const error = validateConditionBlock(block);
               if (error) {
                  ctx.addIssue({
                     code: z.ZodIssueCode.custom,
                     message: error,
                     path: ['conditionBlocks', blockIndex],
                  });
               }
            });
         }
      }
   });

type ChargeFormValues = z.infer<typeof chargeSchema>;

interface ChargeModalProps {
   chargeGroupId?: string; // Optional for standalone charges
   chargeId?: string | null;
   chargeData?: Charge; // The charge data from the table
   isOpen: boolean;
   onClose: () => void;
   mode?: 'create' | 'edit';
}

// Component for rendering Booking Time condition configuration
function BookingTimeConfig({ control, blockIndex, errors }: any) {
   return (
      <div className='flex flex-col gap-2'>
         <Label>Time Range *</Label>
         <p className='text-xs text-muted-foreground mb-2'>
            Define when this charge applies. For overnight hours (e.g., 8PM to 6AM).
         </p>
         <div className='border rounded-md p-3 space-y-3 bg-background'>
            <div className='grid grid-cols-2 gap-3'>
               <div className='flex flex-col gap-2'>
                  <Label className='text-xs'>Start Time *</Label>
                  <Controller
                     control={control}
                     name={`conditionBlocks.${blockIndex}.config.0.startTime`}
                     render={({ field }) => (
                        <Input type='time' {...field} className='bg-background' />
                     )}
                  />
               </div>
               <div className='flex flex-col gap-2'>
                  <Label className='text-xs'>End Time *</Label>
                  <Controller
                     control={control}
                     name={`conditionBlocks.${blockIndex}.config.0.endTime`}
                     render={({ field }) => (
                        <Input type='time' {...field} className='bg-background' />
                     )}
                  />
               </div>
            </div>
            {errors.conditionBlocks?.[blockIndex]?.config?.[0] && (
               <ErrorMessage error={errors.conditionBlocks[blockIndex].config[0] as any} />
            )}
         </div>
      </div>
   );
}

// Helper function to get meter unit
const getMeterUnit = (meter: ChargeMeter | null): string => {
   if (!meter) return '';

   const distanceMeters = [ChargeMeter.PICKUP_DISTANCE, ChargeMeter.TRIP_DISTANCE];
   const timeMeters = [
      ChargeMeter.PICKUP_DURATION,
      ChargeMeter.PICKUP_WAIT_DURATION,
      ChargeMeter.TRIP_DURATION,
      ChargeMeter.TRIP_WAIT_DURATION,
   ];

   if (distanceMeters.includes(meter)) return 'km';
   if (timeMeters.includes(meter)) return 'minute';

   return '';
};

export const ChargeModal = ({
   chargeGroupId,
   chargeId,
   chargeData,
   isOpen,
   onClose,
   mode = 'create',
}: ChargeModalProps) => {
   const [searchInput, setSearchInput] = useState<string>('');
   const [debouncedSearch, setDebouncedSearch] = useState<string>('');
   const [isIdentifierManuallyEdited, setIsIdentifierManuallyEdited] = useState<boolean>(false);

   // Determine if we're in a group context or standalone
   const isGroupContext = !!chargeGroupId;

   // Use appropriate mutations based on context
   const createStandaloneMutation = useCreateStandaloneCharge();
   const createGroupMutation = useCreateCharge();
   const updateChargeMutation = useUpdateCharge();

   // For percentage charges, list charges based on context
   // If in group: show charges from that group
   // If standalone: show common charges
   const { data: groupChargesData } = useListCharges(chargeGroupId || null);
   const { data: commonChargesData } = useListAllCharges(
      1,
      10,
      debouncedSearch || undefined,
      true // isCommon flag
   );

   const queryClient = useQueryClient();

   // Debounce search input for percentage charge selection
   useEffect(() => {
      const timer = setTimeout(() => {
         setDebouncedSearch(searchInput);
      }, 300);
      return () => clearTimeout(timer);
   }, [searchInput]);

   const form = useForm<ChargeFormValues>({
      resolver: zodResolver(chargeSchema) as any,
      defaultValues: {
         name: '',
         identifier: '',
         chargeType: ChargeType.METERED,
         meter: ChargeMeter.TRIP_DISTANCE,
         priceModel: PriceModel.LINEAR_RATE,
         amount: 0,
         rate: 0,
         currency: 'INR',
         tiers: [{ From: 0, To: 'inf', Flat_fee: 0, currency: 'INR' }],
         // formula: '', // Commented out - not in use
         percentage: 0,
         percentageOfChargeId: '',
         enableCondition: false,
         conditionBlocks: [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '20:00', endTime: '06:00', operator: 'or' as const }],
               operator: 'or' as const,
            },
         ],
      },
   });

   const {
      control,
      watch,
      reset,
      formState: { errors },
      handleSubmit,
      setValue,
   } = form;

   const [percentageChargePopoverOpen, setPercentageChargePopoverOpen] = useState(false);

   const chargeType = watch('chargeType');
   const priceModel = watch('priceModel');
   const enableCondition = watch('enableCondition');
   const meter = watch('meter');

   const {
      fields: tierFields,
      append: appendTier,
      remove: removeTier,
   } = useFieldArray({
      control,
      name: 'tiers',
   });

   const {
      fields: conditionBlockFields,
      append: appendConditionBlock,
      remove: removeConditionBlock,
   } = useFieldArray({
      control,
      name: 'conditionBlocks',
   });

   // Track pricing type for each tier (true = flat fee, false = rate)
   const [tierPricingTypes, setTierPricingTypes] = useState<boolean[]>([true]);

   // Load charge data for edit mode
   useEffect(() => {
      if (mode === 'edit' && chargeData && isOpen) {
         const price = chargeData.price || {};

         // Ensure tiers have correct types (From: number, To: number | 'inf')
         const tiers = (price.tiers || [{ From: 0, To: 'inf', Flat_fee: 0, currency: 'INR' }]).map(tier => ({
            From: Number(tier.From),
            To: tier.To === 'inf' ? 'inf' : Number(tier.To),
            Flat_fee: tier.Flat_fee !== undefined && tier.Flat_fee !== null ? Number(tier.Flat_fee) : undefined,
            Rate: tier.Rate !== undefined && tier.Rate !== null ? Number(tier.Rate) : undefined,
            currency: tier.currency || 'INR',
         }));

         // Parse condition if exists
         let enableCondition = false;
         let conditionBlocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '20:00', endTime: '06:00', operator: 'or' as const }],
               operator: 'or' as const,
            },
         ];

         if (chargeData.condition) {
            enableCondition = true;
            const parsedBlocks = parseJsonLogicToBlocks(chargeData.condition);
            if (parsedBlocks.length > 0) {
               conditionBlocks = parsedBlocks;
            }
         }

         reset({
            name: chargeData.name,
            identifier: chargeData.identifier || '',
            chargeType: chargeData.chargeType,
            meter: chargeData.meter || null,
            priceModel: chargeData.priceModel,
            amount: price.amount !== undefined && price.amount !== null ? Number(price.amount) : 0,
            rate: price.rate !== undefined && price.rate !== null ? Number(price.rate) : 0,
            currency: price.currency || 'INR',
            tiers,
            // formula: price.formula || '', // Commented out - not in use
            percentage: chargeData.percentage !== undefined && chargeData.percentage !== null ? Number(chargeData.percentage) : 0,
            percentageOfChargeId: chargeData.percentageOfChargeId || '',
            enableCondition,
            conditionBlocks,
         });

         // Initialize tier pricing types based on existing data
         setTierPricingTypes(
            tiers.map(tier => {
               // If Flat_fee is defined (even if 0), use Flat Fee mode
               if (tier.Flat_fee !== undefined && tier.Flat_fee !== null) return true;
               // If Rate is defined, use Rate mode
               if (tier.Rate !== undefined && tier.Rate !== null) return false;
               // Default to Flat Fee mode
               return true;
            })
         );
      } else if (mode === 'create') {
         reset({
            name: '',
            identifier: '',
            chargeType: ChargeType.METERED,
            meter: ChargeMeter.TRIP_DISTANCE,
            priceModel: PriceModel.LINEAR_RATE,
            amount: 0,
            rate: 0,
            currency: 'INR',
            tiers: [{ From: 0, To: 'inf', Flat_fee: 0, currency: 'INR' }],
            // formula: '', // Commented out - not in use
            percentage: 0,
            percentageOfChargeId: '',
            enableCondition: false,
            conditionBlocks: [
               {
                  type: ConditionType.BOOKING_TIME,
                  config: [{ startTime: '20:00', endTime: '06:00', operator: 'or' as const }],
                  operator: 'or' as const,
               },
            ],
         });
         setTierPricingTypes([true]);
      }
   }, [chargeData, reset, mode, isOpen]);

   const onSubmit = async (data: ChargeFormValues) => {
      try {
         // Use the identifier from form data (which may be manually edited or auto-generated)
         const identifier = data.identifier || data.name.toLowerCase().replace(/\s+/g, '_');

         // Build price config based on price model
         let price: any = {};

         if (data.priceModel === PriceModel.FLAT_AMOUNT) {
            price = { amount: data.amount, currency: data.currency };
         } else if (data.priceModel === PriceModel.LINEAR_RATE) {
            price = { rate: data.rate, currency: data.currency };
         } else if (data.priceModel === PriceModel.TIERED) {
            // Convert "To" field to number if it's not "inf" and only include the active pricing field
            const tiers = data.tiers?.map(tier => {
               const baseTier: Partial<TierConfig> = {
                  From: tier.From,
                  To: tier.To === 'inf' ? 'inf' : Number(tier.To),
                  currency: tier.currency,
               };

               // Only include Flat_fee or Rate, not both (allow 0 values)
               if (tier.Flat_fee !== undefined && tier.Flat_fee !== null) {
                  baseTier.Flat_fee = tier.Flat_fee;
               } else if (tier.Rate !== undefined && tier.Rate !== null) {
                  baseTier.Rate = tier.Rate;
               }

               return baseTier;
            });
            price = { tiers };
         }
         // Commented out - FORMULA price model not in use
         // else if (data.priceModel === PriceModel.FORMULA) {
         //    price = { formula: data.formula };
         // }

         // Generate JSON Logic from condition blocks if enabled
         let condition: any = undefined;
         if (data.enableCondition && data.conditionBlocks && data.conditionBlocks.length > 0) {
            condition = generateJsonLogicFromBlocks(data.conditionBlocks);
         } else if (!data.enableCondition) {
            // Explicitly set to null to remove the condition in edit mode
            condition = null;
         }

         const payload: any = {
            name: data.name,
            chargeType: data.chargeType,
            meter: data.chargeType === ChargeType.METERED ? data.meter : undefined,
            priceModel: data.priceModel,
            price: data.priceModel !== PriceModel.PERCENTAGE_OF_CHARGE ? price : undefined,
            percentage:
               data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE ? data.percentage : undefined,
            percentageOfChargeId:
               data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE
                  ? data.percentageOfChargeId
                  : undefined,
            condition,
         };

         // Only include identifier when creating a new charge
         if (mode === 'create') {
            payload.identifier = identifier;
         }

         if (mode === 'create') {
            if (isGroupContext) {
               // Create charge within a group
               createGroupMutation.mutate(
                  { chargeGroupId: chargeGroupId!, charge: payload },
                  {
                     onSuccess: () => {
                        toast.success('Charge created successfully');
                        handleClose();
                        queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
                     },
                  }
               );
            } else {
               // Create standalone charge
               createStandaloneMutation.mutate(payload, {
                  onSuccess: () => {
                     toast.success('Charge created successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['all-charges'] });
                  },
               });
            }
         } else if (mode === 'edit' && chargeId) {
            updateChargeMutation.mutate(
               { chargeId, charge: payload },
               {
                  onSuccess: () => {
                     toast.success('Charge updated successfully');
                     handleClose();
                     // Invalidate both group and common charges queries
                     if (isGroupContext) {
                        queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
                        queryClient.invalidateQueries({
                           queryKey: ['charge', chargeGroupId, chargeId],
                        });
                     } else {
                        queryClient.invalidateQueries({ queryKey: ['all-charges'] });
                        queryClient.invalidateQueries({ queryKey: ['charge', chargeId] });
                     }
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
      }
   };

   const handleClose = () => {
      onClose();
      reset();
      setIsIdentifierManuallyEdited(false);
   };

   const createMutation = isGroupContext ? createGroupMutation : createStandaloneMutation;
   const isLoading = mode === 'create' ? createMutation.isPending : updateChargeMutation.isPending;

   // Get appropriate charges based on context
   const allCharges = isGroupContext
      ? groupChargesData?.data || []
      : commonChargesData?.data?.data || [];

   // Filter available charges for percentage reference
   const availableCharges = allCharges.filter((c: Charge) => {
      // Exclude current charge
      if (c.id === chargeId) return false;

      // Exclude percentage-based charges (and those that reference current charge)
      if (c.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) return false;

      return true;
   });

   // Also filter out charges that are referenced by percentage charges pointing to current charge
   const filteredCharges = availableCharges.filter((c: Charge) => {
      // Check if any charge references both this charge AND the current charge being edited
      const hasCircularRef = allCharges.some(
         (charge: Charge) =>
            charge.priceModel === PriceModel.PERCENTAGE_OF_CHARGE &&
            charge.percentageOfChargeId === chargeId &&
            charge.id === c.id
      );
      return !hasCircularRef;
   });

   return (
      <Dialog open={isOpen} onOpenChange={open => !open && handleClose()}>
         <DialogContent
            onInteractOutside={e => e.preventDefault()}
            className='max-w-2xl max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>
                  {mode === 'create'
                     ? isGroupContext
                        ? 'Create Custom Charge'
                        : 'Create New Charge'
                     : 'Edit Charge'}
               </DialogTitle>
               <DialogDescription>
                  {mode === 'create'
                     ? isGroupContext
                        ? 'Create a charge specific to this group only'
                        : 'Create a new shared charge available to all groups'
                     : 'Update charge information'}
               </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4 py-4'>
               {/* Name */}
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='name'>Charge Name *</Label>
                  <Controller
                     control={control}
                     name='name'
                     render={({ field }) => (
                        <Input
                           id='name'
                           placeholder='e.g. Waiting Charge'
                           {...field}
                           onChange={e => {
                              field.onChange(e);
                              // Auto-generate identifier from name in create mode only if not manually edited
                              if (mode === 'create' && !isIdentifierManuallyEdited) {
                                 const generatedIdentifier = e.target.value
                                    .toLowerCase()
                                    .replace(/\s+/g, '_');
                                 setValue('identifier', generatedIdentifier);
                              }
                           }}
                        />
                     )}
                  />
                  {errors.name && <ErrorMessage error={errors.name} />}
               </div>

               {/* Identifier - Only show in create mode */}
               {mode === 'create' && (
                  <div className='flex flex-col gap-2'>
                     <Label htmlFor='identifier'>Identifier</Label>
                     <Controller
                        control={control}
                        name='identifier'
                        render={({ field }) => (
                           <Input
                              id='identifier'
                              placeholder='e.g. waiting_charge'
                              {...field}
                              onChange={e => {
                                 field.onChange(e);
                                 setIsIdentifierManuallyEdited(true);
                              }}
                           />
                        )}
                     />
                     <p className='text-xs text-muted-foreground'>
                        Auto-generated from name, but can be customized. Cannot be edited after
                        creation.
                     </p>
                     {errors.identifier && <ErrorMessage error={errors.identifier} />}
                  </div>
               )}

               {/* Charge Type */}
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='chargeType'>Charge Type *</Label>
                  <Controller
                     control={control}
                     name='chargeType'
                     render={({ field }) => (
                        <Select
                           onValueChange={value => {
                              field.onChange(value);
                              // Auto-select first available price model when charge type changes
                              if (value === ChargeType.FLAT) {
                                 setTimeout(() => {
                                    setValue('priceModel', PriceModel.FLAT_AMOUNT);
                                    // Reset price fields to avoid validation errors
                                    setValue('amount', 0);
                                    setValue('rate', 0);
                                    // setValue('formula', ''); // Commented out - not in use
                                 }, 10);
                              } else if (value === ChargeType.METERED) {
                                 setTimeout(() => {
                                    setValue('priceModel', PriceModel.LINEAR_RATE);
                                    // Reset price fields to avoid validation errors
                                    setValue('amount', 0);
                                    setValue('rate', 0);
                                    // setValue('formula', ''); // Commented out - not in use
                                 }, 10);
                              }
                           }}
                           value={field.value}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue />
                           </SelectTrigger>
                           <SelectContent>
                              {Object.values(ChargeType).map(type => (
                                 <SelectItem key={type} value={type}>
                                    {CHARGE_TYPE_LABELS[type]}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  {errors.chargeType && <ErrorMessage error={errors.chargeType} />}
               </div>

               {/* Meter (conditional) */}
               {chargeType === ChargeType.METERED && (
                  <div className='flex flex-col gap-2'>
                     <Label htmlFor='meter'>Meter *</Label>
                     <Controller
                        control={control}
                        name='meter'
                        render={({ field }) => (
                           <Select onValueChange={field.onChange} value={field.value || ''}>
                              <SelectTrigger className='w-full'>
                                 <SelectValue placeholder='Select meter type' />
                              </SelectTrigger>
                              <SelectContent>
                                 {Object.values(ChargeMeter).map(meter => (
                                    <SelectItem key={meter} value={meter}>
                                       {CHARGE_METER_LABELS[meter]}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                        )}
                     />
                     {errors.meter && <ErrorMessage error={errors.meter} />}
                  </div>
               )}

               {/* Price Model */}
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='priceModel'>Price Model *</Label>
                  <Controller
                     control={control}
                     name='priceModel'
                     render={({ field }) => (
                        <Select
                           onValueChange={value => {
                              field.onChange(value);
                              // Reset price fields when price model changes
                              setValue('amount', 0);
                              setValue('rate', 0);
                              // setValue('formula', ''); // Commented out - not in use
                              setValue('percentage', 0);
                              setValue('percentageOfChargeId', '');
                           }}
                           value={field.value}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue />
                           </SelectTrigger>
                           <SelectContent>
                              {Object.values(PriceModel)
                                 .filter(model => {
                                    // For Flat charge type: only Flat Amount and Percentage of Charge
                                    if (chargeType === ChargeType.FLAT) {
                                       return (
                                          model === PriceModel.FLAT_AMOUNT ||
                                          model === PriceModel.PERCENTAGE_OF_CHARGE
                                       );
                                    }
                                    // For Metered charge type: everything except Flat Amount
                                    if (chargeType === ChargeType.METERED) {
                                       return model !== PriceModel.FLAT_AMOUNT;
                                    }
                                    return true;
                                 })
                                 .map(model => {
                                    // Disable percentage option in standalone charge context
                                    const isPercentageDisabled =
                                       model === PriceModel.PERCENTAGE_OF_CHARGE && !isGroupContext;

                                    return (
                                       <SelectItem
                                          key={model}
                                          value={model}
                                          disabled={isPercentageDisabled}
                                       >
                                          {PRICE_MODEL_LABELS[model]}
                                          {isPercentageDisabled && (
                                             <span className='text-xs text-muted-foreground'>
                                                {' '}
                                                (Only available in charge groups custom charge)
                                             </span>
                                          )}
                                       </SelectItem>
                                    );
                                 })}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  {errors.priceModel && <ErrorMessage error={errors.priceModel} />}
               </div>

               {/* Price Config - Flat Amount */}
               {priceModel === PriceModel.FLAT_AMOUNT && (
                  <div className='grid grid-cols-2 gap-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='amount'>Amount *</Label>
                        <Controller
                           control={control}
                           name='amount'
                           render={({ field }) => (
                              <Input
                                 id='amount'
                                 type='number'
                                 step='0.01'
                                 placeholder='50'
                                 value={getNumberDisplayValue(field.value)}
                                 onChange={e =>
                                    handleNumberInputChange(e.target.value, field.onChange)
                                 }
                              />
                           )}
                        />
                        {errors.amount && <ErrorMessage error={errors.amount} />}
                     </div>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='currency'>Currency *</Label>
                        <Controller
                           control={control}
                           name='currency'
                           render={({ field }) => (
                              <Input id='currency' {...field} disabled className='bg-muted' />
                           )}
                        />
                        {errors.currency && <ErrorMessage error={errors.currency} />}
                     </div>
                  </div>
               )}

               {/* Price Config - Linear Rate */}
               {priceModel === PriceModel.LINEAR_RATE && (
                  <div className='grid grid-cols-2 gap-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='rate'>
                           Rate *{' '}
                           {meter && (
                              <span className='text-xs text-muted-foreground font-normal'>
                                 (per 1 {getMeterUnit(meter)})
                              </span>
                           )}
                        </Label>
                        <Controller
                           control={control}
                           name='rate'
                           render={({ field }) => (
                              <Input
                                 id='rate'
                                 type='number'
                                 step='0.01'
                                 placeholder='0.5'
                                 value={getNumberDisplayValue(field.value)}
                                 onChange={e =>
                                    handleNumberInputChange(e.target.value, field.onChange)
                                 }
                              />
                           )}
                        />
                        {errors.rate && <ErrorMessage error={errors.rate} />}
                     </div>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='currency'>Currency *</Label>
                        <Controller
                           control={control}
                           name='currency'
                           render={({ field }) => (
                              <Input id='currency' {...field} disabled className='bg-muted' />
                           )}
                        />
                        {errors.currency && <ErrorMessage error={errors.currency} />}
                     </div>
                  </div>
               )}

               {/* Price Config - Tiered */}
               {priceModel === PriceModel.TIERED && (
                  <div className='flex flex-col gap-2'>
                     <Label>Tiers *</Label>
                     <div className='space-y-3'>
                        {tierFields.map((field, index) => (
                           <div key={field.id} className='border rounded-md p-3 space-y-2'>
                              <div className='flex justify-between items-center'>
                                 <span className='text-sm font-medium'>Tier {index + 1}</span>
                                 {tierFields.length > 1 && (
                                    <Button
                                       type='button'
                                       variant='ghost'
                                       size='sm'
                                       onClick={() => {
                                          removeTier(index);
                                          const newTypes = tierPricingTypes.filter(
                                             (_, i) => i !== index
                                          );
                                          setTierPricingTypes(newTypes);
                                       }}
                                    >
                                       <Trash2 className='h-4 w-4' />
                                    </Button>
                                 )}
                              </div>
                              <div className='grid grid-cols-2 gap-2'>
                                 <div className='flex flex-col gap-1'>
                                    <Label className='text-xs'>
                                       From{meter && ` (${getMeterUnit(meter)})`}
                                    </Label>
                                    <Controller
                                       control={control}
                                       name={`tiers.${index}.From`}
                                       render={({ field }) => (
                                          <Input
                                             type='number'
                                             step='0.01'
                                             placeholder='0'
                                             value={getNumberDisplayValue(field.value)}
                                             onChange={e =>
                                                handleNumberInputChange(
                                                   e.target.value,
                                                   field.onChange
                                                )
                                             }
                                          />
                                       )}
                                    />
                                    {errors.tiers?.[index]?.From && (
                                       <ErrorMessage error={errors.tiers[index].From} />
                                    )}
                                 </div>
                                 <div className='flex flex-col gap-1'>
                                    <Label className='text-xs'>
                                       To{meter && ` (${getMeterUnit(meter)})`}
                                    </Label>
                                    <Controller
                                       control={control}
                                       name={`tiers.${index}.To`}
                                       render={({ field }) => (
                                          <div className='flex flex-col gap-1'>
                                             <Input
                                                type='text'
                                                placeholder='10 or inf'
                                                value={field.value}
                                                onChange={e => {
                                                   const value = e.target.value;
                                                   // Allow empty value
                                                   if (value === '') {
                                                      field.onChange('');
                                                      return;
                                                   }
                                                   // Allow 'inf' as-is (store as string) - also allow partial typing like 'i', 'in'
                                                   if (value.toLowerCase() === 'inf') {
                                                      field.onChange('inf');
                                                      return;
                                                   }
                                                   // Allow partial typing of 'inf'
                                                   if ('inf'.startsWith(value.toLowerCase())) {
                                                      field.onChange(value.toLowerCase());
                                                      return;
                                                   }
                                                   // For numeric values, restrict to 2 decimal places and store as number
                                                   const decimalParts = value.split('.');
                                                   if (
                                                      decimalParts.length === 2 &&
                                                      decimalParts[1].length > 2
                                                   ) {
                                                      // Don't update if more than 2 decimal places
                                                      return;
                                                   }
                                                   // Allow valid numeric input including partial decimals (like "10.")
                                                   if (/^\d*\.?\d*$/.test(value)) {
                                                      const num = parseFloat(value);
                                                      // If it's a complete number, store as number; otherwise store as string for intermediate state
                                                      if (!isNaN(num) && !value.endsWith('.')) {
                                                         field.onChange(num);
                                                      } else if (
                                                         value.endsWith('.') ||
                                                         value === ''
                                                      ) {
                                                         // Allow typing decimal point
                                                         field.onChange(value);
                                                      }
                                                   }
                                                }}
                                             />
                                             <p className='text-xs text-muted-foreground'>
                                                Enter a number or "inf" for infinity
                                             </p>
                                          </div>
                                       )}
                                    />
                                    {errors.tiers?.[index]?.To && (
                                       <ErrorMessage error={errors.tiers[index].To} />
                                    )}
                                 </div>
                              </div>
                              <div className='flex flex-col gap-2'>
                                 <Label className='text-xs'>Pricing Type *</Label>
                                 <RadioGroup
                                    value={tierPricingTypes[index] ? 'flat_fee' : 'rate'}
                                    onValueChange={value => {
                                       const checked = value === 'flat_fee';
                                       const newTypes = [...tierPricingTypes];
                                       newTypes[index] = checked;
                                       setTierPricingTypes(newTypes);
                                       // Clear the other field
                                       if (checked) {
                                          form.setValue(`tiers.${index}.Rate`, undefined);
                                       } else {
                                          form.setValue(`tiers.${index}.Flat_fee`, undefined);
                                       }
                                    }}
                                    className='flex gap-4'
                                 >
                                    <div className='flex items-center gap-2'>
                                       <RadioGroupItem
                                          value='flat_fee'
                                          id={`tier-${index}-flat-fee`}
                                       />
                                       <Label
                                          htmlFor={`tier-${index}-flat-fee`}
                                          className='text-xs font-normal cursor-pointer'
                                       >
                                          Flat Fee
                                       </Label>
                                    </div>
                                    <div className='flex items-center gap-2'>
                                       <RadioGroupItem value='rate' id={`tier-${index}-rate`} />
                                       <Label
                                          htmlFor={`tier-${index}-rate`}
                                          className='text-xs font-normal cursor-pointer'
                                       >
                                          Rate
                                       </Label>
                                    </div>
                                 </RadioGroup>
                              </div>
                              <div className='flex flex-col gap-2'>
                                 <div className='grid grid-cols-2 gap-2'>
                                    {tierPricingTypes[index] ? (
                                       <div className='flex flex-col gap-1'>
                                          <Label className='text-xs'>Flat Fee *</Label>
                                          <Controller
                                             control={control}
                                             name={`tiers.${index}.Flat_fee`}
                                             render={({ field }) => (
                                                <Input
                                                   type='number'
                                                   step='0.01'
                                                   placeholder='50 or 0'
                                                   value={getNumberDisplayValue(field.value)}
                                                   onChange={e =>
                                                      handleNumberInputChange(
                                                         e.target.value,
                                                         field.onChange
                                                      )
                                                   }
                                                />
                                             )}
                                          />
                                       </div>
                                    ) : (
                                       <div className='flex flex-col gap-1'>
                                          <Label className='text-xs'>Rate *</Label>
                                          <Controller
                                             control={control}
                                             name={`tiers.${index}.Rate`}
                                             render={({ field }) => (
                                                <Input
                                                   type='number'
                                                   step='0.01'
                                                   placeholder='0.5 or 0'
                                                   value={getNumberDisplayValue(field.value)}
                                                   onChange={e =>
                                                      handleNumberInputChange(
                                                         e.target.value,
                                                         field.onChange
                                                      )
                                                   }
                                                />
                                             )}
                                          />
                                       </div>
                                    )}
                                    <div className='flex flex-col gap-1'>
                                       <Label className='text-xs'>Currency *</Label>
                                       <Controller
                                          control={control}
                                          name={`tiers.${index}.currency`}
                                          render={({ field }) => (
                                             <Input {...field} disabled className='bg-muted' />
                                          )}
                                       />
                                    </div>
                                 </div>
                              </div>
                              {errors.tiers?.[index] && (
                                 <ErrorMessage error={errors.tiers[index]} />
                              )}
                           </div>
                        ))}
                     </div>
                     <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={() => {
                           appendTier({ From: 0, To: 'inf', Flat_fee: 0, currency: 'INR' });
                           setTierPricingTypes([...tierPricingTypes, true]);
                        }}
                     >
                        <Plus className='h-4 w-4 mr-1' />
                        Add Tier
                     </Button>
                     {errors.tiers && <ErrorMessage error={errors.tiers as any} />}
                  </div>
               )}

               {/* Price Config - Percentage */}
               {priceModel === PriceModel.PERCENTAGE_OF_CHARGE && (
                  <div className='space-y-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='percentage'>Percentage (0.0001 - 100) *</Label>
                        <Controller
                           control={control}
                           name='percentage'
                           render={({ field }) => (
                              <Input
                                 id='percentage'
                                 type='number'
                                 step='0.01'
                                 max='100'
                                 placeholder='15.5'
                                 value={getNumberDisplayValue(field.value)}
                                 onChange={e =>
                                    handleNumberInputChange(e.target.value, field.onChange)
                                 }
                              />
                           )}
                        />
                        {errors.percentage && <ErrorMessage error={errors.percentage} />}
                        <p className='text-xs text-muted-foreground'>
                           Enter a value with up to 2 decimal places between 0.01 and 100 (e.g.,
                           15.5 for 15.5%)
                        </p>
                     </div>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='percentageOfChargeId'>Reference Charge *</Label>
                        <Controller
                           control={control}
                           name='percentageOfChargeId'
                           render={({ field }) => (
                              <Popover
                                 open={percentageChargePopoverOpen}
                                 onOpenChange={setPercentageChargePopoverOpen}
                              >
                                 <PopoverTrigger asChild>
                                    <Button
                                       variant='outline'
                                       role='combobox'
                                       aria-expanded={percentageChargePopoverOpen}
                                       className='bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]'
                                    >
                                       <span
                                          className={cn(
                                             'truncate',
                                             !field.value && 'text-muted-foreground'
                                          )}
                                       >
                                          {field.value
                                             ? filteredCharges?.find(
                                                  (charge: Charge) => charge.id === field.value
                                               )?.name +
                                               (filteredCharges?.find(
                                                  (charge: Charge) => charge.id === field.value
                                               )?.identifier
                                                  ? ` (${
                                                       filteredCharges?.find(
                                                          (charge: Charge) =>
                                                             charge.id === field.value
                                                       )?.identifier
                                                    })`
                                                  : '')
                                             : 'Select a charge'}
                                       </span>
                                       <ChevronDownIcon
                                          size={16}
                                          className='text-muted-foreground/80 shrink-0'
                                          aria-hidden='true'
                                       />
                                    </Button>
                                 </PopoverTrigger>
                                 <PopoverContent
                                    className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
                                    align='start'
                                 >
                                    <Command shouldFilter={false}>
                                       <div className='relative'>
                                          <CommandInput
                                             placeholder='Search charge...'
                                             value={searchInput}
                                             onValueChange={setSearchInput}
                                          />
                                          {!isGroupContext &&
                                             commonChargesData &&
                                             searchInput !== debouncedSearch && (
                                                <div className='absolute right-3 top-1/2 -translate-y-1/2'>
                                                   <Spinner className='h-4 w-4' />
                                                </div>
                                             )}
                                       </div>
                                       <CommandList>
                                          <CommandEmpty>No charge found.</CommandEmpty>
                                          <CommandGroup>
                                             {filteredCharges?.map((charge: Charge) => (
                                                <CommandItem
                                                   key={charge.id}
                                                   value={charge.id}
                                                   onSelect={(currentValue: string) => {
                                                      field.onChange(
                                                         currentValue === field.value
                                                            ? ''
                                                            : currentValue
                                                      );
                                                      setPercentageChargePopoverOpen(false);
                                                   }}
                                                >
                                                   {charge.name}{' '}
                                                   {charge.identifier
                                                      ? `(${charge.identifier})`
                                                      : ''}
                                                   {field.value === charge.id && (
                                                      <CheckIcon size={16} className='ml-auto' />
                                                   )}
                                                </CommandItem>
                                             ))}
                                          </CommandGroup>
                                       </CommandList>
                                    </Command>
                                 </PopoverContent>
                              </Popover>
                           )}
                        />
                        {errors.percentageOfChargeId && (
                           <ErrorMessage error={errors.percentageOfChargeId} />
                        )}
                        <p className='text-xs text-gray-500'>
                           {isGroupContext
                              ? 'Select a charge from this group to calculate percentage from'
                              : 'Select a common charge to calculate percentage from'}
                        </p>
                     </div>
                  </div>
               )}

               {/* Price Config - Formula */}
               {/* Commented out - FORMULA price model not in use */}
               {/* {priceModel === PriceModel.FORMULA && (
                  <div className='flex flex-col gap-2'>
                     <Label htmlFor='formula'>Formula *</Label>
                     <Controller
                        control={control}
                        name='formula'
                        render={({ field }) => (
                           <Input
                              id='formula'
                              placeholder='base_fare + (distance * rate)'
                              {...field}
                           />
                        )}
                     />
                     {errors.formula && <ErrorMessage error={errors.formula} />}
                  </div>
               )} */}

               {/* Condition Configuration */}
               <div className='border-t pt-4 mt-4'>
                  <div className='flex items-center justify-between mb-4'>
                     <div className='flex flex-col gap-1'>
                        <Label htmlFor='enableCondition' className='text-base font-medium'>
                           Enable based on condition
                        </Label>
                        <p className='text-xs text-muted-foreground'>
                           Whether this charge to be added to all rides or based on a specific
                           condition such as booking time
                        </p>
                     </div>
                     <Controller
                        control={control}
                        name='enableCondition'
                        render={({ field }) => (
                           <Switch
                              id='enableCondition'
                              checked={field.value}
                              onCheckedChange={field.onChange}
                           />
                        )}
                     />
                  </div>

                  {/* Condition Blocks Configuration */}
                  {enableCondition && (
                     <div className='space-y-4'>
                        <p className='text-sm text-muted-foreground'>
                           Define one or more conditions. Each condition can be combined with AND/OR
                           operators.
                        </p>

                        {/* Condition Blocks */}
                        <div className='space-y-4'>
                           {conditionBlockFields.map((blockField, blockIndex) => (
                              <div
                                 key={blockField.id}
                                 className='border rounded-md p-4 bg-muted/30 space-y-4'
                              >
                                 <div className='flex justify-between items-center'>
                                    <span className='text-base font-medium'>
                                       Condition {blockIndex + 1}
                                    </span>
                                    {conditionBlockFields.length > 1 && (
                                       <Button
                                          type='button'
                                          variant='ghost'
                                          size='sm'
                                          onClick={() => removeConditionBlock(blockIndex)}
                                       >
                                          <Trash2 className='h-4 w-4' />
                                       </Button>
                                    )}
                                 </div>

                                 {/* Condition Type Selector - For now always BOOKING_TIME */}
                                 <div className='flex flex-col gap-2'>
                                    <Label>Condition Type *</Label>
                                    <Controller
                                       control={control}
                                       name={`conditionBlocks.${blockIndex}.type`}
                                       render={({ field }) => (
                                          <Select
                                             onValueChange={field.onChange}
                                             value={field.value}
                                          >
                                             <SelectTrigger className='w-full'>
                                                <SelectValue />
                                             </SelectTrigger>
                                             <SelectContent>
                                                {Object.values(ConditionType).map(type => (
                                                   <SelectItem key={type} value={type}>
                                                      {CONDITION_TYPE_LABELS[type]}
                                                   </SelectItem>
                                                ))}
                                             </SelectContent>
                                          </Select>
                                       )}
                                    />
                                 </div>

                                 {/* Render config based on condition type */}
                                 <BookingTimeConfig
                                    control={control}
                                    blockIndex={blockIndex}
                                    errors={errors}
                                 />

                                 {/* Operator between condition blocks */}
                                 {blockIndex < conditionBlockFields.length - 1 && (
                                    <div className='flex items-center gap-2 pt-3 border-t'>
                                       <Label className='text-sm text-muted-foreground'>
                                          Combine with next condition using:
                                       </Label>
                                       <Controller
                                          control={control}
                                          name={`conditionBlocks.${blockIndex}.operator`}
                                          render={({ field }) => (
                                             <div className='flex gap-2'>
                                                <Button
                                                   type='button'
                                                   variant={
                                                      field.value === 'and' ? 'default' : 'outline'
                                                   }
                                                   size='sm'
                                                   onClick={() => field.onChange('and')}
                                                >
                                                   AND
                                                </Button>
                                                <Button
                                                   type='button'
                                                   variant={
                                                      field.value === 'or' ? 'default' : 'outline'
                                                   }
                                                   size='sm'
                                                   onClick={() => field.onChange('or')}
                                                >
                                                   OR
                                                </Button>
                                             </div>
                                          )}
                                       />
                                    </div>
                                 )}
                              </div>
                           ))}
                        </div>

                        {/* Add Condition Button */}
                        <Button
                           type='button'
                           variant='outline'
                           size='sm'
                           onClick={() =>
                              appendConditionBlock({
                                 type: ConditionType.BOOKING_TIME,
                                 config: [
                                    {
                                       startTime: '09:00',
                                       endTime: '17:00',
                                       operator: 'or' as const,
                                    },
                                 ],
                                 operator: 'or' as const,
                              })
                           }
                           className='w-full'
                        >
                           <Plus className='h-4 w-4 mr-1' />
                           Add Condition
                        </Button>
                        {errors.conditionBlocks &&
                           typeof errors.conditionBlocks === 'object' &&
                           !Array.isArray(errors.conditionBlocks) && (
                              <ErrorMessage error={errors.conditionBlocks as any} />
                           )}
                     </div>
                  )}
               </div>

               {/* Actions */}
               <div className='flex gap-3 pt-4'>
                  <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                     Cancel
                  </Button>
                  <Button type='submit' disabled={isLoading} className='flex-1'>
                     {isLoading ? (
                        <>
                           {mode === 'create' ? 'Creating...' : 'Updating...'}
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : mode === 'create' ? (
                        'Create Charge'
                     ) : (
                        'Update Charge'
                     )}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
};

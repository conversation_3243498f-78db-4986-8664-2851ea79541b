'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useVehicleTypes } from '../api/queries';
import { useCreateDriverVehicle, useVerifyDriverVehicle } from '../api/mutations';
import { CreateDriverVehicleRequest } from '../types/driver';
import { toast } from 'sonner';

const createVehicleSchema = z.object({
   vehicleTypeId: z.string().min(1, 'Vehicle type is required'),
   vehicleNumber: z
      .string()
      .min(1, 'Vehicle number is required')
      .regex(
         /^[A-Z]{2}\d{1,2}[A-Z]{1,3}\d{4}$/,
         'Invalid vehicle number format. Use format like: MH12AB1234'
      ),
   isPrimary: z.boolean(),
});

type CreateVehicleForm = z.infer<typeof createVehicleSchema>;

interface CreateVehicleModalProps {
   isOpen: boolean;
   onClose: () => void;
   profileId: string;
   onVehicleCreated?: (vehicleId: string) => void;
}

export function CreateVehicleModal({
   isOpen,
   onClose,
   profileId,
   onVehicleCreated,
}: CreateVehicleModalProps) {

   const { data: vehicleTypesResponse } = useVehicleTypes();
   const vehicleTypes = vehicleTypesResponse?.data || [];

   const createVehicleMutation = useCreateDriverVehicle();
   const verifyVehicleMutation = useVerifyDriverVehicle();

   const {
      register,
      handleSubmit,
      formState: { errors },
      setValue,
      watch,
      reset,
   } = useForm<CreateVehicleForm>({
      resolver: zodResolver(createVehicleSchema),
      defaultValues: {
         vehicleTypeId: '',
         vehicleNumber: '',
         isPrimary: false,
      },
   });

   const selectedVehicleTypeId = watch('vehicleTypeId');
   const isPrimary = watch('isPrimary');
   const isLoading = createVehicleMutation.isPending || verifyVehicleMutation.isPending;

   const onSubmit = (data: CreateVehicleForm) => {
      const payload: CreateDriverVehicleRequest = {
         profileId,
         vehicleTypeId: data.vehicleTypeId,
         vehicleNumber: data.vehicleNumber.toUpperCase().trim(),
         isPrimary: data.isPrimary,
      };

      createVehicleMutation.mutate(payload, {
         onSuccess: (response) => {
            if (response.data?.id) {
               verifyVehicleMutation.mutate(response.data.id, {
                  onSuccess: () => {
                     toast.success('Vehicle added and verified successfully');
                     reset();
                     onClose();
                     if (onVehicleCreated) {
                        onVehicleCreated(response.data.id);
                     }
                  },
                  onSettled: () => {
                     if (!verifyVehicleMutation.isSuccess) {
                        toast.success('Vehicle added successfully');
                        reset();
                        onClose();
                        if (onVehicleCreated) {
                           onVehicleCreated(response.data.id);
                        }
                     }
                  }
               });
            } else {
               toast.success('Vehicle added successfully');
               reset();
               onClose();
            }
         }
      });
   };

   const handleClose = () => {
      reset();
      onClose();
   };

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='sm:max-w-md'
         >
            <DialogHeader>
               <DialogTitle>Add New Vehicle</DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
               {/* Vehicle Type Selection */}
               <div className='space-y-2'>
                  <Label htmlFor='vehicleTypeId'>Vehicle Type *</Label>
                  <Select
                     value={selectedVehicleTypeId}
                     onValueChange={value => setValue('vehicleTypeId', value)}
                  >
                     <SelectTrigger className='w-full'>
                        <SelectValue placeholder='Select vehicle type' />
                     </SelectTrigger>
                     <SelectContent>
                        {vehicleTypes.map(type => (
                           <SelectItem key={type.id} value={type.id}>
                              <div>
                                 <div className='font-medium'>{type.name}</div>
                                 {type.description && (
                                    <div className='text-sm text-gray-500'>{type.description}</div>
                                 )}
                              </div>
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
                  {errors.vehicleTypeId && (
                     <p className='text-sm text-red-600'>{errors.vehicleTypeId.message}</p>
                  )}
               </div>

               {/* Vehicle Number */}
               <div className='space-y-2'>
                  <Label htmlFor='vehicleNumber'>Vehicle Number *</Label>
                  <Input
                     id='vehicleNumber'
                     {...register('vehicleNumber')}
                     placeholder='e.g., MH12AB1234'
                     className='uppercase'
                     onChange={e => {
                        const value = e.target.value.toUpperCase();
                        setValue('vehicleNumber', value);
                     }}
                  />
                  {errors.vehicleNumber && (
                     <p className='text-sm text-red-600'>{errors.vehicleNumber.message}</p>
                  )}
                  <p className='text-xs text-gray-500'>
                     Format: State code (2 letters) + RTO code (1-2 digits) + Series (1-3 letters) +
                     Number (4 digits)
                  </p>
               </div>

               {/* Primary Vehicle Checkbox */}
               <div className='flex items-center space-x-2'>
                  <Checkbox
                     id='isPrimary'
                     checked={isPrimary}
                     onCheckedChange={checked => setValue('isPrimary', !!checked)}
                  />
                  <Label htmlFor='isPrimary' className='text-sm'>
                     Set as primary vehicle
                  </Label>
               </div>

               {/* Action Buttons */}
               <div className='flex justify-end space-x-2 pt-4'>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={handleClose}
                     disabled={isLoading}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={isLoading || !selectedVehicleTypeId}
                     className='min-w-[100px]'
                  >
                     {isLoading ? 'Adding...' : 'Add Vehicle'}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
}

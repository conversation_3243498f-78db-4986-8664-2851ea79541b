'use client';

import { useRef, useEffect } from 'react';
import { Autocomplete } from '@react-google-maps/api';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MapPin } from 'lucide-react';
import { Location } from '../types/book-ride';

interface LocationSearchProps {
  label: string;
  placeholder: string;
  value: string;
  onLocationSelect: (location: Location) => void;
  onInputChange: (value: string) => void;
  biasLocation?: Location | null;
}

export function LocationSearch({
  label,
  placeholder,
  value,
  onLocationSelect,
  onInputChange,
  biasLocation,
}: LocationSearchProps) {
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

  const onLoad = (autocomplete: google.maps.places.Autocomplete) => {
    autocompleteRef.current = autocomplete;

    // Configure to search for all location types with optional location bias
    const options: google.maps.places.AutocompleteOptions = {
      fields: ['name', 'geometry', 'place_id', 'formatted_address'],
    };

    // If biasLocation is provided, prioritize results near that location
    if (biasLocation) {
      const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
      const circle = new google.maps.Circle({
        center: center,
        radius: 50000, // 50km radius in meters
      });
      options.bounds = circle.getBounds() || undefined;
      options.strictBounds = false; // Allow results outside bounds if no nearby matches
    }

    autocomplete.setOptions(options);
  };

  // Update autocomplete options when biasLocation changes
  useEffect(() => {
    if (autocompleteRef.current) {
      const options: google.maps.places.AutocompleteOptions = {
        fields: ['name', 'geometry', 'place_id', 'formatted_address'],
      };

      if (biasLocation) {
        const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
        const circle = new google.maps.Circle({
          center: center,
          radius: 50000, // 50km radius in meters
        });
        options.bounds = circle.getBounds() || undefined;
        options.strictBounds = false;
      }

      autocompleteRef.current.setOptions(options);
    }
  }, [biasLocation]);

  const onPlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();

      if (place.geometry?.location) {
        const location: Location = {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng(),
          address: place.formatted_address || place.name || '',
        };

        onLocationSelect(location);
        onInputChange(place.formatted_address || place.name || '');
      }
    }
  };

  return (
    <div className='flex flex-col gap-2'>
      <Label htmlFor={label.toLowerCase().replace(' ', '-')}>{label}</Label>
      <div className='relative'>
        <MapPin className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400' />
        <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
          <Input
            id={label.toLowerCase().replace(' ', '-')}
            type='text'
            placeholder={placeholder}
            value={value}
            onChange={e => onInputChange(e.target.value)}
            className='pl-10'
          />
        </Autocomplete>
      </div>
    </div>
  );
}

export const validateAadhaarNumber = (aadhaarNumber: string): boolean => {
   if (!aadhaarNumber || typeof aadhaarNumber !== 'string') {
      return false;
   }

   const cleanAadhaar = aadhaarNumber.replace(/\s/g, '');

   if (cleanAadhaar.length !== 12) {
      return false;
   }

   if (!/^\d{12}$/.test(cleanAadhaar)) {
      return false;
   }

   // Aadhaar number cannot start with 0 or 1
   if (cleanAadhaar.startsWith('0') || cleanAadhaar.startsWith('1')) {
      return false;
   }

   return true;
};

export const validateDrivingLicenseNumber = (licenseNumber: string): boolean => {
   if (!licenseNumber || typeof licenseNumber !== 'string') {
      return false;
   }

   // Remove all separators and convert to uppercase
   const cleanLicense = licenseNumber.replace(/[-\s/]/g, '').toUpperCase();

   // Valid Indian state codes
   const stateCodesIndia = [
      'AN', 'AP', 'AR', 'AS', 'BR', 'CG', 'CH', 'DN', 'DD', 'DL', 'GA', 'GJ', 'HR', 'HP', 'JK', 
      'JH', 'KA', 'KL', 'LD', 'MP', 'MH', 'MN', 'ML', 'MZ', 'NL', 'OR', 'PY', 'PB', 'RJ', 'SK', 
      'TN', 'TS', 'TR', 'UP', 'UK', 'WB'
   ];

   // Extract state code (first 2 characters)
   const stateCode = cleanLicense.substring(0, 2);
   
   // Validate state code
   if (!stateCodesIndia.includes(stateCode)) {
      return false;
   }

   // Check for new format first (Post-2019): SSRRYYYYNNNNNNN (15 characters)
   const newFormatPattern = /^[A-Z]{2}[0-9]{2}[0-9]{4}[0-9]{7}$/;
   if (newFormatPattern.test(cleanLicense)) {
      // Validate year (1980 to current year)
      const year = parseInt(cleanLicense.substring(4, 8));
      const currentYear = new Date().getFullYear();
      return year >= 1980 && year <= currentYear;
   }

   // Check for old format with year: SSRRYYYYNNNNNN (13-14 characters)
   const oldFormatWithYearPattern = /^[A-Z]{2}[0-9]{2}[0-9]{4}[0-9]{5,6}$/;
   if (oldFormatWithYearPattern.test(cleanLicense)) {
      // Validate year (1980 to current year)
      const year = parseInt(cleanLicense.substring(4, 8));
      const currentYear = new Date().getFullYear();
      return year >= 1980 && year <= currentYear;
   }

   // Check for old format without year: SSRRNNNNNN (9-11 characters)
   const oldFormatWithoutYearPattern = /^[A-Z]{2}[0-9]{2}[0-9]{5,7}$/;
   if (oldFormatWithoutYearPattern.test(cleanLicense)) {
      return true;
   }

   return false;
};

export const formatAadhaarNumber = (value: string): string => {
   const cleaned = value.replace(/\D/g, '');
   const limited = cleaned.substring(0, 12);
   return limited.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3').trim();
};

export const formatDrivingLicenseNumber = (value: string): string => {
   return value.toUpperCase().replace(/[^A-Z0-9]/g, '');
};
import { ROUTE_URLS } from '@/data/route-urls';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
   BadgeDollarSign,
   Car,
   Coins,
   DollarSign,
   Layers,
   Languages,
   MapPin,
   Package,
   Percent,
   Radar,
   Receipt,
   ScrollText,
   Settings,
   ShieldUser,
   ShoppingBag,
   Target,
   Truck,
   Users,
   Wallet,
   Logs,
   ClipboardClock,
} from 'lucide-react';

export const SIDEBAR_ROUTES = [
   {
      title: 'Resources',
      url: '#',
      icon: Layers,
      isActive: false,
      items: [
         {
            title: 'Drivers',
            url: ROUTE_URLS.DASHBOARD_DRIVERS,
            icon: Car,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.DRIVER,
         },
         {
            title: 'Driver Radar',
            url: ROUTE_URLS.DASHBOARD_DRIVER_RADAR,
            icon: Radar,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.DRIVER_RADAR,
         },
         {
            title: 'Riders',
            url: ROUTE_URLS.DASHBOARD_RIDERS,
            icon: Users,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.RIDER,
         },
         {
            title: 'Cities',
            url: ROUTE_URLS.DASHBOARD_CITIES,
            icon: MapPin,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.CITY,
         },
         {
            title: 'Products',
            url: ROUTE_URLS.DASHBOARD_PRODUCTS,
            icon: ShoppingBag,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.PRODUCT,
         },
         {
            title: 'Services',
            url: ROUTE_URLS.DASHBOARD_PRODUCT_SERVICES,
            icon: Package,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.PRODUCT_SERVICE,
         },
         {
            title: 'Languages',
            url: ROUTE_URLS.DASHBOARD_LANGUAGES,
            icon: Languages,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.LANGUAGE,
         },
         {
            title: 'Categories',
            url: ROUTE_URLS.DASHBOARD_VEHICLE_CATEGORIES,
            icon: Truck,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.VEHICLE_CATEGORY,
         },
         {
            title: 'Zone Types',
            url: ROUTE_URLS.DASHBOARD_ZONE_TYPES,
            icon: Target,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.ZONE_TYPE,
         },
         {
            title: 'Book Ride',
            url: ROUTE_URLS.DASHBOARD_BOOK_RIDE,
            icon: ClipboardClock,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.BOOK_RIDE,
         },
      ],
   },
   {
      title: 'Access',
      url: '#',
      icon: Users,
      isActive: false,
      items: [
         {
            title: 'Roles',
            url: ROUTE_URLS.DASHBOARD_ROLES,
            icon: Settings,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.ROLES,
         },
         {
            title: 'Admins',
            url: ROUTE_URLS.DASHBOARD_ADMINS,
            icon: ShieldUser,
            isActive: false,
            pagePermission: { ...RBAC_PERMISSIONS.SUB_ADMIN, ...RBAC_PERMISSIONS.CITY_ADMIN },
         },
      ],
   },
   {
      title: 'Billing',
      url: '#',
      icon: Wallet,
      isActive: false,
      items: [
         {
            title: 'Charges',
            url: ROUTE_URLS.DASHBOARD_CHARGES,
            icon: Coins,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.CHARGE,
         },
         {
            title: 'Charge Groups',
            url: ROUTE_URLS.DASHBOARD_CHARGE_GROUPS,
            icon: BadgeDollarSign,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.CHARGE_GROUPS,
         },
         {
            title: 'Tax Groups',
            url: ROUTE_URLS.DASHBOARD_TAX_GROUPS,
            icon: Receipt,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.TAX_GROUP,
         },
         {
            title: 'Commissions',
            url: ROUTE_URLS.DASHBOARD_COMMISSIONS,
            icon: Percent,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.COMMISSION,
         },
      ],
   },
   {
      title: 'Reports',
      url: '#',
      icon: Logs,
      isActive: false,
      items: [
         {
            title: 'Rides',
            url: ROUTE_URLS.DASHBOARD_RIDES,
            icon: ScrollText,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.RIDES,
         },
         {
            title: 'Earnings',
            url: ROUTE_URLS.DASHBOARD_DRIVER_EARNINGS,
            icon: DollarSign,
            isActive: false,
            pagePermission: RBAC_PERMISSIONS.DRIVER_EARNINGS,
         },
      ],
   },
];

import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   ZoneResponse,
   ListZoneParams,
   ListZoneResponse,
   CityZonesResponse,
} from '../types/city-zone';

/**
 * Hook for getting zones by city ID
 */
export const useCityZones = (cityId: string, params?: { includeRelations?: boolean }) => {
   return useQuery({
      queryKey: ['city-zones', cityId, params?.includeRelations],
      queryFn: (): Promise<CityZonesResponse> => {
         return apiClient.get(`/zones/city/${cityId}`, {
            params: {
               includeRelations: params?.includeRelations,
            },
         });
      },
      enabled: !!cityId,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for listing zones with pagination and filters
 */
export const useListZones = ({
   page = 1,
   limit = 10,
   search,
   zoneTypeId,
   includeRelations = true,
}: ListZoneParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['zones', page, limit, search, zoneTypeId, includeRelations],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListZoneResponse> => {
         return apiClient.get('/zones', {
            params: {
               page,
               limit,
               search,
               zoneTypeId,
               includeRelations,
            },
         });
      },
   });
};

/**
 * Hook for getting a single zone by ID
 */
export const useGetZone = (id: string | null) => {
   return useQuery({
      queryKey: ['zone', id],
      queryFn: (): Promise<ZoneResponse> => {
         return apiClient.get(`/zones/${id || ''}`, {
            params: {
               includeRelations: false,
            },
         });
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for checking if zone name exists in city
 */
export const useCheckZoneNameExists = (cityId: string, name: string) => {
   return useQuery({
      queryKey: ['zone-name-check', cityId, name],
      queryFn: (): Promise<{
         success: boolean;
         message: string;
         data: { exists: boolean; available: boolean };
         timestamp: number;
      }> => {
         return apiClient.get(`/zones/check-name/${cityId}/${encodeURIComponent(name)}`);
      },
      enabled: !!cityId && !!name && name.length > 0,
      refetchOnWindowFocus: false,
   });
};

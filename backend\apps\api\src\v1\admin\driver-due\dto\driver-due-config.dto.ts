import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNumber, IsOptional, IsBoolean, Min } from 'class-validator';

export class SetDriverDueLimitDto {
  @ApiProperty({
    description: 'City ID',
    example: 'city-uuid-123',
  })
  @IsUUID()
  cityId!: string;

  @ApiProperty({
    description: 'Maximum due limit amount in ₹',
    example: 750,
    type: 'number',
  })
  @IsNumber()
  @Min(0)
  maxDueLimit!: number;
}

export class UpdateDriverDueLimitDto {
  @ApiProperty({
    description: 'Maximum due limit amount in ₹',
    example: 750,
    type: 'number',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxDueLimit?: number;

  @ApiProperty({
    description: 'Is the due config active',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class DriverDueConfigResponseDto {
  @ApiProperty({
    description: 'Due config ID',
    example: 'config-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'City ID',
    example: 'city-uuid-123',
  })
  cityId!: string;

  @ApiProperty({
    description: 'Maximum due limit amount in ₹',
    example: 750,
    type: 'number',
  })
  maxDueLimit!: number;

  @ApiProperty({
    description: 'Is the due config active',
    example: true,
  })
  isActive!: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt!: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt!: string;
}

export class AdminDueLimitResponseDto {
  @ApiProperty({
    description: 'API response success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Due limit configured successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Due config data',
    type: DriverDueConfigResponseDto,
  })
  data!: DriverDueConfigResponseDto;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1704067200000,
  })
  timestamp!: number;
}

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';
import { useUpdatePrimaryStatus } from '../api/driver-product-mutations';
import { DriverProduct } from '../types/driver-product';

interface UpdatePrimaryStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  driverProduct: DriverProduct;
  vehicleId: string;
  currentPrimaryProduct?: DriverProduct;
}

export function UpdatePrimaryStatusModal({
  isOpen,
  onClose,
  driverProduct,
  vehicleId,
  currentPrimaryProduct,
}: UpdatePrimaryStatusModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const updatePrimaryMutation = useUpdatePrimaryStatus(vehicleId);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    
    try {
      await updatePrimaryMutation.mutateAsync({
        driverVehicleId: vehicleId,
        cityProductId: driverProduct.cityProductId,
        isPrimary: true,
      });
      onClose();
    } catch {
      // Error handling is managed by the mutation
    } finally {
      setIsSubmitting(false);
    }
  };

  const productName = driverProduct.cityProduct?.product?.name || 'this product';
  const currentPrimaryName = currentPrimaryProduct?.cityProduct?.product?.name;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            Make Primary Product
          </DialogTitle>
          <DialogDescription className="text-left">
            Are you sure you want to make <strong>{productName}</strong> the primary product for this vehicle?
            {currentPrimaryName && (
              <>
                <br />
                <br />
                This will replace <strong>{currentPrimaryName}</strong> as the current primary product.
              </>
            )}
            <br />
            <br />
            <span className="text-orange-600 font-medium">
              Only one product can be primary per vehicle.
            </span>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex-row justify-end gap-2 sm:gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? 'Updating...' : 'Make Primary'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
// Token verification response types
export interface VerifyTokenResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    userId: string;
    roleId: string;
    firstName: string;
    lastName: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    user: {
      id: string;
      email: string;
      phoneNumber?: string;
      createdAt: string;
      updatedAt: string;
    };
    role?: {
      id: string;
      name: string;
      description?: string;
    };
  };
  timestamp: number;
}

// Setup password payload and response types
export interface SetupPasswordPayload {
  token: string;
  password: string;
  passwordConfirmation: string;
}

export interface SetupPasswordResponse {
  success: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    isProfileUpdated: boolean;
    emailVerified: boolean;
    phoneVerified: boolean;
    isPolicyAllowed: boolean;
    phoneNumber: string | null;
    email: string | null;
    userId: string | null;
    userProfileId: string | null;
  };
  timestamp: number;
}

// Form data types
export interface InvitationFormData {
  password: string;
  passwordConfirmation: string;
}

// Token verification payload
export interface VerifyTokenPayload {
  token: string;
}
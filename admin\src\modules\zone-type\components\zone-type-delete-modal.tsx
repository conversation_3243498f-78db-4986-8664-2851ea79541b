'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface ZoneTypeDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  zoneTypeName: string;
}

export const ZoneTypeDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  zoneTypeName,
}: ZoneTypeDeleteModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className='flex items-center justify-center w-12 h-12 rounded-full bg-red-100'>
              <AlertTriangle className='w-6 h-6 text-red-600' />
            </div>
            <div>
              <DialogTitle className='text-red-900'>Delete Zone Type</DialogTitle>
              <DialogDescription className='text-red-700'>
                This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='pt-2'>
          <p className='text-sm text-gray-600'>
            Are you sure you want to delete the zone type "{zoneTypeName}"? This will permanently remove 
            the zone type from the system and cannot be undone.
          </p>
        </div>

        <div className='flex gap-3 pt-4'>
          <Button 
            type='button' 
            variant='outline' 
            onClick={onClose} 
            className='flex-1'
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            type='button' 
            variant='destructive'
            onClick={onConfirm} 
            className='flex-1'
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                Deleting...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              'Delete Zone Type'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
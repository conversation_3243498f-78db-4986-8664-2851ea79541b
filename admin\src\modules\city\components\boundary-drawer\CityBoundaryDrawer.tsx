'use client';

import { useState, useCallback } from 'react';
import { CityBoundaryDrawerContent } from './CityBoundaryDrawerContent';
import { City } from '../../types/city';

interface CityBoundaryDrawerProps {
   cityDetails: City;
}

export function CityBoundaryDrawer({ cityDetails }: CityBoundaryDrawerProps) {
   const [isFullscreen, setIsFullscreen] = useState(false);

   const handleToggleFullscreen = useCallback(() => {
      setIsFullscreen(!isFullscreen);
   }, [isFullscreen]);

   return (
      <div className={isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'relative h-full w-full'}>
         {/* Fullscreen Overlay Background */}
         {isFullscreen && <div className='fixed inset-0 bg-black/50 -z-10' />}

         <CityBoundaryDrawerContent
            cityDetails={cityDetails}
            isFullscreen={isFullscreen}
            onFullscreen={handleToggleFullscreen}
         />
      </div>
   );
}

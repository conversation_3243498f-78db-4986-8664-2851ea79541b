import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { AuthProvider } from '@shared/shared/common/constants/constants';

export class OAuthLoginDto {
  @ApiProperty({
    description: 'OAuth provider',
    enum: AuthProvider,
    example: AuthProvider.GOOGLE,
  })
  @IsNotEmpty()
  @IsEnum(AuthProvider)
  provider!: AuthProvider;

  @ApiProperty({
    description: 'OAuth access token from the provider',
    example: '********************************...',
  })
  @IsNotEmpty()
  @IsString()
  accessToken!: string;
}

'use client';

import React, { useEffect, useRef } from 'react';
import { Coordinate, DriverStatus, STATUS_COLORS } from '../types/radar';

interface StatusCircleOverlayProps {
   position: Coordinate;
   status: DriverStatus;
   map: google.maps.Map | null;
}

// Calculate radius in meters that corresponds to a pixel size on screen
// This ensures circles scale proportionally to the marker icon (28x28 px)
const getRadiusInMetersForPixels = (
   map: google.maps.Map,
   position: { lat: number; lng: number },
   pixelRadius: number
): number => {
   const scale = Math.pow(2, map.getZoom() || 11);
   const metersPerPixel = (156543.03392 * Math.cos((position.lat * Math.PI) / 180)) / scale;
   return pixelRadius * metersPerPixel;
};

// Circle opacity values
const CIRCLE_OPACITY = {
   INNER: 0.8,
   OUTER: 0.3,
} as const;

export const StatusCircleOverlay = React.memo(({ position, status, map }: StatusCircleOverlayProps) => {
   const innerCircleRef = useRef<google.maps.Circle | null>(null);
   const outerCircleRef = useRef<google.maps.Circle | null>(null);

   useEffect(() => {
      if (!map) return;

      const statusColor = STATUS_COLORS[status];

      // Calculate radius based on pixel size to match marker icon (28x28 px)
      // Inner circle: 22px radius, Outer circle: 32px radius
      const innerRadius = getRadiusInMetersForPixels(map, position, 22);
      const outerRadius = getRadiusInMetersForPixels(map, position, 32);

      // Cleanup existing circles
      if (innerCircleRef.current) {
         innerCircleRef.current.setMap(null);
         innerCircleRef.current = null;
      }
      if (outerCircleRef.current) {
         outerCircleRef.current.setMap(null);
         outerCircleRef.current = null;
      }

      // Create outer circle (glow effect)
      const outerCircle = new google.maps.Circle({
         center: { lat: position.lat, lng: position.lng },
         radius: outerRadius,
         strokeColor: statusColor,
         strokeOpacity: 0,
         strokeWeight: 0,
         fillColor: statusColor,
         fillOpacity: CIRCLE_OPACITY.OUTER,
         clickable: false,
         draggable: false,
         editable: false,
         map: map,
         zIndex: 1,
      });

      // Create inner circle (main status indicator)
      const innerCircle = new google.maps.Circle({
         center: { lat: position.lat, lng: position.lng },
         radius: innerRadius,
         strokeColor: statusColor,
         strokeOpacity: 0,
         strokeWeight: 0,
         fillColor: statusColor,
         fillOpacity: CIRCLE_OPACITY.INNER,
         clickable: false,
         draggable: false,
         editable: false,
         map: map,
         zIndex: 2,
      });

      innerCircleRef.current = innerCircle;
      outerCircleRef.current = outerCircle;

      // Listen to zoom changes and update circle radius dynamically
      const zoomListener = map.addListener('zoom_changed', () => {
         const newInnerRadius = getRadiusInMetersForPixels(map, position, 22);
         const newOuterRadius = getRadiusInMetersForPixels(map, position, 32);

         if (innerCircleRef.current) {
            innerCircleRef.current.setRadius(newInnerRadius);
         }
         if (outerCircleRef.current) {
            outerCircleRef.current.setRadius(newOuterRadius);
         }
      });

      // Cleanup on unmount or when dependencies change
      return () => {
         if (zoomListener) {
            google.maps.event.removeListener(zoomListener);
         }
         if (innerCircleRef.current) {
            innerCircleRef.current.setMap(null);
            innerCircleRef.current = null;
         }
         if (outerCircleRef.current) {
            outerCircleRef.current.setMap(null);
            outerCircleRef.current = null;
         }
      };
   }, [map, position, position.lat, position.lng, status]);

   // This component doesn't render anything directly
   return null;
});

StatusCircleOverlay.displayName = 'StatusCircleOverlay';

'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useDeleteDriver } from '../api/mutations';
import { Driver, ListDriverResponse } from '../types/driver';
import { DeleteDriverDialog } from './delete-driver-dialog';
import { DriverStatusModal } from './driver-status-modal';
import { DriverTableEmpty } from './driver-table-empty';
import { DriverTableFilteredEmpty } from './driver-table-filtered-empty';
import { DriverTableLoading } from './driver-table-loading';
import { EditDriver } from './edit-driver';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import Link from 'next/link';

// Define the columns for the table
const getColumns = ({
   deleteDriverMutation,
   handleDeleteClick,
   handleEditClick,
   handleViewClick,
   handleStatusButtonClick,
   driverToDelete,
   withPermission,
   hasPermission,
}: {
   handleDeleteClick: (id: string) => void;
   handleEditClick: (id: string) => void;
   handleViewClick: (id: string) => void;
   handleStatusButtonClick: (driver: Driver, newStatus: 'active' | 'inactive') => void;
   deleteDriverMutation: any;
   driverToDelete: string | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
   hasPermission: (permission: string) => boolean;
}): ColumnDef<Driver>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Driver</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const fullName =
            driver.firstName && driver.lastName
               ? `${driver.firstName} ${driver.lastName}`
               : driver.firstName || driver.lastName || 'No Name';
         return (
            <div className='text-left'>
               <div className='font-semibold text-sm'>{fullName}</div>
               <div className='text-xs text-gray-500'>ID: {driver.id.slice(0, 8)}...</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'contact',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Contact</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.phoneNumber}</div>
               <div className='text-xs text-gray-500'>{driver.email || '-'}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'city',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.cityName || '-'}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'onboardingStatus',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Onboarding Status</div>
      ),
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const currentStep = driver.onboarding?.currentStep || 'NOT_STARTED';

         // Format the status for display
         const formatStatus = (status: string) => {
            // Special handling for long status names
            if (status === 'VEHICLE_DOCUMENTS_VERIFICATION') {
               return 'Vehicle Docs';
            }
            return status
               .replace(/_/g, ' ')
               .toLowerCase()
               .replace(/\b\w/g, l => l.toUpperCase());
         };

         // Status color mapping
         const getStatusColor = (status: string) => {
            switch (status) {
               case 'LANGUAGE_UPDATE':
                  return 'bg-orange-100 text-orange-800';
               case 'PROFILE_SETUP':
                  return 'bg-blue-100 text-blue-800';
               case 'PROFILE_PHOTO_UPLOAD':
                  return 'bg-purple-100 text-purple-800';
               case 'KYC_DOCUMENT_UPLOAD':
                  return 'bg-yellow-100 text-yellow-800';
               case 'VEHICLE_DOCUMENTS_VERIFICATION':
                  return 'bg-indigo-100 text-indigo-800';
               case 'COMPLETED':
                  return 'bg-green-100 text-green-800';
               default:
                  return 'bg-gray-100 text-gray-800';
            }
         };

         return (
            <div className='text-left'>
               <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                     currentStep
                  )}`}
               >
                  {formatStatus(currentStep)}
               </span>
            </div>
         );
      },
      size: 180,
   },
   {
      accessorKey: 'status',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm whitespace-nowrap'>
            Account Status
         </div>
      ),
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const status = driver.status || 'pending';

         // Format the status for display
         const formatStatus = (status: string) => {
            return status.charAt(0).toUpperCase() + status.slice(1);
         };

         // Status color mapping
         const getStatusColor = (status: string) => {
            switch (status.toLowerCase()) {
               case 'active':
                  return 'bg-green-100 text-green-800';
               case 'pending':
                  return 'bg-yellow-100 text-yellow-800';
               case 'suspended':
                  return 'bg-red-100 text-red-800';
               case 'inactive':
                  return 'bg-gray-100 text-gray-800';
               default:
                  return 'bg-gray-100 text-gray-800';
            }
         };

         return (
            <div className='text-left'>
               <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                     status
                  )}`}
               >
                  {formatStatus(status)}
               </span>
            </div>
         );
      },
      size: 120,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='flex justify-center gap-1'>
               {hasPermission(RBAC_PERMISSIONS.DRIVER.MANAGE) ? (
                  <Link
                     href={`/dashboard/drivers/${driver.id}?returnPage=${1}`}
                     className='text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  >
                     Manage
                  </Link>
               ) : (
                  <button
                     className='text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                     onClick={() =>
                        withPermission(RBAC_PERMISSIONS.DRIVER.MANAGE, () =>
                           handleViewClick(driver.id)
                        )
                     }
                  >
                     Manage
                  </button>
               )}

               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.DRIVER.EDIT, () => handleEditClick(driver.id));
                  }}
               >
                  Edit
               </button>
               <button
                  className={`text-sm cursor-pointer font-medium px-3 py-1 rounded-md transition-colors bg-white ${
                     driver.status === 'active'
                        ? 'border  bg-white text-red-600 hover:text-red-700 border-red-400'
                        : 'border border-green-400 bg-white text-green-600 hover:text-green-700 hover:border-green-500'
                  }`}
                  onClick={() =>
                     withPermission(RBAC_PERMISSIONS.DRIVER.STATUS_UPDATE, () =>
                        handleStatusButtonClick(
                           driver,
                           driver.status === 'active' ? 'inactive' : 'active'
                        )
                     )
                  }
               >
                  {driver.status === 'active' ? 'Deactivate' : 'Activate'}
               </button>
               <button
                  className='hidden ktext-sm font-medium text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white'
                  onClick={() => handleDeleteClick(driver.id)}
                  disabled={deleteDriverMutation.isPending && driverToDelete === driver.id}
               >
                  Delete
               </button>
            </div>
         );
      },
      size: 200,
   },
];

interface DriverTableProps {
   data: ListDriverResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   hasSearch: boolean;
   hasStatus: boolean;
   hasLocation: boolean;
   onClearFilters: () => void;
}

export function DriverTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   hasSearch,
   hasStatus,
   hasLocation,
   onClearFilters,
}: DriverTableProps) {
   const [driverToDelete, setDriverToDelete] = useState<string | null>(null);
   const [driverToEdit, setDriverToEdit] = useState<string | null>(null);
   const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
   const [pendingStatus, setPendingStatus] = useState<'active' | 'inactive'>('active');
   const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
   const deleteDriverMutation = useDeleteDriver();
   const queryClient = useQueryClient();
   const router = useRouter();
   const { withPermission, hasPermission } = useRoleBasedAccess();

   const handleDeleteClick = (id: string) => {
      setDriverToDelete(id);
   };

   const handleEditClick = (id: string) => {
      setDriverToEdit(id);
   };

   const handleViewClick = (id: string) => {
      router.push(`/dashboard/drivers/${id}?returnPage=${currentPage}`);
   };

   const handleStatusButtonClick = (driver: Driver, newStatus: 'active' | 'inactive') => {
      setSelectedDriver(driver);
      setPendingStatus(newStatus);
      setIsStatusModalOpen(true);
   };

   const handleStatusUpdated = () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] });
   };

   const handleDeleteConfirm = () => {
      if (!driverToDelete) return;

      deleteDriverMutation.mutate(driverToDelete, {
         onSuccess: () => {
            toast.success('Driver deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['drivers'] });
         },
         onSettled: () => {
            setDriverToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteDriverMutation,
      handleDeleteClick,
      handleEditClick,
      handleViewClick,
      handleStatusButtonClick,
      driverToDelete,
      withPermission,
      hasPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   // Loading state
   if (isLoading) {
      return <DriverTableLoading />;
   }

   // Empty state with filters
   if (!data?.data?.length && hasFilters) {
      return (
         <DriverTableFilteredEmpty
            hasSearch={hasSearch}
            hasStatus={hasStatus}
            hasLocation={hasLocation}
            onClearFilters={onClearFilters}
         />
      );
   }

   // Empty state without filters
   if (!data?.data?.length) {
      return <DriverTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {/* Pagination */}
         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         {/* Delete Confirmation Dialog */}
         <DeleteDriverDialog
            isOpen={!!driverToDelete}
            onClose={() => setDriverToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteDriverMutation.isPending}
         />

         {/* Edit Driver Sheet */}
         <EditDriver
            driverId={driverToEdit}
            isOpen={!!driverToEdit}
            onClose={() => setDriverToEdit(null)}
         />

         {/* Driver Status Modal */}
         {selectedDriver && (
            <DriverStatusModal
               isOpen={isStatusModalOpen}
               onClose={() => setIsStatusModalOpen(false)}
               driverId={selectedDriver.id}
               driverName={
                  `${selectedDriver.firstName || ''} ${selectedDriver.lastName || ''}`.trim() ||
                  'N/A'
               }
               currentStatus={selectedDriver.status}
               newStatus={pendingStatus}
               onStatusUpdated={handleStatusUpdated}
            />
         )}
      </div>
   );
}

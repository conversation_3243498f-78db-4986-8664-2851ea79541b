'use client';

import { ChevronDown, User, LogOut } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuLabel,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useGetMe } from '@/api/get-me';
import { useRouter } from 'next/navigation';
import { resetStore } from '@/store/store-helpers';
import { useQueryClient } from '@tanstack/react-query';
// import { useLogout } from '@/api/logout';

export function UserProfileDropdown() {
   const { data: meData, isLoading } = useGetMe();
   const queryClient = useQueryClient();
   // const logout = useLogout();
   const router = useRouter();

   const handleLogout = () => {
      // logout.mutate();
      resetStore();
      router.push('/');

      setTimeout(() => {
         queryClient.resetQueries();
      }, 1000);
   };

   const handleProfileClick = () => {
      router.push('/dashboard/profile');
   };

   // Get user display data
   const userData = meData?.data;
   const displayName =
      userData?.userProfile?.firstName && userData?.userProfile?.lastName
         ? `${userData.userProfile.firstName} ${userData.userProfile.lastName}`
         : userData?.email?.split('@')[0] || 'User';
   const displayEmail = userData?.email || '';
   const avatarUrl = userData?.userProfile?.profilePictureUrl;

   // Generate initials for avatar fallback
   const getInitials = (name: string) => {
      return name
         .split(' ')
         .map(n => n[0])
         .join('')
         .toUpperCase()
         .slice(0, 2);
   };

   if (isLoading) {
      return (
         <Button variant='outline' className='flex items-center gap-2 px-2 py-1 h-8 rounded-md'>
            <Avatar className='h-6 w-6'>
               <AvatarFallback className='bg-gray-200 text-xs'>...</AvatarFallback>
            </Avatar>
            <div className='hidden md:flex flex-col items-start'>
               <span className='text-xs font-medium text-gray-900'>Loading...</span>
            </div>
            <ChevronDown className='h-3 w-3 text-gray-500' />
         </Button>
      );
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <Button
               variant='outline'
               className='flex items-center gap-2 px-2 py-1 h-8 rounded-md border-gray-200 cursor-pointer'
            >
               <Avatar className='h-6 w-6'>
                  <AvatarImage src={avatarUrl || undefined} alt={displayName} />
                  <AvatarFallback className='bg-gray-200 text-xs'>
                     {getInitials(displayName)}
                  </AvatarFallback>
               </Avatar>
               <div className='hidden md:flex flex-col items-start'>
                  <span className='text-xs font-medium text-gray-900'>{displayName}</span>
               </div>
               <ChevronDown className='h-3 w-3 text-gray-500' />
            </Button>
         </DropdownMenuTrigger>

         <DropdownMenuContent className='w-64 rounded-lg' align='end' sideOffset={8}>
            <DropdownMenuLabel className='p-0 font-normal'>
               <div className='flex items-center gap-2 px-3 py-2 text-left text-sm'>
                  <Avatar className='h-8 w-8'>
                     <AvatarImage src={avatarUrl || undefined} alt={displayName} />
                     <AvatarFallback className='bg-gray-200'>
                        {getInitials(displayName)}
                     </AvatarFallback>
                  </Avatar>
                  <div className='grid flex-1 text-left text-sm leading-tight'>
                     <span className='truncate font-medium text-gray-900'>{displayName}</span>
                     <span className='truncate text-xs text-gray-500'>{displayEmail}</span>
                  </div>
               </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
               <DropdownMenuItem onClick={handleProfileClick} className='cursor-pointer'>
                  <User className='h-4 w-4' />
                  <span>Profile</span>
               </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={handleLogout} className='cursor-pointer'>
               <LogOut className='h-4 w-4 text-red-500' />
               <span className='text-red-500'>Log out</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}

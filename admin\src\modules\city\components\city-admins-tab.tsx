'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListCityAdmins } from '../api/city-admin-queries';
import { CityAdmin } from '../types/city-admin';
import { AddCityAdminsModal } from './add-city-admins-modal';
import { CityAdminFilters } from './city-admin-filters';
import { CityAdminTable } from './city-admin-table';
import { RemoveCityAdminModal } from './remove-city-admin-modal';
import { InviteAdminSheet } from '../../admin/components/invite-admin-sheet';

interface CityAdminsTabProps {
   cityId: string;
}

export function CityAdminsTab({ cityId }: CityAdminsTabProps) {
   const [page, setPage] = useState(1);
   const [limit] = useState(100);
   const [search, setSearch] = useState('');
   const [status, setStatus] = useState<string | undefined>(undefined);

   // Modal states
   const [removeModalOpen, setRemoveModalOpen] = useState(false);
   const [selectedCityAdmin, setSelectedCityAdmin] = useState<CityAdmin | null>(null);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setStatus(value);
      setPage(1);
   };

   const cityAdminsQuery = useListCityAdmins(cityId, {
      page,
      limit,
      search: search || undefined,
      status: status || undefined,
   });

   // Handle modal actions
   const handleRemoveClick = (cityAdmin: CityAdmin) => {
      setSelectedCityAdmin(cityAdmin);
      setRemoveModalOpen(true);
   };

   // Check if any filters are active
   const hasFilters = !!search || !!status;

   return (
      <div className='space-y-4'>
         {/* Header with Add Button */}
         <div className='flex justify-between items-center'>
            <div>
               <h3 className='text-lg font-semibold text-gray-900'>City Admins</h3>
               <p className='text-sm text-gray-600'>Manage administrators assigned to this city</p>
            </div>
            <div className='flex items-center gap-4'>
               {/* Admin Info */}
               <div className='flex items-center gap-2'>
                  <AddCityAdminsModal cityId={cityId} />
                  <InviteAdminSheet label='Invite Admins' selectedCity={cityId} />
               </div>
            </div>
         </div>

         {/* Table Card */}
         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <CityAdminFilters
               search={search}
               status={status}
               onSearchChange={handleSearchChange}
               onStatusChange={handleStatusChange}
               isLoading={cityAdminsQuery.isFetching && !cityAdminsQuery.isLoading}
            />

            <CityAdminTable
               data={cityAdminsQuery.data ?? undefined}
               isLoading={cityAdminsQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={hasFilters}
               onRemoveClick={handleRemoveClick}
            />
         </Card>

         {/* Modals */}
         <RemoveCityAdminModal
            open={removeModalOpen}
            onOpenChange={setRemoveModalOpen}
            cityAdmin={selectedCityAdmin}
            cityId={cityId}
         />
      </div>
   );
}

import { ApiProperty } from '@nestjs/swagger';

export class VehicleDocumentInfoDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440001' })
  id!: string;

  @ApiProperty({ example: 'Registration Certificate' })
  name!: string;

  @ApiProperty({ example: 'Vehicle registration certificate', required: false })
  description?: string;

  @ApiProperty({ example: true })
  isMandatory!: boolean;
}

export class DriverInfoDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440002' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;
}

export class CreatedByUserDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440006' })
  id!: string;

  @ApiProperty({ example: 'Admin', required: false, nullable: true })
  firstName?: string | null;

  @ApiProperty({ example: 'User', required: false, nullable: true })
  lastName?: string | null;
}

export class VehicleTypeInfoDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440003' })
  id!: string;

  @ApiProperty({ example: 'Car' })
  name!: string;
}

export class DriverVehicleInfoDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440004' })
  id!: string;

  @ApiProperty({ example: 'KL-01-AB-1234' })
  vehicleNumber!: string;

  @ApiProperty({ type: DriverInfoDto })
  userProfile!: DriverInfoDto;

  @ApiProperty({ type: VehicleTypeInfoDto })
  vehicleType!: VehicleTypeInfoDto;
}

export class DriverVehicleDocumentResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440005' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440004' })
  driverVehicleId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440001' })
  vehicleDocumentId!: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440006',
    required: false,
    nullable: true,
    description: 'ID of the user who created this document',
  })
  createdBy?: string | null;

  @ApiProperty({
    example: 'https://signed-url.amazonaws.com/documents/rc.jpg',
    required: false,
    description: 'Signed URL for the document (valid for 1 hour)',
  })
  documentUrl?: string;

  @ApiProperty({
    example: 'PENDING',
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
  })
  status!: string;

  @ApiProperty({
    example: 'Document quality is poor',
    required: false,
    description: 'Rejection note if document was rejected',
  })
  rejectionNote?: string;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  updatedAt!: Date;

  @ApiProperty({ type: VehicleDocumentInfoDto })
  vehicleDocument!: VehicleDocumentInfoDto;

  @ApiProperty({ type: DriverVehicleInfoDto, required: false })
  driverVehicle?: DriverVehicleInfoDto;

  @ApiProperty({
    type: CreatedByUserDto,
    required: false,
    description: 'Information about the user who created this document',
  })
  createdByUser?: CreatedByUserDto;
}

export class DriverVehicleDocumentListResponseDto {
  @ApiProperty({ type: [DriverVehicleDocumentResponseDto] })
  data!: DriverVehicleDocumentResponseDto[];

  @ApiProperty({ example: 1 })
  page!: number;

  @ApiProperty({ example: 10 })
  limit!: number;

  @ApiProperty({ example: 25 })
  total!: number;

  @ApiProperty({ example: 3 })
  totalPages!: number;

  @ApiProperty({ example: true })
  hasNextPage!: boolean;

  @ApiProperty({ example: false })
  hasPrevPage!: boolean;
}

export class DocumentActionResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440005' })
  id!: string;

  @ApiProperty({ example: 'APPROVED', enum: ['APPROVED', 'REJECTED'] })
  status!: string;

  @ApiProperty({ example: 'Document approved successfully' })
  message!: string;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  updatedAt!: Date;
}

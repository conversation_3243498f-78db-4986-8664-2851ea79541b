'use client';

import { useRef } from 'react';
import { Autocomplete } from '@react-google-maps/api';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Search } from 'lucide-react';

interface CitySearchProps {
   onCitySelect: (place: google.maps.places.PlaceResult) => void;
}

export function CitySearch({ onCitySelect }: CitySearchProps) {
   const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

   const onLoad = (autocomplete: google.maps.places.Autocomplete) => {
      autocompleteRef.current = autocomplete;

      // Configure to search for cities only
      autocomplete.setOptions({
         types: ['(cities)'],
         fields: ['name', 'geometry', 'place_id', 'formatted_address'],
      });
   };

   const onPlaceChanged = () => {
      if (autocompleteRef.current) {
         const place = autocompleteRef.current.getPlace();

         if (place.geometry) {
            onCitySelect(place);
         }
      }
   };

   return (
      <Card className='px-4 py-1'>
         <div className='flex items-center gap-2 h-8'>
            <Search className='h-4 w-4 text-gray-400' />
            <Autocomplete className='border-none' onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
               <Input
                  type='text'
                  placeholder='Search for a city...'
                  className='w-64 text-sm border-none shadow-none focus-visible:ring-0 p-0 h-full'
               />
            </Autocomplete>
         </div>
      </Card>
   );
}

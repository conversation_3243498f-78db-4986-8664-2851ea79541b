import { useMutation } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type { LoginPayload, LoginResponse } from '../types/login';
import type {
   ForgotPasswordPayload,
   ForgotPasswordResponse,
   VerifyForgotPasswordOtpPayload,
   VerifyForgotPasswordOtpResponse,
   ResetPasswordPayload,
   ResetPasswordResponse,
} from '../types/auth';
import type {
   VerifyTokenPayload,
   VerifyTokenResponse,
   SetupPasswordPayload,
   SetupPasswordResponse,
} from '../types/invitation';

export const useLogin = () => {
   return useMutation({
      mutationFn: (data: LoginPayload): Promise<LoginResponse> =>
         apiClient.post('/auth/password/login', data),
   });
};

export const useForgotPassword = () => {
   return useMutation({
      mutationFn: (data: ForgotPasswordPayload): Promise<ForgotPasswordResponse> =>
         apiClient.post('/auth/forgot-password', data),
   });
};

export const useVerifyForgotPasswordOtp = () => {
   return useMutation({
      mutationFn: (
         data: VerifyForgotPasswordOtpPayload
      ): Promise<VerifyForgotPasswordOtpResponse> =>
         apiClient.post('/auth/verify-forgot-password-otp', data),
   });
};

export const useResetPassword = () => {
   return useMutation({
      mutationFn: (data: ResetPasswordPayload): Promise<ResetPasswordResponse> =>
         apiClient.post('/auth/reset-password', data),
   });
};

export const useVerifyInvitationToken = () => {
   return useMutation({
      mutationFn: (data: VerifyTokenPayload): Promise<VerifyTokenResponse> =>
         apiClient.get(`/admin/sub-admin/profile/token?token=${data.token}`),
   });
};

export const useSetupPassword = () => {
   return useMutation({
      mutationFn: (data: SetupPasswordPayload): Promise<SetupPasswordResponse> =>
         apiClient.post('/admin/sub-admin/setup-password', data),
   });
};

import { BaseEntity } from '../base.repository';

export interface DriverEarnings extends BaseEntity {
  driverId: string;
  earningsDate: Date;
  totalFareAmount: any;
  completedRides: number;
  totalTaxesOnCharge?: any;
  totalCommission?: any;
  totalTaxOnCommission?: any;
}

export interface DriverEarningsAggregation {
  driverId: string;
  totalEarnings: number;
  totalRides: number;
  averageEarningsPerRide: number;
  dateRange: {
    from: Date;
    to: Date;
  };
}

export interface DailyEarnings {
  date: Date;
  totalFareAmount: number;
  completedRides: number;
  averageEarningsPerRide: number;
}

'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteCommission } from '../api/mutations';
import { ListCommissionsResponse, Commission } from '../types/commissions';
import { CommissionsModal } from './commissions-modal';
import { CommissionsTableEmpty } from './commissions-table-empty';
import { CommissionsTableLoading } from './commissions-table-loading';
import { CommissionsDeleteModal } from './commissions-delete-modal';
import {
  USE_ROLE_BASED_ACCESS,
  useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

// ============================================
// COLUMN DEFINITIONS
// ============================================
const getColumns = ({
  deleteCommissionMutation,
  handleEditClick,
  handleDeleteClick,
  itemToDelete,
  withPermission,
}: {
  handleEditClick: (id: string) => void;
  handleDeleteClick: (item: Commission) => void;
  deleteCommissionMutation: any;
  itemToDelete: Commission | null;
  withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Commission>[] => [
  {
    accessorKey: 'name',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm font-medium break-words'>{item.name}</div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'description',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      return (
        <div className='text-left max-w-[250px]'>
          <div className='text-sm text-gray-600 break-words'>
            {item.description || 'No description'}
          </div>
        </div>
      );
    },
    size: 250,
  },
  {
    accessorKey: 'type',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Type</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      return (
        <div className='flex justify-center'>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              item.type === 'percentage'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-purple-100 text-purple-800'
            }`}
          >
            {item.type === 'percentage' ? 'Percentage' : 'Flat'}
          </span>
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'value',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Value</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      const value =
        item.type === 'percentage'
          ? `${parseFloat(item.percentageValue || '0').toFixed(2)}%`
          : `$${parseFloat(item.flatValue || '0').toFixed(2)}`;
      return (
        <div className='text-center'>
          <div className='text-sm font-medium'>{value}</div>
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'taxGroup',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Tax Group</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      return (
        <div className='text-left max-w-[150px]'>
          <div className='text-sm text-gray-600 break-words'>
            {item.taxGroup
              ? `${item.taxGroup.name} (${parseFloat(item.taxGroup.totalPercentage).toFixed(2)}%)`
              : 'No tax group'}
          </div>
        </div>
      );
    },
    size: 150,
  },
  {
    accessorKey: 'createdAt',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Created Date</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      const date = new Date(item.createdAt);
      return (
        <div className='text-left'>
          <div className='text-sm text-gray-600'>
            {date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
          </div>
        </div>
      );
    },
    size: 120,
  },
  {
    id: 'actions',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
    cell: ({ row }) => {
      const item = row.original as Commission;
      const isDeleting = itemToDelete?.id === item.id && deleteCommissionMutation.isPending;

      return (
        <div className='flex justify-center gap-1'>
          <button
            className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.COMMISSION.EDIT, () => handleEditClick(item.id));
            }}
            disabled={isDeleting}
          >
            Edit
          </button>
          <button
            className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.COMMISSION.DELETE, () => handleDeleteClick(item));
            }}
            disabled={isDeleting}
          >
            {isDeleting ? '...' : 'Delete'}
          </button>
        </div>
      );
    },
    size: 150,
  },
];

// ============================================
// TABLE COMPONENT
// ============================================
interface CommissionsTableProps {
  data: ListCommissionsResponse | undefined;
  isLoading: boolean;
  currentPage: number;
  onPageChange: (page: number) => void;
  hasFilters?: boolean;
  hasSearch?: boolean;
  onClearFilters?: () => void;
}

export function CommissionsTable({
  data,
  isLoading,
  currentPage,
  onPageChange,
  hasFilters: _hasFilters,
  hasSearch: _hasSearch,
  onClearFilters: _onClearFilters,
}: CommissionsTableProps) {
  const [itemToEdit, setItemToEdit] = useState<string | null>(null);
  const [itemToDelete, setItemToDelete] = useState<Commission | null>(null);
  const deleteMutation = useDeleteCommission();
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  const handleEditClick = (id: string) => {
    setItemToEdit(id);
  };

  const handleDeleteClick = (item: Commission) => {
    setItemToDelete(item);
  };

  const handleDeleteConfirm = () => {
    if (!itemToDelete) return;

    deleteMutation.mutate(itemToDelete.id, {
      onSuccess: () => {
        toast.success('Commission deleted successfully');
        queryClient.invalidateQueries({ queryKey: ['commissions'] });
      },
      onSettled: () => {
        setItemToDelete(null);
      },
    });
  };

  const columns = getColumns({
    deleteCommissionMutation: deleteMutation,
    handleEditClick,
    handleDeleteClick,
    itemToDelete,
    withPermission,
  });

  const table = useReactTable({
    data: data?.data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <CommissionsTableLoading />;
  }

  if (!data?.data?.data?.length) {
    return <CommissionsTableEmpty />;
  }

  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full table-fixed'>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id} className='border-b bg-gray-50'>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className='h-11 px-4 text-left align-middle'
                      style={{ width: header.getSize(), maxWidth: header.getSize() }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map(row => (
                <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                  {row.getVisibleCells().map(cell => (
                    <td
                      key={cell.id}
                      className='px-4 py-3 align-middle'
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {data && data.data.meta && data.data.meta.totalPages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.data.meta.totalPages}
          onPageChange={onPageChange}
          hasNext={data.data.meta.hasNextPage}
          hasPrev={data.data.meta.hasPrevPage}
        />
      )}

      {/* Delete Confirmation Modal */}
      <CommissionsDeleteModal
        isOpen={!!itemToDelete}
        onClose={() => setItemToDelete(null)}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteMutation.isPending}
        itemName={itemToDelete?.name || ''}
      />

      {/* Edit Modal */}
      <CommissionsModal
        mode='edit'
        itemId={itemToEdit}
        isOpen={!!itemToEdit}
        onClose={() => setItemToEdit(null)}
      />
    </div>
  );
}

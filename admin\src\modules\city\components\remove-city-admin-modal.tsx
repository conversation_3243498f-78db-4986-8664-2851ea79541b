'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { Al<PERSON>Triangle, User } from 'lucide-react';
import { useRemoveAdminFromCity } from '../api/city-admin-mutations';
import { CityAdmin } from '../types/city-admin';

interface RemoveCityAdminModalProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   cityAdmin: CityAdmin | null;
   cityId: string;
}

export function RemoveCityAdminModal({
   open,
   onOpenChange,
   cityAdmin,
   cityId,
}: RemoveCityAdminModalProps) {
   const removeAdminMutation = useRemoveAdminFromCity(cityId);

   const handleRemove = () => {
      if (!cityAdmin) return;

      removeAdminMutation.mutate(
         //  { userProfileId: cityAdmin.admin.user.id },
         { userProfileId: cityAdmin.admin.id },
         {
            onSuccess: () => {
               onOpenChange(false);
            },
         }
      );
   };

   const isSubmitting = removeAdminMutation.isPending;

   if (!cityAdmin) return null;

   const user = cityAdmin.admin.user;
   const admin = cityAdmin.admin;
   const fullName =
      admin?.firstName && admin?.lastName ? `${admin.firstName} ${admin.lastName}` : null;
   const displayName = fullName || user?.email || 'Unknown Admin';

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='sm:max-w-[400px]'>
            <DialogHeader>
               <div className='flex items-center gap-3'>
                  <div className='w-12 h-12 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0'>
                     <AlertTriangle className='w-6 h-6 text-red-600' />
                  </div>
                  <div>
                     <DialogTitle className='text-lg'>Remove Admin</DialogTitle>
                     <DialogDescription className='text-sm text-gray-600'>
                        This action cannot be undone.
                     </DialogDescription>
                  </div>
               </div>
            </DialogHeader>

            <div className='py-4'>
               <div className='bg-gray-50 rounded-lg p-4'>
                  <div className='flex items-center gap-3'>
                     <div className='w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0 relative overflow-hidden'>
                        <User className='w-5 h-5 text-gray-400' />
                     </div>
                     <div className='min-w-0 flex-1'>
                        <div className='font-medium text-sm text-gray-900 truncate'>
                           {displayName}
                        </div>
                        {fullName && user?.email && (
                           <div className='text-xs text-gray-500 truncate'>{user.email}</div>
                        )}
                     </div>
                  </div>
               </div>

               <p className='text-sm text-gray-600 mt-4'>
                  Are you sure you want to remove <strong>{displayName}</strong> from this city?
                  They will lose administrative access to this city's operations.
               </p>
            </div>

            <DialogFooter>
               <Button
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
               >
                  Cancel
               </Button>
               <Button
                  variant='destructive'
                  onClick={handleRemove}
                  disabled={isSubmitting}
                  className='min-w-[100px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Removing...
                     </div>
                  ) : (
                     'Remove Admin'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

export const polygonOptions: google.maps.PolygonOptions = {
  fillColor: '#FF6B6B',
  fillOpacity: 0.35,
  strokeColor: '#E55555',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  clickable: true,
  editable: false,
};

export const editablePolygonOptions: google.maps.PolygonOptions = {
  ...polygonOptions,
  editable: true,
  draggable: false,
};

export const otherCityPolygonOptions: google.maps.PolygonOptions = {
  fillColor: '#306aff',
  fillOpacity: 0.2,
  strokeColor: '#306aff',
  strokeOpacity: 0.66,
  strokeWeight: 1.7,
  clickable: false,
  editable: false,
};

export const zonePolygonOptions: google.maps.PolygonOptions = {
  fillColor: '#10B981', // Green color for zones
  fillOpacity: 0.35,
  strokeColor: '#059669',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  clickable: true,
  editable: false,
};

export const editableZonePolygonOptions: google.maps.PolygonOptions = {
  ...zonePolygonOptions,
  editable: true,
  draggable: false,
};

export const cityBoundaryPolygonOptions: google.maps.PolygonOptions = {
  fillColor: '#FF6B6B',
  fillOpacity: 0.08, // Extremely transparent red background
  strokeColor: '#E55555',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  clickable: false,
  editable: false,
};

export const otherZonePolygonOptions: google.maps.PolygonOptions = {
  fillColor: '#8B5CF6', // Purple color for other zones
  fillOpacity: 0.25,
  strokeColor: '#8B5CF6',
  strokeOpacity: 0.7,
  strokeWeight: 1.5,
  clickable: false,
  editable: false,
};

export const zoneInCityModePolygonOptions: google.maps.PolygonOptions = {
  fillColor: '#10B981', // Green color for zones
  fillOpacity: 0.15, // More dimmed than normal zones
  strokeColor: '#059669',
  strokeOpacity: 0.6, // More transparent
  strokeWeight: 1.5,
  clickable: false,
  editable: false,
};
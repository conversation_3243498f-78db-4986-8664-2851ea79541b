import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ReportsRepository, ReportFilters } from './reports.repository';
import { CSVGenerator } from './utils/csv-generator';
import { GenerateReportDto } from './dto/generate-report.dto';

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(private readonly reportsRepository: ReportsRepository) {}

  /**
   * Generate CSV report for rides
   */
  async generateRidesReport(filters: GenerateReportDto) {
    this.logger.log('Generating rides report with filters:', filters);

    // Parse and validate dates
    const reportFilters = this.parseFilters(filters);

    // Validate date range
    if (reportFilters.fromDate && reportFilters.toDate) {
      if (reportFilters.fromDate > reportFilters.toDate) {
        throw new BadRequestException('fromDate cannot be after toDate');
      }
    }

    // Fetch rides from database
    const rides = await this.reportsRepository.getRidesForReport(reportFilters);

    this.logger.log(`Found ${rides.length} rides for report`);

    // Generate CSV content
    const csvContent = CSVGenerator.generateCSV(rides);

    // Generate filename
    const fileName = CSVGenerator.generateFileName('rides-report');

    return {
      fileName,
      content: csvContent,
      rowCount: rides.length,
    };
  }

  /**
   * Get report metadata (available filters, counts, etc.)
   */
  async getReportMetadata() {
    this.logger.log('Fetching report metadata');

    const [cities, cityProducts, totalRides] = await Promise.all([
      this.reportsRepository.getAvailableCities(),
      this.reportsRepository.getAvailableCityProducts(),
      this.reportsRepository.getRidesCountForReport({}),
    ]);

    return {
      cities,
      cityProducts,
      totalRides,
      availableStatuses: ['completed', 'cancelled'],
    };
  }

  /**
   * Parse and validate filter parameters
   */
  private parseFilters(filters: GenerateReportDto): ReportFilters {
    const reportFilters: ReportFilters = {};

    if (filters.fromDate) {
      reportFilters.fromDate = new Date(filters.fromDate);
      // Set to start of day
      reportFilters.fromDate.setHours(0, 0, 0, 0);
    }

    if (filters.toDate) {
      reportFilters.toDate = new Date(filters.toDate);
      // Set to end of day
      reportFilters.toDate.setHours(23, 59, 59, 999);
    }

    if (filters.cityProductId) {
      reportFilters.cityProductId = filters.cityProductId;
    }

    if (filters.status) {
      reportFilters.status = filters.status;
    }

    return reportFilters;
  }
}

import {
  CSVColumn,
  CSV_COLUMNS,
  getDynamicChargeColumns,
  getDynamicCommissionColumns,
} from './csv-columns';

/**
 * CSV Generator utility for creating CSV reports from ride data
 */
export class CSVGenerator {
  /**
   * Escape CSV field values to handle commas, quotes, and newlines
   */
  private static escapeCSVField(field: any): string {
    if (field === null || field === undefined) {
      return '';
    }

    const stringValue = String(field);

    // If field contains comma, quote, or newline, wrap in quotes and escape quotes
    if (
      stringValue.includes(',') ||
      stringValue.includes('"') ||
      stringValue.includes('\n')
    ) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  }

  /**
   * Generate CSV content from rides data
   */
  static generateCSV(rides: any[]): string {
    if (rides.length === 0) {
      return this.generateHeaders([]);
    }

    // Get all unique columns from all rides (to handle dynamic charges/commissions)
    const allColumns = this.getAllColumns(rides);

    // Generate header row
    const headerRow = allColumns
      .map((col) => this.escapeCSVField(col.name))
      .join(',');

    // Generate data rows
    const dataRows = rides.map((ride) => {
      return allColumns
        .map((col) => {
          const value = col.extractor(ride);
          return this.escapeCSVField(value);
        })
        .join(',');
    });

    return [headerRow, ...dataRows].join('\n');
  }

  /**
   * Get all columns including dynamic ones from rides
   * Handles cases where different rides have different numbers of charges/commissions
   */
  private static getAllColumns(rides: any[]): CSVColumn[] {
    const columns = [...CSV_COLUMNS];
    const dynamicChargeColumnsMap = new Map<string, CSVColumn>();
    const dynamicCommissionColumnsMap = new Map<string, CSVColumn>();

    // Collect all unique dynamic columns from all rides
    rides.forEach((ride) => {
      if (ride.fareSpec) {
        const chargeColumns = getDynamicChargeColumns(ride.fareSpec);
        const commissionColumns = getDynamicCommissionColumns(ride.fareSpec);

        // Store charge columns with their extractors
        chargeColumns.forEach((col) => {
          if (!dynamicChargeColumnsMap.has(col.name)) {
            dynamicChargeColumnsMap.set(col.name, col);
          }
        });

        // Store commission columns with their extractors
        commissionColumns.forEach((col) => {
          if (!dynamicCommissionColumnsMap.has(col.name)) {
            dynamicCommissionColumnsMap.set(col.name, col);
          }
        });
      }
    });

    // Create extractors that handle missing charges/commissions by returning 0
    const chargeColumnsArray = Array.from(dynamicChargeColumnsMap.values()).map(
      (col) => ({
        name: col.name,
        description: col.description,
        extractor: (ride: any) => {
          const chargeColumns = getDynamicChargeColumns(ride.fareSpec || {});
          const matchingCol = chargeColumns.find((c) => c.name === col.name);
          // Return 0 if this charge doesn't exist for this ride
          return matchingCol ? matchingCol.extractor(ride) : 0;
        },
      }),
    );

    const commissionColumnsArray = Array.from(
      dynamicCommissionColumnsMap.values(),
    ).map((col) => ({
      name: col.name,
      description: col.description,
      extractor: (ride: any) => {
        const commissionColumns = getDynamicCommissionColumns(
          ride.fareSpec || {},
        );
        const matchingCol = commissionColumns.find((c) => c.name === col.name);
        // Return 0 if this commission doesn't exist for this ride
        return matchingCol ? matchingCol.extractor(ride) : 0;
      },
    }));

    return [...columns, ...chargeColumnsArray, ...commissionColumnsArray];
  }

  /**
   * Generate headers only (for empty results)
   */
  private static generateHeaders(columns: CSVColumn[]): string {
    const allColumns = columns.length > 0 ? columns : CSV_COLUMNS;
    return allColumns.map((col) => this.escapeCSVField(col.name)).join(',');
  }

  /**
   * Convert CSV string to Buffer
   */
  static csvToBuffer(csvContent: string): Buffer {
    return Buffer.from(csvContent, 'utf-8');
  }

  /**
   * Generate filename with timestamp
   */
  static generateFileName(prefix: string = 'rides-report'): string {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, '-')
      .split('T')[0];
    return `${prefix}-${timestamp}.csv`;
  }
}

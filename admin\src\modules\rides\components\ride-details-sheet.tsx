'use client';

import {
   She<PERSON>,
   <PERSON><PERSON><PERSON>onte<PERSON>,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Phone, Mail } from 'lucide-react';
import {
   Timeline,
   TimelineDate,
   TimelineHeader,
   TimelineIndicator,
   TimelineItem,
   TimelineSeparator,
   TimelineTitle,
} from '@/components/ui/timeline';
import { useGetRideDetails } from '../api/queries';
import { RideDetails } from '../types/ride';
import { format } from 'date-fns';
import { RideRouteMap } from './ride-route-map';

interface RideDetailsSheetProps {
   rideId: string | null;
   isOpen: boolean;
   onClose: () => void;
}

function formatStatus(status: string): string {
   return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
}

function getStatusBadgeClass(status: string): string {
   const normalizedStatus = status.toLowerCase();

   if (normalizedStatus.includes('completed')) {
      return 'bg-green-50 text-green-700 border-green-200';
   }
   if (normalizedStatus.includes('cancelled')) {
      return 'bg-red-50 text-red-700 border-red-200';
   }
   if (normalizedStatus.includes('progress') || normalizedStatus.includes('accepted')) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
   }
   if (normalizedStatus.includes('requested') || normalizedStatus.includes('processing')) {
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
   }
   return 'bg-gray-50 text-gray-700 border-gray-200';
}

function formatDuration(seconds: number | null | undefined): string {
   if (!seconds) return 'N/A';
   const hours = Math.floor(seconds / 3600);
   const minutes = Math.floor((seconds % 3600) / 60);
   if (hours > 0) {
      return `${hours}h ${minutes}m`;
   }
   return `${minutes}m`;
}

function formatDistance(meters: number | null | undefined): string {
   if (!meters) return 'N/A';
   const km = meters / 1000;
   if (km >= 1) {
      return `${km.toFixed(2)} km`;
   }
   return `${meters} m`;
}

export function RideDetailsSheet({ rideId, isOpen, onClose }: RideDetailsSheetProps) {
   const { data, isLoading } = useGetRideDetails(rideId);
   const rideDetails: RideDetails | undefined = data?.data;

   return (
      <Sheet open={isOpen} onOpenChange={onClose}>
         <SheetContent className='sm:max-w-xl'>
            <SheetHeader className='pb-4'>
               <SheetTitle>Ride Details</SheetTitle>
            </SheetHeader>

            <ScrollArea className='h-[calc(100vh-6rem)] px-6'>
               {isLoading && (
                  <div className='space-y-4'>
                     <div className='h-6 bg-gray-200 rounded animate-pulse w-32' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-48' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
                  </div>
               )}

               {!isLoading && rideDetails && (
                  <div className='space-y-6'>
                     {/* Combined Ride Info & Metrics */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>
                           Ride Overview
                        </h3>
                        <div className='space-y-3 text-sm'>
                           <div className='grid grid-cols-3 gap-3'>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Status</div>
                                 <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getStatusBadgeClass(rideDetails.status)}`}>
                                    {formatStatus(rideDetails.status)}
                                 </span>
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Duration</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {formatDuration(rideDetails.duration)}
                                 </div>
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Distance</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {formatDistance(rideDetails.distance)}
                                 </div>
                              </div>
                           </div>

                           {rideDetails.verificationCode && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Verification Code</div>
                                 <div className='text-gray-900 font-bold text-base tracking-wider'>
                                    {rideDetails.verificationCode}
                                 </div>
                              </div>
                           )}
                        </div>
                     </div>

                     {/* Route Map */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>
                           Route Map
                        </h3>
                        <RideRouteMap
                           pickupLocation={rideDetails.pickupLocation}
                           destinationLocation={rideDetails.destinationLocation}
                           stops={rideDetails.stops}
                        />
                     </div>

                     {/* Ride Participants & Vehicle Information */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>
                           Ride Participants & Vehicle
                        </h3>
                        <div className='grid grid-cols-1 md:grid-cols-2 gap-6 text-sm'>
                           {/* Rider Section */}
                           <div className='space-y-3'>
                              <div className='text-xs font-semibold text-blue-600 uppercase tracking-wide mb-2'>
                                 Rider
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Name</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {rideDetails.rider.firstName} {rideDetails.rider.lastName}
                                 </div>
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1 flex items-center gap-1'>
                                    <Phone className='w-3 h-3' />
                                    Phone
                                 </div>
                                 {rideDetails.rider?.phoneNumber ? (
                                    <div className='text-gray-900 font-medium'>
                                       {rideDetails.rider.phoneNumber}
                                    </div>
                                 ) : (
                                    <div className='text-gray-400 italic text-xs'>Not provided</div>
                                 )}
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1 flex items-center gap-1'>
                                    <Mail className='w-3 h-3' />
                                    Email
                                 </div>
                                 {rideDetails.rider?.email ? (
                                    <div className='text-gray-900 font-medium break-all'>
                                       {rideDetails.rider.email}
                                    </div>
                                 ) : (
                                    <div className='text-gray-400 italic text-xs'>Not provided</div>
                                 )}
                              </div>
                              {rideDetails.rider.averageRating && (
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Rating</div>
                                    <div className='text-gray-900 font-semibold'>
                                       ⭐ {rideDetails.rider.averageRating.toFixed(1)} / 5.0
                                    </div>
                                 </div>
                              )}
                           </div>

                           {/* Driver Section */}
                           {rideDetails.driver && (
                              <div className='space-y-3'>
                                 <div className='text-xs font-semibold text-green-600 uppercase tracking-wide mb-2'>
                                    Driver
                                 </div>
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Name</div>
                                    <div className='text-gray-900 font-semibold'>
                                       {rideDetails.driver.firstName} {rideDetails.driver.lastName}
                                    </div>
                                 </div>
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1 flex items-center gap-1'>
                                       <Phone className='w-3 h-3' />
                                       Phone
                                    </div>
                                    {rideDetails.driver?.phoneNumber ? (
                                       <div className='text-gray-900 font-medium'>
                                          {rideDetails.driver.phoneNumber}
                                       </div>
                                    ) : (
                                       <div className='text-gray-400 italic text-xs'>Not provided</div>
                                    )}
                                 </div>
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1 flex items-center gap-1'>
                                       <Mail className='w-3 h-3' />
                                       Email
                                    </div>
                                    {rideDetails.driver?.email ? (
                                       <div className='text-gray-900 font-medium break-all'>
                                          {rideDetails.driver.email}
                                       </div>
                                    ) : (
                                       <div className='text-gray-400 italic text-xs'>Not provided</div>
                                    )}
                                 </div>
                                 {rideDetails.driver.averageRating && (
                                    <div>
                                       <div className='text-gray-500 text-xs mb-1'>Rating</div>
                                       <div className='text-gray-900 font-semibold'>
                                          ⭐ {rideDetails.driver.averageRating.toFixed(1)} / 5.0
                                       </div>
                                    </div>
                                 )}
                              </div>
                           )}
                        </div>

                        {/* Product & Vehicle Section */}
                        <div className='mt-6 pt-6 border-t border-gray-200'>
                           <div className='text-xs font-semibold text-purple-600 uppercase tracking-wide mb-3'>
                              Product & Vehicle
                           </div>
                           <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm'>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Product</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {rideDetails.product.name}
                                 </div>
                              </div>
                              {rideDetails.driverVehicle && (
                                 <>
                                    <div>
                                       <div className='text-gray-500 text-xs mb-1'>Vehicle Number</div>
                                       <div className='text-gray-900 font-bold tracking-wider'>
                                          {rideDetails.driverVehicle.vehicleNumber}
                                       </div>
                                    </div>
                                    <div>
                                       <div className='text-gray-500 text-xs mb-1'>Vehicle Type</div>
                                       <div className='text-gray-900 font-medium'>
                                          {rideDetails.driverVehicle.vehicleType.name}
                                       </div>
                                    </div>
                                 </>
                              )}
                           </div>
                        </div>
                     </div>

                     {/* Locations */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>Locations</h3>
                        <div className='space-y-4 text-sm'>
                           {(() => {
                              // Find timestamps from lifecycle metadata
                              const arrivedAtPickupLifecycle = rideDetails.rideLifecycles?.find(
                                 lc => lc.status.toLowerCase().includes('arrived') &&
                                       lc.meta?.locationType?.toLowerCase() === 'pickup'
                              );
                              const arrivedAtDestinationLifecycle = rideDetails.rideLifecycles?.find(
                                 lc => lc.status.toLowerCase().includes('completed') ||
                                       (lc.status.toLowerCase().includes('arrived') &&
                                        lc.meta?.locationType?.toLowerCase() === 'destination')
                              );

                              return (
                                 <>
                                    {/* Pickup Location */}
                                    <div className='bg-white rounded-md p-3 border border-gray-200'>
                                       <div className='flex items-center gap-2 mb-2'>
                                          <div className='w-2 h-2 rounded-full bg-green-500'></div>
                                          <div className='text-gray-500 text-xs font-semibold'>Pickup</div>
                                       </div>
                                       <div className='font-medium text-gray-900 mb-1'>
                                          {rideDetails.pickupLocation.address || 'N/A'}
                                       </div>
                                       <div className='text-xs text-gray-500 mb-0.5'>Coordinates</div>
                                       <div className='text-xs text-gray-900 font-mono mb-2'>
                                          Latitude: {rideDetails.pickupLocation.lat.toFixed(6)}, Longitude: {rideDetails.pickupLocation.lng.toFixed(6)}
                                       </div>
                                       {arrivedAtPickupLifecycle && (
                                          <div className='mt-2 pt-2 border-t border-gray-200'>
                                             <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                             <div className='text-xs text-gray-900 font-medium'>
                                                {format(
                                                   new Date(arrivedAtPickupLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                    </div>

                                    {/* Stops */}
                                    {rideDetails.stops && rideDetails.stops.length > 0 && (
                                       <>
                                          {rideDetails.stops.map((stop, index) => {
                                             // Try to find when driver arrived at this stop from lifecycle metadata
                                             const arrivedAtStopLifecycle = rideDetails.rideLifecycles?.find(
                                                lc => lc.status.toLowerCase().includes('arrived') &&
                                                      lc.meta?.locationType?.toLowerCase() === 'stop' &&
                                                      lc.meta?.stopIndex === index
                                             );

                                             return (
                                                <div key={index} className='bg-white rounded-md p-3 border border-gray-200'>
                                                   <div className='flex items-center gap-2 mb-2'>
                                                      <div className='w-2 h-2 rounded-full bg-blue-500'></div>
                                                      <div className='text-gray-500 text-xs font-semibold'>
                                                         Stop {index + 1}
                                                      </div>
                                                   </div>
                                                   <div className='font-medium text-gray-900 mb-1'>
                                                      {stop.address || 'N/A'}
                                                   </div>
                                                   <div className='text-xs text-gray-500 mb-0.5'>Coordinates</div>
                                                   <div className='text-xs text-gray-900 font-mono mb-2'>
                                                      Latitude: {stop.lat.toFixed(6)}, Longitude: {stop.lng.toFixed(6)}
                                                   </div>
                                                   {arrivedAtStopLifecycle && (
                                                      <div className='mt-2 pt-2 border-t border-gray-200'>
                                                         <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                                         <div className='text-xs text-gray-900 font-medium'>
                                                            {format(
                                                               new Date(arrivedAtStopLifecycle.createdAt),
                                                               'MMM dd, yyyy • hh:mm:ss a'
                                                            )}
                                                         </div>
                                                      </div>
                                                   )}
                                                </div>
                                             );
                                          })}
                                       </>
                                    )}

                                    {/* Destination */}
                                    <div className='bg-white rounded-md p-3 border border-gray-200'>
                                       <div className='flex items-center gap-2 mb-2'>
                                          <div className='w-2 h-2 rounded-full bg-red-500'></div>
                                          <div className='text-gray-500 text-xs font-semibold'>Destination</div>
                                       </div>
                                       <div className='font-medium text-gray-900 mb-1'>
                                          {rideDetails.destinationLocation.address || 'N/A'}
                                       </div>
                                       <div className='text-xs text-gray-500 mb-0.5'>Coordinates</div>
                                       <div className='text-xs text-gray-900 font-mono mb-2'>
                                          Latitude: {rideDetails.destinationLocation.lat.toFixed(6)}, Longitude: {rideDetails.destinationLocation.lng.toFixed(6)}
                                       </div>
                                       {arrivedAtDestinationLifecycle && (
                                          <div className='mt-2 pt-2 border-t border-gray-200'>
                                             <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                             <div className='text-xs text-gray-900 font-medium'>
                                                {format(
                                                   new Date(arrivedAtDestinationLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                    </div>
                                 </>
                              );
                           })()}
                        </div>
                     </div>

                     {/* Ride Lifecycle Timeline */}
                     {rideDetails.rideLifecycles && rideDetails.rideLifecycles.length > 0 && (
                        <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-6'>
                              Ride Lifecycle
                           </h3>
                           <Timeline
                              defaultValue={rideDetails.rideLifecycles.length}
                           >
                              {rideDetails.rideLifecycles.map((lifecycle, index) => (
                                 <TimelineItem key={lifecycle.id} step={index + 1}>
                                    <TimelineHeader>
                                       <TimelineSeparator />
                                       <TimelineDate className='text-gray-600'>
                                          {format(
                                             new Date(lifecycle.createdAt),
                                             'MMM dd, yyyy'
                                          )}
                                          {' • '}
                                          {format(
                                             new Date(lifecycle.createdAt),
                                             'hh:mm a'
                                          )}
                                       </TimelineDate>
                                       <TimelineTitle className='text-gray-900 font-semibold'>
                                          {formatStatus(lifecycle.status)}
                                       </TimelineTitle>
                                       <TimelineIndicator className='bg-white' />
                                    </TimelineHeader>
                                 </TimelineItem>
                              ))}
                           </Timeline>
                        </div>
                     )}

                     {/* Reviews */}
                     {rideDetails.reviews && rideDetails.reviews.length > 0 && (
                        <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-4'>Reviews</h3>
                           <div className='space-y-3'>
                              {rideDetails.reviews.map(review => (
                                 <div
                                    key={review.id}
                                    className='border rounded-lg p-4 bg-white border-gray-200'
                                 >
                                    <div className='flex justify-between items-start mb-3'>
                                       <div className='font-semibold text-sm text-gray-900'>
                                          {review.reviewBy.firstName} {review.reviewBy.lastName}
                                       </div>
                                       <div className='text-sm font-bold text-gray-900'>
                                          ⭐ {review.rating.toFixed(1)}
                                       </div>
                                    </div>
                                    {review.review && (
                                       <div className='text-sm text-gray-700 mb-3 leading-relaxed'>
                                          {review.review}
                                       </div>
                                    )}
                                    <div className='text-xs text-gray-500'>
                                       {format(new Date(review.createdAt), 'MMM dd, yyyy • hh:mm a')}
                                    </div>
                                 </div>
                              ))}
                           </div>
                        </div>
                     )}
                  </div>
               )}
            </ScrollArea>
         </SheetContent>
      </Sheet>
   );
}

import { ApiProperty } from '@nestjs/swagger';

/**
 * Optimized radar map driver DTO - minimal data for real-time map view
 * Returns only essential fields: driver ID, location, status, and product info
 */
export class RadarMapDriverDto {
  @ApiProperty({
    description: 'Driver ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id!: string;

  @ApiProperty({
    description: 'Driver latitude coordinate',
    example: 12.9716,
  })
  lat!: number;

  @ApiProperty({
    description: 'Driver longitude coordinate',
    example: 77.5946,
  })
  lng!: number;

  @ApiProperty({
    description: 'Driver status',
    example: 'active',
    enum: ['active', 'pending', 'disabled', 'inactive'],
  })
  status!: string;

  @ApiProperty({
    description: 'City Product ID (primary vehicle)',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  cityProductId!: string;

  @ApiProperty({
    description: 'Product name',
    example: 'Standard Ride',
  })
  productName!: string;

  @ApiProperty({
    description: 'Product icon URL',
    example: 'https://example.com/icon.png',
    required: false,
    nullable: true,
  })
  productIcon?: string | null;
}

export class RadarMapResponseDto {
  @ApiProperty({
    description: 'Array of drivers with minimal metadata for radar map view',
    type: [RadarMapDriverDto],
  })
  drivers!: RadarMapDriverDto[];

  @ApiProperty({
    description: 'Total number of drivers found',
    example: 42,
  })
  total!: number;
}

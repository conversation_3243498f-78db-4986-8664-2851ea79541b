'use client';

import React, { useMemo } from 'react';
import { MapPin } from 'lucide-react';
import { STATUS_COLORS, RadarDriver } from '../types/radar';

interface MapControlsProps {
  drivers: RadarDriver[];
}

export const MapControls: React.FC<MapControlsProps> = ({ drivers }) => {
  // Calculate status counts
  const statusCounts = useMemo(() => {
    const counts = {
      active: 0,
      pending: 0,
      inactive: 0,
      disabled: 0,
    };

    drivers.forEach(driver => {
      if (driver.status in counts) {
        counts[driver.status]++;
      }
    });

    return counts;
  }, [drivers]);

  const totalCount = drivers.length;

  return (
    <div className='absolute top-4 left-4 z-10'>
      {/* Driver Status with Count */}
      <div className='bg-white rounded-lg shadow-lg border border-gray-200 p-2.5'>
        <div className='flex items-center justify-between mb-2.5'>
          <p className='text-xs font-semibold text-gray-700'>Total Drivers</p>
          <div className='flex items-center gap-1.5'>
            <MapPin className='h-4 w-4 text-blue-600' />
            <span className='text-lg font-bold text-gray-900'>{totalCount}</span>
          </div>
        </div>
        <div className='space-y-1.5'>
          <div className='flex items-center justify-between gap-3'>
            <div className='flex items-center gap-2'>
              <div
                className='w-3 h-3 rounded-full border-2'
                style={{ borderColor: STATUS_COLORS.active }}
              />
              <span className='text-xs text-gray-600'>Active</span>
            </div>
            <span className='text-xs font-semibold text-gray-700'>{statusCounts.active}</span>
          </div>
          <div className='flex items-center justify-between gap-3'>
            <div className='flex items-center gap-2'>
              <div
                className='w-3 h-3 rounded-full border-2'
                style={{ borderColor: STATUS_COLORS.pending }}
              />
              <span className='text-xs text-gray-600'>Pending</span>
            </div>
            <span className='text-xs font-semibold text-gray-700'>{statusCounts.pending}</span>
          </div>
          <div className='flex items-center justify-between gap-3'>
            <div className='flex items-center gap-2'>
              <div
                className='w-3 h-3 rounded-full border-2'
                style={{ borderColor: STATUS_COLORS.inactive }}
              />
              <span className='text-xs text-gray-600'>Inactive</span>
            </div>
            <span className='text-xs font-semibold text-gray-700'>{statusCounts.inactive}</span>
          </div>
          <div className='flex items-center justify-between gap-3'>
            <div className='flex items-center gap-2'>
              <div
                className='w-3 h-3 rounded-full border-2'
                style={{ borderColor: STATUS_COLORS.disabled }}
              />
              <span className='text-xs text-gray-600'>Disabled</span>
            </div>
            <span className='text-xs font-semibold text-gray-700'>{statusCounts.disabled}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

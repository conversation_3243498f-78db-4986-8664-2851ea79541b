import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsBoolean } from 'class-validator';

export class UpdateCityProductPrimaryStatusDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Driver vehicle ID',
  })
  @IsUUID('4')
  driverVehicleId!: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'City product ID',
  })
  @IsUUID('4')
  cityProductId!: string;

  @ApiProperty({
    example: true,
    description:
      'Whether this city product should be primary for the driver vehicle',
  })
  @IsBoolean()
  isPrimary!: boolean;
}

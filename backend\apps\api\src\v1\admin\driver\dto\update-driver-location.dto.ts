import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  Min,
  Max,
} from 'class-validator';

export enum DriverStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  BUSY = 'busy',
  IN_RIDE = 'in_ride',
}

export class UpdateDriverLocationDto {
  @ApiProperty({
    example: 'driver_123',
    description: 'Driver profile ID',
  })
  @IsString()
  driverId!: string;

  @ApiProperty({
    example: 12.9716,
    description: 'Latitude coordinate',
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  lat!: number;

  @ApiProperty({
    example: 77.5946,
    description: 'Longitude coordinate',
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  lng!: number;

  @ApiProperty({
    example: '2023-08-06T09:16:42.000Z',
    description: 'Timestamp of the location update',
  })
  @IsString()
  timestamp!: string;

  @ApiProperty({
    example: 'bangalore',
    description: 'City where the driver is located',
  })
  @IsString()
  city!: string;

  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.ONLINE,
    description: 'Current status of the driver',
  })
  @IsEnum(DriverStatus)
  status!: DriverStatus;

  @ApiProperty({
    example: 'ride_456',
    description: 'Current ride ID if driver is in a ride',
    required: false,
  })
  @IsOptional()
  @IsString()
  rideId?: string;

  @ApiProperty({
    description: 'Ride ID if driver is currently in a ride',
    example: 'ride_456',
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;
}

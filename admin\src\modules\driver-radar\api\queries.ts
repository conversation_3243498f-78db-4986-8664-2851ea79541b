import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { RadarMapBounds, RadarMapResponse, MAP_CONSTANTS } from '../types/radar';

/**
 * Hook for fetching driver locations for radar map view
 * Implements automatic polling every 5 seconds when enabled
 * Only polls when tab is visible and enabled
 */
export const useRadarMapDrivers = (bounds: RadarMapBounds | null, enabled: boolean = true) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['radar-drivers', bounds],
    queryFn: (): Promise<RadarMapResponse> => {
      if (!bounds) {
        throw new Error('Bounds are required');
      }
      return apiClient.post('/drivers/admin/radar-map/locations', bounds);
    },
    enabled: enabled && !!bounds && hasPermission(RBAC_PERMISSIONS.DRIVER_RADAR.VIEW),
    refetchInterval: query => {
      // Only poll if:
      // 1. Query is enabled
      // 2. Tab is visible
      // 3. No errors
      if (!enabled) return false;
      if (typeof document !== 'undefined' && document.visibilityState !== 'visible') return false;
      if (query.state.error) return false;

      return MAP_CONSTANTS.POLLING_INTERVAL_MS;
    },
    refetchIntervalInBackground: false, // Don't poll when tab is in background
    refetchOnWindowFocus: true, // Refetch when tab becomes active again
    staleTime: MAP_CONSTANTS.POLLING_INTERVAL_MS - 1000, // Consider data stale after 4s
    gcTime: 10000, // Keep in cache for 10s after unmount
    retry: 3, // Retry failed requests 3 times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

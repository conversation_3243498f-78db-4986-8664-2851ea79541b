// Driver Due Limit TypeScript interfaces based on backend API

export interface DriverDueConfig {
  id: string;
  cityId: string;
  maxDueLimit: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SetDriverDueLimitRequest {
  cityId: string;
  maxDueLimit: number;
}

export interface UpdateDriverDueLimitRequest {
  maxDueLimit?: number;
  isActive?: boolean;
}

export interface DriverDueLimitResponse {
  success: boolean;
  message: string;
  data: DriverDueConfig;
  timestamp: number;
}

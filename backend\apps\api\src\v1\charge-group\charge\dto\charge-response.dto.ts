import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ChargeType,
  ChargeMeter,
  PriceModel,
} from '@shared/shared/repositories/models/charge.model';
import { TaxGroupResponseDto } from '../../../tax-groups/dto/tax-group-response.dto';
import { CommissionResponseDto } from '../../../commissions/dto/commission-response.dto';

export class ChargeResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Name of the charge',
    example: 'Waiting Charge',
  })
  name!: string;

  @ApiProperty({
    description: 'Unique identifier for the charge',
    example: 'waiting_charge',
  })
  identifier!: string;

  @ApiProperty({
    description: 'Type of charge',
    enum: ChargeType,
    example: ChargeType.METERED,
  })
  chargeType!: ChargeType;

  @ApiPropertyOptional({
    description: 'Conditional logic for when charge applies',
    example: {
      timeOfDay: { start: '08:00', end: '18:00' },
      dayOfWeek: ['monday', 'tuesday'],
    },
  })
  condition?: any;

  @ApiPropertyOptional({
    description: 'Meter type for metered charges',
    enum: ChargeMeter,
    example: ChargeMeter.PICKUP_WAIT_DURATION,
  })
  meter?: ChargeMeter;

  @ApiProperty({
    description: 'Price model for the charge',
    enum: PriceModel,
    example: PriceModel.LINEAR_RATE,
  })
  priceModel!: PriceModel;

  @ApiProperty({
    description: 'Price configuration',
    example: {
      rate: 0.5,
      currency: 'USD',
    },
  })
  price!: any;

  @ApiPropertyOptional({
    description: 'Percentage for percentage-based charges',
    example: 15.5,
  })
  percentage?: number;

  @ApiPropertyOptional({
    description: 'ID of the charge used for percentage calculation',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  percentageOfChargeId?: string;

  @ApiPropertyOptional({
    description: 'ID of the charge group (null for standalone charges)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  chargeGroupId?: string;

  @ApiPropertyOptional({
    description: 'Tax group ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  taxGroupId?: string | null;

  @ApiPropertyOptional({
    description: 'Tax group information',
    type: TaxGroupResponseDto,
  })
  taxGroup?: TaxGroupResponseDto | null;

  @ApiPropertyOptional({
    description: 'Commission ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  commissionId?: string | null;

  @ApiPropertyOptional({
    description: 'Commission information',
    type: CommissionResponseDto,
  })
  commission?: CommissionResponseDto | null;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt!: Date;
}

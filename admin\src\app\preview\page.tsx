/* eslint-disable @next/next/no-img-element */
'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';

function PreviewContent() {
   const searchParams = useSearchParams();
   const url = searchParams.get('url');
   const type = searchParams.get('type');

   if (!url) {
      return (
         <div className='flex items-center justify-center h-screen bg-gray-100'>
            <div className='text-center'>
               <h1 className='text-2xl font-semibold text-gray-800 mb-2'>No File URL Provided</h1>
               <p className='text-gray-600'>Please provide a valid file URL to preview.</p>
            </div>
         </div>
      );
   }

   const isImage =
      type === 'image' ||
      url.includes('.jpg') ||
      url.includes('.jpeg') ||
      url.includes('.png') ||
      url.includes('.svg');

   return (
      <div className='h-screen w-full flex items-center justify-center'>
         {isImage ? (
            <div
               style={{
                  height: 'inherit',
               }}
               className='flex  justify-center'
            >
               <img src={url} alt='' />
            </div>
         ) : (
            <embed
               src={url}
               className={cn(
                  'border-none',
                  isImage ? 'w-auto h-[100vh] flex justify-center items-center' : 'w-full h-full'
               )}
               title='File Preview'
            />
         )}
      </div>
   );
}

export default function PreviewPage() {
   return (
      <Suspense
         fallback={
            <div className='flex items-center justify-center h-screen bg-gray-100'>
               <div className='text-center'>
                  <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
                  <p className='text-gray-600'>Loading preview...</p>
               </div>
            </div>
         }
      >
         <PreviewContent />
      </Suspense>
   );
}

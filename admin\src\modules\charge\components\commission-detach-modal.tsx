'use client';

import {
   <PERSON><PERSON>,
   DialogContent,
   Di<PERSON>Header,
   DialogTitle,
   DialogFooter,
   DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';

interface CommissionDetachModalProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
   isLoading: boolean;
   chargeName: string;
   commissionName: string;
}

export function CommissionDetachModal({
   isOpen,
   onClose,
   onConfirm,
   isLoading,
   chargeName,
   commissionName,
}: CommissionDetachModalProps) {
   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Remove Commission</DialogTitle>
               <DialogDescription className='text-sm text-muted-foreground mt-2'>
                  Are you sure you want to remove commission "{commissionName}" from "{chargeName}"?
               </DialogDescription>
            </DialogHeader>

            <DialogFooter>
               <Button type='button' variant='outline' onClick={onClose} disabled={isLoading}>
                  Cancel
               </Button>
               <Button type='button' variant='destructive' onClick={onConfirm} disabled={isLoading}>
                  {isLoading ? (
                     <>
                        <Spinner className='h-4 w-4 mr-2' />
                        Removing...
                     </>
                  ) : (
                     'Remove Commission'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

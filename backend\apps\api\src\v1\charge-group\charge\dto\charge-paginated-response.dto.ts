import { ApiProperty } from '@nestjs/swagger';
import { ChargeResponseDto } from './charge-response.dto';

export class ChargePaginatedDataDto {
  @ApiProperty({
    description: 'Array of charges',
    type: [ChargeResponseDto],
  })
  data!: ChargeResponseDto[];

  @ApiProperty({
    description: 'Total number of charges',
    example: 100,
  })
  total!: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit!: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages!: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNext!: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrev!: boolean;
}

export class ChargePaginatedResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Charges retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Paginated charge data',
    type: ChargePaginatedDataDto,
  })
  data!: ChargePaginatedDataDto;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}

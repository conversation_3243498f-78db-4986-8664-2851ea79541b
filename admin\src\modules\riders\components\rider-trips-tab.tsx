'use client';

import { useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useListRides, useGetRideDetails } from '@/modules/rides/api/queries';
import { useListProduct } from '@/modules/product/api/queries';
import { RideTable } from '@/modules/rides/components/ride-table';
import { RideFilters } from '@/modules/rides/components/ride-filters';
import { RideDetailsView } from '@/modules/rides/components/ride-details-view';
import { CancelRideModal } from '@/modules/rides/components/cancel-ride-modal';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Ban } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';

interface RiderTripsTabProps {
   riderId: string;
}

export function RiderTripsTab({ riderId }: RiderTripsTabProps) {
   const router = useRouter();
   const searchParams = useSearchParams();
   const queryClient = useQueryClient();
   const rideId = searchParams.get('rideId');
   const [page, setPage] = useState(1);
   const [limit] = useState(9);
   const [status, setStatus] = useState<string>('');
   const [fromDate, setFromDate] = useState<string>('');
   const [toDate, setToDate] = useState<string>('');
   const [searchQuery, setSearchQuery] = useState<string>('');
   const [searchType, setSearchType] = useState<string>('driverPhoneNumber');
   const [productId, setProductId] = useState<string>('');
   const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

   // Build search params based on search type
   const searchParamsObj: {
      driverName?: string;
      vehicleNumber?: string;
      driverPhoneNumber?: string;
   } = {};

   if (searchQuery && searchType) {
      if (searchType === 'driverName') {
         searchParamsObj.driverName = searchQuery;
      } else if (searchType === 'vehicleNumber') {
         searchParamsObj.vehicleNumber = searchQuery;
      } else if (searchType === 'driverPhoneNumber') {
         searchParamsObj.driverPhoneNumber = searchQuery;
      }
   }

   // Fetch products for filter
   const listProducts = useListProduct({
      page: 1,
      limit: 100,
      isEnabled: 'true',
   });

   const listRides = useListRides({
      page,
      limit,
      status: status || undefined,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      productId: productId || undefined,
      riderId, // Filter by riderId - this is the key!
      ...searchParamsObj,
   });

   const rideDetails = useGetRideDetails(rideId || '');

   const handleStatusChange = (value: string) => {
      setStatus(value === 'all' ? '' : value);
      setPage(1);
   };

   const handleSearchTypeChange = (type: string) => {
      setSearchType(type);
      setSearchQuery(''); // Clear search when type changes
      setPage(1);
   };

   const handleProductChange = (value: string) => {
      setProductId(value === 'all' ? '' : value);
      setPage(1);
   };

   const handleClearFilters = () => {
      setStatus('');
      setFromDate('');
      setToDate('');
      setSearchQuery('');
      setSearchType('driverPhoneNumber');
      setProductId('');
      setPage(1);
   };

   const handleBackToList = () => {
      // Remove rideId from URL to show list
      const currentPath = window.location.pathname;
      router.push(currentPath);
   };

   // Check if ride can be cancelled
   const canCancelRide = (status: string) => {
      if (!status) return false;
      const normalizedStatus = status.toLowerCase().trim();
      const nonCancellableStatuses = ['trip_completed', 'cancelled', 'trip_started'];
      return !nonCancellableStatuses.includes(normalizedStatus);
   };

   const handleCancelSuccess = () => {
      queryClient.invalidateQueries({ queryKey: ['ride-details', rideId] });
      queryClient.invalidateQueries({ queryKey: ['rides'] });
      rideDetails.refetch();
   };

   // Show ride details view if rideId is present
   if (rideId) {
      const currentRideDetails = rideDetails.data?.data;
      return (
         <div className='space-y-4'>
            <div className='flex items-center justify-between'>
               <Button variant='ghost' onClick={handleBackToList} className='gap-2'>
                  <ArrowLeft className='h-4 w-4' />
                  Back to Trips
               </Button>
               {currentRideDetails && canCancelRide(currentRideDetails.status) && (
                  <Button
                     variant='outline'
                     onClick={() => setIsCancelModalOpen(true)}
                     className='gap-2 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700'
                  >
                     <Ban className='h-4 w-4' />
                     Cancel Ride
                  </Button>
               )}
            </div>
            {currentRideDetails && <RideDetailsView rideDetails={currentRideDetails} />}

            {/* Cancel Ride Modal */}
            <CancelRideModal
               isOpen={isCancelModalOpen}
               onClose={() => setIsCancelModalOpen(false)}
               rideId={rideId}
               onCancelSuccess={handleCancelSuccess}
            />
         </div>
      );
   }

   // Show list of rides
   return (
      <div className='space-y-4'>
         <RideFilters
            status={status}
            fromDate={fromDate}
            toDate={toDate}
            searchQuery={searchQuery}
            searchType={searchType}
            productId={productId}
            products={listProducts.data?.data || []}
            onStatusChange={handleStatusChange}
            onFromDateChange={setFromDate}
            onToDateChange={setToDate}
            onSearchQueryChange={setSearchQuery}
            onSearchTypeChange={handleSearchTypeChange}
            onProductIdChange={handleProductChange}
            onClearFilters={handleClearFilters}
            isLoading={listRides.isFetching && !listRides.isLoading}
            // Hide rider-specific search options since we're already filtering by rider
            hideSearchTypes={['riderName', 'riderPhoneNumber']}
         />

         <RideTable
            data={listRides.data}
            isLoading={listRides.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
            // hasFilters={!!status || !!fromDate || !!toDate || !!searchQuery || !!productId}
            // onClearFilters={handleClearFilters}
         />
      </div>
   );
}

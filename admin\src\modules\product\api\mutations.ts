import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateProductRequest, ProductResponse, UpdateProductRequest } from '../types/product';

/**
 * Hook for creating a new product
 */
export const useCreateProduct = () => {
   return useMutation({
      mutationFn: async (data: CreateProductRequest): Promise<ProductResponse> => {
         return apiClient.post('/products', data);
      },
   });
};

/**
 * Hook for updating a product
 */
export const useUpdateProduct = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateProductRequest): Promise<ProductResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/products/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a product
 */
export const useDeleteProduct = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/products/${id}`);
      },
   });
};

/**
 * Hook for enabling a product
 */
export const useEnableProduct = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<ProductResponse> => {
         return apiClient.patch(`/products/${id}/enable`);
      },
   });
};

/**
 * Hook for disabling a product
 */
export const useDisableProduct = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<ProductResponse> => {
         return apiClient.patch(`/products/${id}/disable`);
      },
   });
};

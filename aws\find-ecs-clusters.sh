#!/bin/bash

# Script to find available ECS clusters
# This helps you identify the correct cluster name to use in pipeline variables

set -e

REGION="ap-south-1"

echo "🔍 Finding ECS clusters in region: $REGION"
echo "=========================================="

# Check if AWS CLI is configured
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo "❌ AWS CLI not configured or credentials invalid"
    echo "Please run: aws configure"
    exit 1
fi

echo "✅ AWS CLI is configured"
echo ""

# List all ECS clusters
echo "📋 Available ECS Clusters:"
echo "--------------------------"

CLUSTERS=$(aws ecs list-clusters --region $REGION --query 'clusterArns[]' --output text 2>/dev/null || echo "")

if [ -z "$CLUSTERS" ]; then
    echo "❌ No ECS clusters found in region $REGION"
    echo ""
    echo "💡 You need to create an ECS cluster first:"
    echo "   - Go to AWS ECS Console"
    echo "   - Click 'Create Cluster'"
    echo "   - Choose EC2 or Fargate"
    echo "   - Name it something like 'tukxi-staging-cluster'"
    exit 1
fi

echo "Found the following clusters:"
for CLUSTER_ARN in $CLUSTERS; do
    CLUSTER_NAME=$(echo $CLUSTER_ARN | rev | cut -d'/' -f1 | rev)
    echo "  📦 $CLUSTER_NAME"
    
    # Get cluster status
    STATUS=$(aws ecs describe-clusters --region $REGION --clusters $CLUSTER_NAME --query 'clusters[0].status' --output text)
    echo "     Status: $STATUS"
    
    # Count running tasks
    RUNNING_TASKS=$(aws ecs list-tasks --cluster $CLUSTER_NAME --desired-status RUNNING --query 'taskArns | length(@)' --output text)
    echo "     Running tasks: $RUNNING_TASKS"
    
    # Count services
    SERVICES=$(aws ecs list-services --cluster $CLUSTER_NAME --query 'serviceArns | length(@)' --output text)
    echo "     Services: $SERVICES"
    echo ""
done

echo "🔧 NEXT STEPS:"
echo "=============="
echo "1. Choose the cluster name you want to use (e.g., 'tukxi-staging-cluster')"
echo "2. Go to your Bitbucket repository settings"
echo "3. Navigate to: Repository settings → Pipelines → Repository variables"
echo "4. Add a new variable:"
echo "   - Name: ECS_CLUSTER_NAME"
echo "   - Value: [your chosen cluster name]"
echo "   - Secured: No (it's not sensitive data)"
echo ""
echo "Example variable value based on your clusters above:"
for CLUSTER_ARN in $CLUSTERS; do
    CLUSTER_NAME=$(echo $CLUSTER_ARN | rev | cut -d'/' -f1 | rev)
    echo "   ECS_CLUSTER_NAME = $CLUSTER_NAME"
    break  # Just show the first one as an example
done

import { BaseEntity } from '../base.repository';
import { Ride } from './ride.model';
import { RideMeterLog } from './rideMeterLog.model';

/**
 * Enum for standard ride meter types
 */
export enum RideMeterType {
  // Distance metrics
  PICKUP_DISTANCE = 'pickup_distance',
  TRIP_DISTANCE = 'trip_distance',
  TOTAL_DISTANCE = 'total_distance',

  // Duration metrics
  PICKUP_DURATION = 'pickup_duration',
  TRIP_DURATION = 'trip_duration',
  ACTUAL_DURATION = 'actual_duration',

  // Wait time metrics
  PICKUP_WAIT_TIME = 'pickup_wait_time',
  TRIP_WAIT_TIME = 'trip_wait_time',
  DESTINATION_WAIT_TIME = 'destination_wait_time',

  // Other metrics
  IDLE_TIME = 'idle_time',
  SURGE_MULTIPLIER = 'surge_multiplier',
  TOLL_CHARGES = 'toll_charges',
}

/**
 * Enum for measurement units
 */
export enum RideMeterUnit {
  // Distance units
  METERS = 'meters',
  KILOMETERS = 'km',
  MILES = 'miles',

  // Time units
  SECONDS = 'seconds',
  MINUTES = 'minutes',
  HOURS = 'hours',

  // Currency units
  INR = 'INR',
  USD = 'USD',
  EUR = 'EUR',

  // Multiplier units
  MULTIPLIER = 'multiplier',
  PERCENTAGE = 'percentage',

  // Count units
  COUNT = 'count',
}

/**
 * Interface for ride meter/metric data
 */
export interface RideMeter extends BaseEntity {
  rideId: string;
  name: string; // The meter/metric name (e.g., "distance", "duration", "wait_time")
  value: number; // The measured value
  unit: string; // The unit of measurement (e.g., "km", "minutes", "seconds")

  // Relations
  ride?: Ride;
  rideMeterLogs?: RideMeterLog[];
}

/**
 * Interface for creating a new ride meter
 */
export interface CreateRideMeterData {
  rideId: string;
  name: string;
  value: number;
  unit: string;
}

/**
 * Interface for updating ride meter data
 */
export interface UpdateRideMeterData {
  name?: string;
  value?: number;
  unit?: string;
}

/**
 * Interface for ride meter query filters
 */
export interface RideMeterFilters {
  rideId?: string;
  name?: string;
  names?: string[];
  unit?: string;
  minValue?: number;
  maxValue?: number;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * Interface for bulk ride meter creation
 */
export interface BulkCreateRideMeterData {
  rideId: string;
  meters: Array<{
    name: string;
    value: number;
    unit: string;
  }>;
}

/**
 * Interface for ride meter summary/aggregation
 */
export interface RideMeterSummary {
  rideId: string;
  totalMeters: number;
  metersByType: Record<string, number>;
  metersByUnit: Record<string, number>;
  totalDistance?: number; // in kilometers
  totalDuration?: number; // in seconds
  totalWaitTime?: number; // in seconds
}

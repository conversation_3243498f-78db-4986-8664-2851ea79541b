'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import {
   Timeline,
   TimelineDate,
   TimelineHeader,
   TimelineIndicator,
   TimelineItem,
   TimelineSeparator,
   TimelineTitle,
} from '@/components/ui/timeline';
import { RideLifecycle } from '../types/ride';
import { formatStatus } from '../utils/ride-formatters';

interface RideLifecycleCardProps {
   rideLifecycles: RideLifecycle[];
}

export function RideLifecycleCard({ rideLifecycles }: RideLifecycleCardProps) {
   const [lifecycleModalOpen, setLifecycleModalOpen] = useState(false);

   if (!rideLifecycles || rideLifecycles.length === 0) {
      return null;
   }

   const firstLifecycle = rideLifecycles[0];
   const lastLifecycle = rideLifecycles[rideLifecycles.length - 1];

   return (
      <Card className='p-4 rounded-sm'>
         <div className='flex items-center justify-between mb-2'>
            <h3 className='text-base font-semibold text-gray-900'>Ride Lifecycle</h3>
            <Dialog open={lifecycleModalOpen} onOpenChange={setLifecycleModalOpen}>
               <DialogTrigger asChild>
                  <button className='text-sm cursor-pointer text-blue-600 hover:text-blue-700 flex items-center gap-1'>
                     See all updates
                     <ChevronRight className='w-4 h-4' />
                  </button>
               </DialogTrigger>
               <DialogContent className='max-w-2xl max-h-[80vh] overflow-y-auto'>
                  <DialogHeader>
                     <DialogTitle>Ride Lifecycle</DialogTitle>
                  </DialogHeader>
                  <div className='mt-4'>
                     <Timeline defaultValue={rideLifecycles.length}>
                        {rideLifecycles.map((lifecycle, index) => (
                           <TimelineItem key={lifecycle.id} step={index + 1}>
                              <TimelineHeader>
                                 <TimelineSeparator />
                                 <TimelineDate className='text-gray-600'>
                                    {format(new Date(lifecycle.createdAt), 'MMM dd, yyyy')}
                                    {' • '}
                                    {format(new Date(lifecycle.createdAt), 'hh:mm a')}
                                 </TimelineDate>
                                 <TimelineTitle className='text-gray-900 font-semibold'>
                                    {formatStatus(lifecycle.status)}
                                 </TimelineTitle>
                                 <TimelineIndicator className='bg-white' />
                              </TimelineHeader>
                           </TimelineItem>
                        ))}
                     </Timeline>
                  </div>
               </DialogContent>
            </Dialog>
         </div>

         {/* Show only first and last status */}
         <div className='space-y-3'>
            {firstLifecycle && (
               <div className='flex items-center gap-3'>
                  <div className='w-2 h-2 rounded-full bg-green-500 flex-shrink-0'></div>
                  <div className='flex-1'>
                     <div className='text-sm font-medium text-gray-900'>
                        {formatStatus(firstLifecycle.status)}
                     </div>
                     <div className='text-xs text-gray-500'>
                        {format(new Date(firstLifecycle.createdAt), 'MMM dd, yyyy • hh:mm a')}
                     </div>
                  </div>
               </div>
            )}

            {lastLifecycle && lastLifecycle.id !== firstLifecycle?.id && (
               <div className='flex items-center gap-3'>
                  <div className='w-2 h-2 rounded-full bg-blue-500 flex-shrink-0'></div>
                  <div className='flex-1'>
                     <div className='text-sm font-medium text-gray-900'>
                        {formatStatus(lastLifecycle.status)}
                     </div>
                     <div className='text-xs text-gray-500'>
                        {format(new Date(lastLifecycle.createdAt), 'MMM dd, yyyy • hh:mm a')}
                     </div>
                  </div>
               </div>
            )}
         </div>
      </Card>
   );
}

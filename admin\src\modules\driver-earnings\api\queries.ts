import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
   AggregatedEarningsResponse,
   DailyEarningsResponse,
   AggregatedEarningsParams,
   DailyEarningsParams,
} from '../types/driver-earnings';

/**
 * Hook for listing aggregated driver earnings with pagination and filters
 */
export const useListAggregatedEarnings = ({
   page = 1,
   limit = 10,
   fromDate,
   toDate,
   driverId,
   cityId,
   phoneNumber,
}: AggregatedEarningsParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: hasPermission(RBAC_PERMISSIONS.DRIVER_EARNINGS.LIST),
      queryKey: ['driver-earnings-aggregated', page, limit, fromDate, toDate, driverId, cityId, phoneNumber],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<AggregatedEarningsResponse> => {
         return apiClient.get('/driver-earnings/aggregated', {
            params: {
               page,
               limit,
               fromDate,
               toDate,
               driverId,
               cityId,
               phoneNumber,
            },
         });
      },
   });
};

/**
 * Hook for listing daily driver earnings for a specific driver
 */
export const useListDailyEarnings = ({
   driverId,
   page = 1,
   limit = 10,
   fromDate,
   toDate,
}: DailyEarningsParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: !!driverId && hasPermission(RBAC_PERMISSIONS.DRIVER_EARNINGS.LIST),
      queryKey: ['driver-earnings-daily', driverId, page, limit, fromDate, toDate],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<DailyEarningsResponse> => {
         return apiClient.get('/driver-earnings/daily', {
            params: {
               driverId,
               page,
               limit,
               fromDate,
               toDate,
            },
         });
      },
   });
};

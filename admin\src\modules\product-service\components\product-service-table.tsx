'use client';

import { CustomPagination } from '@/components/pagination';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { Package } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ListProductServiceResponse, ProductService } from '../types/product-service';
import { ProductServiceModal } from './product-service-modal';
import { ProductServiceTableEmpty } from './product-service-table-empty';
import { ProductServiceTableLoading } from './product-service-table-loading';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';

const getColumns = ({
   handleEditClick,
   withPermission,
}: {
   handleEditClick: (id: string) => void;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<ProductService>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const productService = row.original as ProductService;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium'>{productService.name}</div>
            </div>
         );
      },
      size: 250,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const productService = row.original as ProductService;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600 max-w-xs truncate'>
                  {productService.description || '-'}
               </div>
            </div>
         );
      },
      size: 300,
   },
   {
      accessorKey: 'icon',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Icon</div>,
      cell: ({ row }) => {
         const productService = row.original as ProductService;
         return (
            <div className='flex justify-center'>
               {productService.icon ? (
                  <ErrorBoundary
                     fallback={
                        <div className='w-8 h-8 rounded bg-gray-100 flex items-center justify-center border border-gray-200'>
                           <Package className='w-4 h-4 text-gray-400' />
                        </div>
                     }
                  >
                     <Image
                        src={productService.icon}
                        alt={`${productService.name} icon`}
                        width={32}
                        height={32}
                        className='w-8 h-8 rounded object-cover border border-gray-200'
                     />
                  </ErrorBoundary>
               ) : (
                  <div className='w-8 h-8 bg-gray-100 rounded border border-gray-200 flex items-center justify-center'>
                     <Package className='w-4 h-4 text-gray-400' />
                  </div>
               )}
            </div>
         );
      },
      size: 80,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const productService = row.original as ProductService;
         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.PRODUCT_SERVICE.EDIT, () =>
                        handleEditClick(productService.id)
                     );
                  }}
               >
                  Edit
               </button>
            </div>
         );
      },
      size: 100,
   },
];

interface ProductServiceTableProps {
   data: ListProductServiceResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function ProductServiceTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
}: ProductServiceTableProps) {
   const [productServiceToEdit, setProductServiceToEdit] = useState<string | null>(null);
   const { withPermission } = useRoleBasedAccess();

   const handleEditClick = (id: string) => {
      setProductServiceToEdit(id);
   };

   const columns = getColumns({
      handleEditClick,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <ProductServiceTableLoading />;
   }

   if (!data?.data?.length) {
      return <ProductServiceTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         <ProductServiceModal
            productServiceId={productServiceToEdit}
            isOpen={!!productServiceToEdit}
            onClose={() => setProductServiceToEdit(null)}
         />
      </div>
   );
}

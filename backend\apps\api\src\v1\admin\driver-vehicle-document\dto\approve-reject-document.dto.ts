import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export enum DocumentAction {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
}

export class ApproveRejectDocumentDto {
  @ApiProperty({
    enum: DocumentAction,
    example: DocumentAction.APPROVE,
    description: 'Action to perform on the document',
  })
  @IsEnum(DocumentAction)
  action!: DocumentAction;

  @ApiProperty({
    example: 'Document quality is poor, please resubmit with better image',
    description: 'Rejection note (required when rejecting)',
    required: false,
  })
  @IsOptional()
  @IsString()
  rejectionNote?: string;
}

import { Injectable, Logger } from '@nestjs/common';
// import { Cron, CronExpression } from '@nestjs/schedule';
import { RideScheduleService } from '@shared/shared/modules/ride/services/ride-schedule.service';

@Injectable()
export class RidesCronService {
  private readonly logger = new Logger(RidesCronService.name);

  constructor(private readonly rideScheduleService: RideScheduleService) {}

  /**
   * Cron job that runs every 10 minutes to process scheduled rides
   * Cron expression: "0 star/10 star star star star" means "at minute 0 of every 10th minute"
   * This will run at: 00:00, 00:10, 00:20, 00:30, 00:40, 00:50, etc.
   */

  // @Cron(CronExpression.EVERY_10_MINUTES)
  async processScheduledRides(): Promise<void> {
    this.logger.log(
      '****************** Starting scheduled rides cron job ****************** ',
    );

    try {
      const result = await this.rideScheduleService.processAllScheduledRides();

      this.logger.log(
        `Scheduled rides cron job completed: ${result.processed} processed, ${result.failed} failed`,
      );

      if (result.failed > 0) {
        this.logger.warn(
          `Some scheduled rides failed to process: ${result.errors.join(', ')}`,
        );
      }
    } catch (error) {
      this.logger.error('Scheduled rides cron job failed:', error);
    }
  }

  /**
   * Manual trigger for processing scheduled rides (for testing/debugging)
   */
  async triggerScheduledRidesProcessing(): Promise<{
    processed: number;
    failed: number;
    errors: string[];
  }> {
    this.logger.log('Manual trigger for scheduled rides processing...');

    try {
      const result = await this.rideScheduleService.processAllScheduledRides();

      this.logger.log(
        `Manual scheduled rides processing completed: ${result.processed} processed, ${result.failed} failed`,
      );

      return result;
    } catch (error) {
      this.logger.error('Manual scheduled rides processing failed:', error);
      throw error;
    }
  }
}

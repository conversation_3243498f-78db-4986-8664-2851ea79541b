'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { ChevronRight } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FareSpec } from '../types/ride';
import { formatCurrency } from '../utils/ride-formatters';
import { FareBreakdownModal } from './fare-breakdown-modal';

interface RidePricingCardProps {
   fareSpec?: FareSpec | null;
}

export function RidePricingCard({ fareSpec }: RidePricingCardProps) {
   const [fareModalOpen, setFareModalOpen] = useState(false);

   return (
      <Card className='p-4 rounded-sm'>
         <div className='flex items-center justify-between mb-2'>
            <h3 className='text-base font-semibold text-gray-900'>Pricing</h3>
         </div>

         {fareSpec ? (
            <>
               {fareSpec?.chargeBreakdown?.length > 0 && (
                  <FareBreakdownModal
                     open={fareModalOpen}
                     onOpenChange={setFareModalOpen}
                     fareSpec={fareSpec}
                  />
               )}

               <div className='space-y-2'>
                  {/* Main Fare */}
                  <div>
                     <div className='text-xs text-gray-500 mb-0.5'>Total Fare</div>
                     <div className='text-xl font-bold text-gray-900'>
                        {formatCurrency(fareSpec.passengerFare, fareSpec.currency)}
                     </div>
                  </div>

                  {/* Charges Preview */}
                  {fareSpec.chargeBreakdown?.length > 0 && (
                     <div className='pt-1.5 border-t border-gray-200'>
                        <div className='text-xs text-gray-500 mb-1.5'>Fare Breakdown</div>
                        <ScrollArea type='auto' className='h-[50px] pr-3'>
                           <div className='space-y-1 text-sm'>
                              {fareSpec.chargeBreakdown?.map(charge => (
                                 <div key={charge.chargeId} className='flex justify-between'>
                                    <span className='text-gray-700 text-xs font-medium truncate pr-2'>
                                       {charge.chargeName}
                                    </span>
                                    <span className='text-gray-900 font-semibold text-xs'>
                                       {formatCurrency(charge.calculatedAmount, fareSpec.currency)}
                                    </span>
                                 </div>
                              ))}
                              {fareSpec.totalTaxes > 0 && (
                                 <div className='flex justify-between'>
                                    <span className='text-gray-700 text-xs font-medium truncate pr-2'>
                                       Tax
                                    </span>
                                    <span className='text-gray-900 font-semibold text-xs'>
                                       {formatCurrency(fareSpec.totalTaxes, fareSpec.currency)}
                                    </span>
                                 </div>
                              )}
                           </div>
                        </ScrollArea>
                        <button
                           onClick={() => setFareModalOpen(true)}
                           className='text-sm cursor-pointer text-blue-600 hover:text-blue-700 flex items-center gap-1 justify-center w-full mt-2'
                        >
                           view detailed breakdown
                           <ChevronRight className='w-3 h-3' />
                        </button>
                     </div>
                  )}

                  {/* Revenue Distribution */}
                  <div className='pt-1.5 border-t border-gray-200'>
                     <div className='text-xs text-gray-500 mb-1.5'>Revenue Split</div>
                     <div className='space-y-1'>
                        <div className='flex justify-between text-sm'>
                           <span className='text-gray-700 text-xs font-medium'>Driver</span>
                           <span className='font-semibold text-green-600 text-xs'>
                              {formatCurrency(fareSpec.driverEarnings, fareSpec.currency)}
                           </span>
                        </div>

                        <div className='flex justify-between text-sm'>
                           <span className='text-gray-700 text-xs font-medium'>Commissions</span>
                           <span className='font-semibold text-blue-600 text-xs'>
                              {formatCurrency(fareSpec.totalCommissions, fareSpec.currency)}
                           </span>
                        </div>

                        {fareSpec.totalTaxes > 0 && (
                           <div className='flex justify-between text-sm'>
                              <span className='text-gray-700 text-xs font-medium'>Tax</span>
                              <span className='font-semibold text-red-600 text-xs'>
                                 {formatCurrency(fareSpec.totalTaxes, fareSpec.currency)}
                              </span>
                           </div>
                        )}
                     </div>
                  </div>
               </div>
            </>
         ) : (
            <div className='flex flex-col items-center justify-center py-8 text-center'>
               <div className='w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3'>
                  <svg
                     className='w-6 h-6 text-gray-400'
                     fill='none'
                     viewBox='0 0 24 24'
                     stroke='currentColor'
                  >
                     <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                     />
                  </svg>
               </div>
               <div className='text-sm font-medium text-gray-900 mb-1'>No pricing data</div>
               <div className='text-xs text-gray-500'>
                  Fare information unavailable for this ride
               </div>
            </div>
         )}
      </Card>
   );
}

'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useSearchRides } from '../api/queries';
import { useCreateRide } from '../api/mutations';
import { LocationSearch } from '../components/location-search';
import { Location, RideSearchResult } from '../types/book-ride';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useListRiders } from '@/modules/riders/api/queries';
import { Rider } from '@/modules/riders/types/rider';
import { X, Plus, Check, ChevronsUpDown } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

type RiderType = 'existing' | 'new';

export function BookRidePage() {
   const { withPermission } = useRoleBasedAccess();
   const router = useRouter();
   const riderTriggerRef = useRef<HTMLButtonElement>(null);

   // Rider state
   const [riderType, setRiderType] = useState<RiderType>('existing');
   const [selectedRider, setSelectedRider] = useState<Rider | null>(null);
   const [riderSearchQuery, setRiderSearchQuery] = useState('');
   const [riderComboboxOpen, setRiderComboboxOpen] = useState(false);
   const [newRiderName, setNewRiderName] = useState('');
   const [newRiderPhone, setNewRiderPhone] = useState('');
   const [newRiderEmail, setNewRiderEmail] = useState('');

   // Location state
   const [pickupLocation, setPickupLocation] = useState<Location | null>(null);
   const [destinationLocation, setDestinationLocation] = useState<Location | null>(null);
   const [pickupInput, setPickupInput] = useState('');
   const [destinationInput, setDestinationInput] = useState('');

   // Stops state
   const [stops, setStops] = useState<Array<{ location: Location | null; input: string }>>([]);

   // Product state
   const [products, setProducts] = useState<RideSearchResult[]>([]);
   const [selectedProductId, setSelectedProductId] = useState<string>('');

   // Success state
   const [bookingSuccess, setBookingSuccess] = useState(false);
   const [createdRideId, setCreatedRideId] = useState<string | null>(null);

   // API hooks
   const searchMutation = useSearchRides();
   const createRideMutation = useCreateRide();
   const ridersQuery = useListRiders({
      page: 1,
      limit: 50,
      search: riderSearchQuery,
   });

   // Auto-trigger search when locations are selected
   useEffect(() => {
      if (pickupLocation && destinationLocation) {
         const stopsLocations = stops
            .filter(stop => stop.location !== null)
            .map(stop => stop.location!);

         searchMutation.mutate(
            {
               pickup: pickupLocation,
               destination: destinationLocation,
               stops: stopsLocations.length > 0 ? stopsLocations : undefined,
            },
            {
               onSuccess: response => {
                  setProducts(response.data);
                  if (response.data.length === 0 || !response.data[0]?.cityProduct?.id) {
                     toast.error('No available products found for this route');
                     setSelectedProductId('');
                  } else {
                     // Auto-select first product - use cityProduct.id
                     setSelectedProductId(response.data[0].cityProduct.id);
                  }
               },
               onError: (error: any) => {
                  console.error('Search error:', error);
                  toast.error(error?.response?.data?.message || 'Failed to search for rides');
                  setProducts([]);
                  setSelectedProductId('');
               },
            }
         );
      } else {
         // Clear products if either location is cleared
         setProducts([]);
         setSelectedProductId('');
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
   }, [pickupLocation, destinationLocation, stops, searchMutation.mutate]);

   const handlePickupSelect = (location: Location) => {
      setPickupLocation(location);
   };

   const handleDestinationSelect = (location: Location) => {
      setDestinationLocation(location);
   };

   const handleStopSelect = (index: number, location: Location) => {
      const newStops = [...stops];
      newStops[index].location = location;
      setStops(newStops);
   };

   const handleStopInputChange = (index: number, value: string) => {
      const newStops = [...stops];
      newStops[index].input = value;
      setStops(newStops);
   };

   const addStop = () => {
      if (stops.length < 5) {
         setStops([...stops, { location: null, input: '' }]);
      }
   };

   const removeStop = (index: number) => {
      setStops(stops.filter((_, i) => i !== index));
   };

   const handleRiderTypeChange = (value: string) => {
      setRiderType(value as RiderType);
      // Clear rider data when switching types
      setSelectedRider(null);
      setRiderSearchQuery('');
      setRiderComboboxOpen(false);
      setNewRiderName('');
      setNewRiderPhone('');
      setNewRiderEmail('');
   };

   const handleBookRide = () => {
      // Validate locations and product
      if (!pickupLocation || !destinationLocation || !selectedProductId) {
         toast.error('Please fill in all location and product fields');
         return;
      }

      // Validate rider selection
      if (riderType === 'existing' && !selectedRider) {
         toast.error('Please select an existing rider');
         return;
      }

      if (riderType === 'new') {
         if (!newRiderName || !newRiderPhone) {
            toast.error('Please fill in rider name and phone number');
            return;
         }
      }

      withPermission(RBAC_PERMISSIONS.BOOK_RIDE.CREATE, () => {
         const stopsLocations = stops
            .filter(stop => stop.location !== null)
            .map(stop => stop.location!);

         createRideMutation.mutate(
            {
               pickup: pickupLocation,
               destination: destinationLocation,
               stops: stopsLocations.length > 0 ? stopsLocations : undefined,
               cityProductId: selectedProductId,
               riderId: riderType === 'existing' && selectedRider ? selectedRider.id : undefined,
               riderMeta: {
                  name:
                     riderType === 'existing' && selectedRider
                        ? `${selectedRider.firstName} ${selectedRider.lastName}`
                        : newRiderName,
                  phoneNumber:
                     riderType === 'existing' && selectedRider
                        ? selectedRider.user.phoneNumber
                        : newRiderPhone,
                  email:
                     riderType === 'existing' && selectedRider
                        ? selectedRider.user.email || undefined
                        : newRiderEmail || undefined,
               },
            },
            {
               onSuccess: response => {
                  toast.success('Ride booked successfully!');
                  setBookingSuccess(true);
                  setCreatedRideId(response.data.id);
                  // Don't clear form yet - show success card first
               },
               onError: (error: any) => {
                  console.error('Booking error:', error);
                  toast.error(error?.response?.data?.message || 'Failed to book ride');
               },
            }
         );
      });
   };

   const handleViewRideDetails = () => {
      if (createdRideId) {
         router.push(`/dashboard/rides?rideId=${createdRideId}`);
      }
   };

   const handleBookAnother = () => {
      // Clear all form data
      setBookingSuccess(false);
      setCreatedRideId(null);
      setRiderType('existing');
      setSelectedRider(null);
      setRiderSearchQuery('');
      setRiderComboboxOpen(false);
      setNewRiderName('');
      setNewRiderPhone('');
      setNewRiderEmail('');
      setPickupLocation(null);
      setDestinationLocation(null);
      setPickupInput('');
      setDestinationInput('');
      setStops([]);
      setProducts([]);
      setSelectedProductId('');
   };

   const isSearching = searchMutation.isPending;
   const isBooking = createRideMutation.isPending;
   const productsAvailable = products.length > 0;

   const riderValid =
      riderType === 'existing' ? selectedRider !== null : newRiderName && newRiderPhone;

   const canBook =
      pickupLocation && destinationLocation && selectedProductId && riderValid && !isBooking;

   // Show success card if booking was successful
   if (bookingSuccess && createdRideId) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex justify-between items-center'>
               <h2 className='text-2xl font-semibold text-gray-900'>Book a Ride</h2>
            </div>

            <Card className='p-6 max-w-2xl'>
               <div className='flex flex-col items-center gap-4 py-8'>
                  <div className='rounded-full bg-green-100 p-4'>
                     <svg
                        className='h-8 w-8 text-green-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                     >
                        <path
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           strokeWidth={2}
                           d='M5 13l4 4L19 7'
                        />
                     </svg>
                  </div>
                  <h3 className='text-xl font-semibold text-gray-900'>
                     Ride Created Successfully!
                  </h3>
                  <p className='text-sm text-gray-600 text-center'>
                     The ride has been booked. You can view the ride details or book another ride.
                  </p>
                  <div className='flex gap-3 mt-4'>
                     <Button onClick={handleViewRideDetails} size='lg'>
                        View Ride Details
                     </Button>
                     <Button onClick={handleBookAnother} variant='outline' size='lg'>
                        Book Another Ride
                     </Button>
                  </div>
               </div>
            </Card>
         </div>
      );
   }

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Book a Ride</h2>
         </div>

         <Card className='p-6 max-w-2xl'>
            <div className='space-y-6'>
               {/* Rider Selection */}
               <div className='flex flex-col gap-4'>
                  <Label>Select Rider Type *</Label>
                  <RadioGroup
                     value={riderType}
                     onValueChange={handleRiderTypeChange}
                     className='flex flex-row gap-6'
                  >
                     <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='existing' id='existing' />
                        <Label htmlFor='existing' className='font-normal cursor-pointer'>
                           Existing Rider
                        </Label>
                     </div>
                     <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='new' id='new' />
                        <Label htmlFor='new' className='font-normal cursor-pointer'>
                           New Rider
                        </Label>
                     </div>
                  </RadioGroup>

                  {/* Existing Rider Selection */}
                  {riderType === 'existing' && (
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='rider-select'>Search and Select Rider *</Label>
                        <Popover open={riderComboboxOpen} onOpenChange={setRiderComboboxOpen}>
                           <PopoverTrigger asChild>
                              <Button
                                 ref={riderTriggerRef}
                                 variant='outline'
                                 role='combobox'
                                 aria-expanded={riderComboboxOpen}
                                 className='w-full justify-between'
                              >
                                 {selectedRider ? (
                                    <div className='flex flex-col items-start'>
                                       <span>{`${selectedRider.firstName} ${selectedRider.lastName}`}</span>
                                       <span className='text-xs text-gray-500'>
                                          {selectedRider.user.phoneNumber}
                                       </span>
                                    </div>
                                 ) : (
                                    <span className='text-muted-foreground'>Search riders...</span>
                                 )}
                                 <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                              </Button>
                           </PopoverTrigger>
                           <PopoverContent
                              className='p-0'
                              align='start'
                              style={{ width: riderTriggerRef.current?.offsetWidth }}
                           >
                              <Command shouldFilter={false}>
                                 <CommandInput
                                    placeholder='Search by name, email, or phone...'
                                    value={riderSearchQuery}
                                    onValueChange={setRiderSearchQuery}
                                 />
                                 <CommandList>
                                    <CommandEmpty>
                                       {ridersQuery.isLoading ? 'Loading...' : 'No riders found.'}
                                    </CommandEmpty>
                                    <CommandGroup>
                                       {ridersQuery.data?.data &&
                                          ridersQuery.data.data.slice(0, 10).map(rider => (
                                             <CommandItem
                                                className='w-full'
                                                key={rider.id}
                                                value={rider.id}
                                                onSelect={() => {
                                                   setSelectedRider(rider);
                                                   setRiderComboboxOpen(false);
                                                }}
                                             >
                                                <Check
                                                   className={cn(
                                                      'mr-2 h-4 w-4',
                                                      selectedRider?.id === rider.id
                                                         ? 'opacity-100'
                                                         : 'opacity-0'
                                                   )}
                                                />
                                                <div className='flex flex-col'>
                                                   <span>{`${rider.firstName} ${rider.lastName}`}</span>
                                                   <span className='text-xs text-gray-500'>
                                                      {rider.user.phoneNumber}
                                                   </span>
                                                </div>
                                             </CommandItem>
                                          ))}
                                    </CommandGroup>
                                 </CommandList>
                              </Command>
                           </PopoverContent>
                        </Popover>
                     </div>
                  )}

                  {/* New Rider Form */}
                  {riderType === 'new' && (
                     <div className='flex flex-col gap-4'>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-name'>Rider Name *</Label>
                           <Input
                              id='new-rider-name'
                              placeholder='Enter rider name'
                              value={newRiderName}
                              onChange={e => setNewRiderName(e.target.value)}
                           />
                        </div>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-phone'>Phone Number *</Label>
                           <PhoneInput
                              id='new-rider-phone'
                              international
                              countryCallingCodeEditable={false}
                              defaultCountry='IN'
                              value={newRiderPhone}
                              limitMaxLength={true}
                              onChange={value => setNewRiderPhone(value || '')}
                              className='flex h-10 outline-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0 w-full rounded-md border border-input bg-background px-3 py-0 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                           />
                        </div>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-email'>Email (Optional)</Label>
                           <Input
                              id='new-rider-email'
                              type='email'
                              placeholder='<EMAIL>'
                              value={newRiderEmail}
                              onChange={e => setNewRiderEmail(e.target.value)}
                           />
                        </div>
                     </div>
                  )}
               </div>
               {/* Pickup Location */}
               <LocationSearch
                  label='Pickup Location *'
                  placeholder='Search for pickup location...'
                  value={pickupInput}
                  onLocationSelect={handlePickupSelect}
                  onInputChange={setPickupInput}
               />

               {/* Stops Section */}
               <div className='flex flex-col gap-3'>
                  <div className='flex items-center justify-between'>
                     <Label>Stops (Optional)</Label>
                     <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addStop}
                        disabled={stops.length >= 5}
                        className='flex items-center gap-1'
                     >
                        <Plus className='h-4 w-4' />
                        Add Stop {stops.length >= 5 && '(Max 5)'}
                     </Button>
                  </div>

                  {stops.map((stop, index) => (
                     <div key={index} className='flex gap-2 items-end'>
                        <div className='flex-1'>
                           <LocationSearch
                              label={`Stop ${index + 1}`}
                              placeholder='Search for stop location...'
                              value={stop.input}
                              onLocationSelect={location => handleStopSelect(index, location)}
                              onInputChange={value => handleStopInputChange(index, value)}
                              biasLocation={pickupLocation}
                           />
                        </div>
                        <Button
                           type='button'
                           variant='ghost'
                           size='icon'
                           onClick={() => removeStop(index)}
                           className='mb-0.5'
                        >
                           <X className='h-4 w-4' />
                        </Button>
                     </div>
                  ))}
               </div>

               {/* Destination Location */}
               <LocationSearch
                  label='Destination Location *'
                  placeholder='Search for destination...'
                  value={destinationInput}
                  onLocationSelect={handleDestinationSelect}
                  onInputChange={setDestinationInput}
                  biasLocation={pickupLocation}
               />

               {/* Product Selection */}
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='product'>Select Product *</Label>
                  <Select
                     value={selectedProductId}
                     onValueChange={setSelectedProductId}
                     disabled={!productsAvailable || isSearching}
                  >
                     <SelectTrigger id='product' className='w-full'>
                        <SelectValue
                           placeholder={
                              isSearching
                                 ? 'Searching for available products...'
                                 : !pickupLocation || !destinationLocation
                                 ? 'Select pickup and destination first'
                                 : productsAvailable
                                 ? 'Select a product'
                                 : 'No products available for this route'
                           }
                        />
                     </SelectTrigger>
                     <SelectContent>
                        {products
                           .filter(product => product?.cityProduct?.id)
                           .map(product => (
                              <SelectItem
                                 key={product.cityProduct.id}
                                 value={product.cityProduct.id}
                              >
                                 <div className='flex items-center justify-between gap-4 w-full'>
                                    <span>{product.name}</span>
                                    <span className='text-sm text-gray-500'>
                                       ₹{product.price?.toFixed(2) ?? '0.00'}
                                    </span>
                                 </div>
                              </SelectItem>
                           ))}
                     </SelectContent>
                  </Select>
                  {isSearching && (
                     <div className='flex items-center gap-2 text-sm text-gray-500'>
                        <Spinner className='h-4 w-4' />
                        <span>Searching for available products...</span>
                     </div>
                  )}
               </div>

               {/* Book Ride Button */}
               <Button onClick={handleBookRide} disabled={!canBook} className='w-full' size='lg'>
                  {isBooking ? (
                     <>
                        <Spinner className='mr-2 h-4 w-4' />
                        Booking Ride...
                     </>
                  ) : (
                     'Book Ride'
                  )}
               </Button>
            </div>
         </Card>
      </div>
   );
}

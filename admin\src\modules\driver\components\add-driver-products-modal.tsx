'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Spinner } from '@/components/ui/spinner';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useAvailableCityProducts, useListDriverProducts } from '../api/driver-product-queries';
import { useAddCityProductsToDriver } from '../api/driver-product-mutations';

interface AddDriverProductsModalProps {
   vehicleId: string;
   driverCityId: string;
   driverCityName: string;
}

export function AddDriverProductsModal({ vehicleId, driverCityId, driverCityName }: AddDriverProductsModalProps) {
   const [open, setOpen] = useState(false);
   const [selectedOptions, setSelectedOptions] = useState<Option[]>([]);

   // Use driver's city directly - no need to fetch all cities

   // Get city products for driver's city
   const availableCityProductsQuery = useAvailableCityProducts(driverCityId, {
      page: 1,
      limit: 1000,
   });

   // Get current vehicle products to filter out already assigned ones
   const driverProductsQuery = useListDriverProducts(vehicleId, {
      page: 1,
      limit: 100,
   });

   const addProductsMutation = useAddCityProductsToDriver(vehicleId);

   // Get currently assigned cityProductIds for this driver
   const assignedCityProductIds = new Set(
      driverProductsQuery.data?.data?.map(driverProduct => driverProduct.cityProductId) || []
   );

   // Convert CityProduct to MultipleSelector Option format, filtering out already assigned and disabled ones
   const cityProductOptions: Option[] = (availableCityProductsQuery.data?.data || [])
      .filter(cityProduct => 
         cityProduct.isEnabled && // Only show enabled city products
         !assignedCityProductIds.has(cityProduct.id) // Exclude already assigned products
      )
      .map(cityProduct => ({
         value: cityProduct.id,
         label: `${cityProduct.product?.name || 'Unknown'} (${
            cityProduct.vehicleType?.name || 'Unknown'
         })`,
         cityProductId: cityProduct.id,
      }));

   const handleSubmit = () => {
      if (selectedOptions.length === 0) return;

      const cityProductIds = selectedOptions.map((option: any) => option.cityProductId);

      addProductsMutation.mutate(
         {
            driverVehicleId: vehicleId,
            cityProductIds,
         },
         {
            onSuccess: () => {
               setOpen(false);
               setSelectedOptions([]);
            },
         }
      );
   };

   // No longer needed - using driver's city directly

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         setSelectedOptions([]);
      }
   };

   const isCityProductsLoading =
      availableCityProductsQuery.isLoading || driverProductsQuery.isLoading;
   const isSubmitting = addProductsMutation.isPending;
   const hasAvailableCityProducts = cityProductOptions.length > 0;

   return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
         <DialogTrigger asChild>
            <Button variant={'outline'} className='flex items-center gap-2'>
               <Plus className='w-4 h-4' />
               Add Products
            </Button>
         </DialogTrigger>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Add Products to Vehicle</DialogTitle>
               <DialogDescription>
                  Select products available in {driverCityName} to add to this vehicle.
               </DialogDescription>
            </DialogHeader>

            <div className='py-4 space-y-4'>
               {/* Driver City Display */}
               <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
                  <div className='flex items-center gap-2'>
                     <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                     <span className='text-sm font-medium text-blue-900'>Driver's City: {driverCityName}</span>
                  </div>
                  <p className='text-xs text-blue-700 mt-1'>Products will be added from this city</p>
               </div>

               {/* City Products Selection */}
                  <div className='space-y-2'>
                     <Label htmlFor='cityProducts'>Select City Products</Label>
                     {isCityProductsLoading ? (
                        <div className='flex items-center justify-center py-4'>
                           <Spinner size='sm' className='mr-2' />
                           <span className='text-sm text-gray-600'>Loading city products...</span>
                        </div>
                     ) : (
                        <>
                           <MultipleSelector
                              value={selectedOptions}
                              onChange={setSelectedOptions}
                              defaultOptions={cityProductOptions}
                              placeholder='Select city products to add...'
                              emptyIndicator={
                                 <p className='text-center text-sm text-gray-500'>
                                    No city products found
                                 </p>
                              }
                              commandProps={{
                                 label: 'Select city products',
                              }}
                           />
                           {!hasAvailableCityProducts ? (
                              <div className='text-xs text-gray-500'>
                                 No products available for assignment.
                              </div>
                           ) : (
                              <div className='text-xs text-gray-500'>
                                 Selected: {selectedOptions.length} product
                                 {selectedOptions.length !== 1 ? 's' : ''}
                              </div>
                           )}
                        </>
                     )}
                  </div>
            </div>

            <DialogFooter>
               <Button
                  variant='outline'
                  onClick={() => handleOpenChange(false)}
                  disabled={isSubmitting}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={selectedOptions.length === 0 || isSubmitting}
                  className='min-w-[100px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Adding...
                     </div>
                  ) : (
                     `Add ${selectedOptions.length || ''} Product${
                        selectedOptions.length !== 1 ? 's' : ''
                     }`
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

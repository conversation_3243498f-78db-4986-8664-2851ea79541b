import { Module } from '@nestjs/common';
import { DriverEarningsController } from './driver-earnings.controller';
import { DriverEarningsModule as SharedDriverEarningsModule } from '@shared/shared/modules/driver-earnings/driver-earnings.module';
import { PaymentModule } from '@shared/shared/modules/payment/payment.module';
import { DriverAccountModule } from '@shared/shared/modules/driver-account/driver-account.module';
import { DriverDueModule } from '@shared/shared/modules/driver-due/driver-due.module';
import { UserProfileModule as SharedUserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { DriverEarningsReportService } from './services/driver-earnings-report.service';

@Module({
  providers: [DriverEarningsReportService],
  imports: [
    SharedDriverEarningsModule,
    PaymentModule,
    DriverAccountModule,
    DriverDueModule,
    SharedUserProfileModule,
  ],
  controllers: [DriverEarningsController],
})
export class DriverEarningsModule {}

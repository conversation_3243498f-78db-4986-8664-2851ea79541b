// Product interface for API responses
export interface Product {
  id: string;
  vehicleTypeId: string;
  productServiceId: string;
  name: string;
  description?: string | null;
  icon?: string | null;
  passengerLimit: number;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  // Related objects
  vehicleType?: {
    id: string;
    name: string;
  };
  productService?: {
    id: string;
    name: string;
  };
}

// API response structure for listing products
export interface ListProductResponse {
  success: boolean;
  message: string;
  data: Product[];
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  timestamp: number;
}

// API response structure for single product
export interface ProductResponse {
  success: boolean;
  message: string;
  data: Product;
  timestamp: number;
}

// Request for creating product
export interface CreateProductRequest {
  vehicleTypeId: string;
  productServiceId: string;
  name: string;
  description?: string;
  icon?: string;
  passengerLimit: number;
}

// Request for updating product
export interface UpdateProductRequest {
  vehicleTypeId?: string;
  productServiceId?: string;
  name?: string;
  description?: string;
  icon?: string;
  passengerLimit?: number;
}

// Parameters for listing products with pagination
export interface ListProductParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  vehicleTypeId?: string;
  productServiceId?: string;
  isEnabled?: string;
  isDisabled?: string;
}

// Dropdown option interface for vehicle types and product services
export interface DropdownOption {
  id: string;
  name: string;
}

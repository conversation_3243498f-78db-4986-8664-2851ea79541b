'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateZoneType, useUpdateZoneType } from '../api/mutations';
import { useGetZoneType, useGetAlgorithms } from '../api/queries';
import { ZoneAlgorithm } from '../types/zone-type';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const zoneTypeSchema = z.object({
   name: z
      .string()
      .min(1, 'Zone type name is required')
      .min(2, 'Zone type name must be at least 2 characters')
      .max(100, 'Zone type name must not exceed 100 characters')
      .regex(/^[a-zA-Z\s]+$/, 'Zone type name can only contain letters and spaces'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
   algorithm: z.enum(['CITY', 'AIRPORT', 'HIGHWAY', 'SUBURBAN', 'RURAL'] as const, {
      message: 'Algorithm is required',
   }),
});

type ZoneTypeFormValues = z.infer<typeof zoneTypeSchema>;

interface ZoneTypeModalProps {
   zoneTypeId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const ZoneTypeModal = ({
   zoneTypeId,
   isOpen,
   onClose,
   mode = 'create',
}: ZoneTypeModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const createZoneTypeMutation = useCreateZoneType();
   const updateZoneTypeMutation = useUpdateZoneType();
   const zoneTypeQuery = useGetZoneType(zoneTypeId || null);
   const algorithmsQuery = useGetAlgorithms();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<ZoneTypeFormValues>({
      resolver: zodResolver(zoneTypeSchema),
      defaultValues: {
         name: '',
         description: '',
         algorithm: undefined,
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   const isDataLoading = zoneTypeQuery.isLoading || algorithmsQuery.isLoading;

   // Reset form when zoneTypeId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && zoneTypeQuery.data?.data && modalOpen && !isDataLoading) {
         const zoneType = zoneTypeQuery.data.data;
         reset({
            name: zoneType.name,
            description: zoneType.description || '',
            algorithm: zoneType.algorithm,
         });
      } else if (mode === 'create') {
         reset({
            name: '',
            description: '',
            algorithm: 'CITY' as ZoneAlgorithm,
         });
      }
   }, [zoneTypeQuery.data, reset, mode, modalOpen, isDataLoading]);

   const onSubmit = async (data: ZoneTypeFormValues) => {
      try {
         const payload = {
            name: data.name,
            description: data.description || undefined,
            // algorithm: data.algorithm,
            algorithm: 'CITY' as ZoneAlgorithm,
         };

         if (mode === 'create') {
            createZoneTypeMutation.mutate(payload, {
               onSuccess: () => {
                  toast.success('Zone type created successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['zone-types'] });
               },
            });
         } else if (mode === 'edit' && zoneTypeId) {
            updateZoneTypeMutation.mutate(
               { id: zoneTypeId, ...payload },
               {
                  onSuccess: () => {
                     toast.success('Zone type updated successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['zone-types'] });
                     queryClient.invalidateQueries({
                        queryKey: ['zone-type', zoneTypeId],
                     });
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
   };

   const isLoading =
      mode === 'create' ? createZoneTypeMutation.isPending : updateZoneTypeMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && zoneTypeQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>
                     Please wait while we load the zone type data.
                  </DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && zoneTypeQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load zone type data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load zone type data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-md max-h-[90vh] overflow-y-auto'
      >
         <DialogHeader>
            <DialogTitle>
               {mode === 'create' ? 'Create New Zone Type' : 'Edit Zone Type'}
            </DialogTitle>
            <DialogDescription>
               {mode === 'create'
                  ? 'Add a new zone type to the system'
                  : 'Update the zone type information'}
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Zone Type Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. Downtown City Zone, Airport Terminal Zone'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='description'>Description</Label>
               <Controller
                  control={control}
                  name='description'
                  render={({ field }) => (
                     <Textarea
                        id='description'
                        placeholder='Enter zone type description'
                        {...field}
                        className='w-full min-h-[80px] resize-none'
                     />
                  )}
               />
               {errors.description && <ErrorMessage error={errors.description} />}
            </div>

            {/* <div className='flex flex-col gap-2'>
               <Label htmlFor='algorithm'>Algorithm *</Label>
               <Controller
                  control={control}
                  name='algorithm'
                  render={({ field }) => (
                     <Select
                        key={`algorithm-${selectKey}`}
                        value={field.value}
                        onValueChange={field.onChange}
                     >
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='Select algorithm type' />
                        </SelectTrigger>
                        <SelectContent>
                           {algorithmsQuery.data?.data?.map((algorithm: ZoneAlgorithm) => (
                              <SelectItem key={algorithm} value={algorithm}>
                                 {algorithm}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  )}
               />
               {errors.algorithm && <ErrorMessage error={errors.algorithm} />}
            </div> */}

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : mode === 'create' ? (
                     'Create Zone Type'
                  ) : (
                     'Update Zone Type'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, return button and dialog separately
   if (mode === 'create' && isOpen === undefined) {
      return (
         <>
            <Button
               className='cursor-pointer'
               variant='outline'
               onClick={() =>
                  withPermission(RBAC_PERMISSIONS.ZONE_TYPE.CREATE, () => setModalOpen(true))
               }
            >
               <Plus />
               Add Zone Type
            </Button>
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
               {content}
            </Dialog>
         </>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};

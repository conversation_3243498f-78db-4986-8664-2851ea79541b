import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
   CreateCityRequest,
   UpdateCityRequest,
  ChangeCityStatusRequest,
  CityCreateResponse,
  CityUpdateResponse,
  CityStatusChangeResponse,
} from '../types/city';

/**
 * Hook for creating a new city
 */
export const useCreateCity = () => {
  return useMutation({
    mutationFn: async (data: CreateCityRequest): Promise<CityCreateResponse> => {
      return apiClient.post('/cities', data);
    },
  });
};

/**
 * Hook for updating a city
 */
export const useUpdateCity = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: { id: string } & UpdateCityRequest): Promise<CityUpdateResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/cities/${id}`, payload);
    },
    onSuccess: (response, variables) => {
      const { id } = variables;
      const updatedCity = response.data;
      
      // Update the specific city cache
      queryClient.setQueryData(['city', id], response);
      
      // Update cities list cache if it exists
      queryClient.setQueryData(['cities-all'], (oldData: any) => {
        if (!oldData?.data) return oldData;
        
        return {
          ...oldData,
          data: oldData.data.map((city: any) => 
            city.id === id ? updatedCity : city
          ),
        };
      });
    },
  });
};

/**
 * Hook for updating city status (active/inactive)
 */
export const useUpdateCityStatus = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & ChangeCityStatusRequest): Promise<CityStatusChangeResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/cities/${id}/status`, payload);
    },
  });
};
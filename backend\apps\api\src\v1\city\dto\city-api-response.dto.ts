import { ApiProperty } from '@nestjs/swagger';
import { CityResponseDto } from './city-response.dto';

export class CityCreateApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City created successfully' })
  message!: string;

  @ApiProperty({
    type: CityResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: '<PERSON><PERSON>',
      icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
      state: 'Kerala',
      country: 'India',
      status: 'active',
      meta: { colour: '#6668' },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityListApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Cities retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [CityResponseDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Kochi',
        icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
        state: 'Kerala',
        country: 'India',
        status: 'active',
        meta: { colour: '#6668' },
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        deletedAt: null,
      },
    ],
  })
  data!: CityResponseDto[];

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: CityResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Kochi',
      icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
      state: 'Kerala',
      country: 'India',
      status: 'active',
      meta: { colour: '#6668' },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityUpdateApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City updated successfully' })
  message!: string;

  @ApiProperty({
    type: CityResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Kochi',
      icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
      state: 'Kerala',
      country: 'India',
      status: 'active',
      meta: { colour: '#6668' },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityDeleteApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City deleted successfully' })
  message!: string;

  @ApiProperty({
    type: CityResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Kochi',
      icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
      state: 'Kerala',
      country: 'India',
      status: 'active',
      meta: { colour: '#6668' },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: '2023-12-01T10:30:00Z',
    },
  })
  data!: CityResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityStatusChangeApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City status changed successfully' })
  message!: string;

  @ApiProperty({
    type: CityResponseDto,
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Kochi',
      icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
      state: 'Kerala',
      country: 'India',
      status: 'inactive',
      meta: { colour: '#6668' },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2023-12-01T10:00:00Z',
      deletedAt: null,
    },
  })
  data!: CityResponseDto;

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

export class CityPaginatedApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Cities paginated successfully' })
  message!: string;

  @ApiProperty({
    type: [CityResponseDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Kochi',
        icon: 'https://signed-url.amazonaws.com/uploads/cities/kochi-icon.png',
        state: 'Kerala',
        country: 'India',
        status: 'active',
        meta: { colour: '#6668' },
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        deletedAt: null,
      },
    ],
  })
  data!: CityResponseDto[];

  @ApiProperty({
    example: {
      page: 1,
      limit: 10,
      total: 25,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: false,
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  @ApiProperty({ example: 1701428400000 })
  timestamp!: number;
}

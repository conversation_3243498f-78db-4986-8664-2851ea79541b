'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { Search, X } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';

export interface CommissionsFiltersProps {
  onSearchChange: (search: string) => void;
  search: string;
}

export function CommissionsFilters({
  onSearchChange,
  search,
  isLoading,
}: CommissionsFiltersProps & { isLoading?: boolean }) {
  const [searchValue, setSearchValue] = useState(search || '');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local search state when props change
  useEffect(() => {
    setSearchValue(search || '');
  }, [search]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Show searching indicator
    setIsSearching(true);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout
    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
      searchTimeoutRef.current = null;
      setIsSearching(false);
    }, 500); // 500ms debounce time
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Clear any pending timeouts
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setIsSearching(false);
    setSearchValue('');
    onSearchChange('');
  };

  // Check if any filters are active
  const hasActiveFilters = !!search;

  return (
    <div className='flex flex-col space-y-4 mb-4'>
      <div className='flex justify-between items-center'>
        {/* Search field */}
        <div className='flex gap-2 items-center'>
          {/* Search Input */}
          <div className='relative w-[200px]'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
            <Input
              placeholder='Search commissions...'
              value={searchValue}
              onChange={handleSearchChange}
              className='pl-8'
            />
            {(isSearching || (isLoading && searchValue)) && (
              <div className='absolute right-2.5 top-2.5 text-gray-500'>
                <Spinner className='h-4 w-4 text-primary' />
              </div>
            )}
            {searchValue && !isSearching && !isLoading && (
              <button
                onClick={() => {
                  if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                    searchTimeoutRef.current = null;
                  }
                  setIsSearching(false);
                  setSearchValue('');
                  onSearchChange('');
                }}
                className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
              >
                <X className='h-4 w-4' />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Clear Filters Button - Below the filters */}
      {hasActiveFilters && (
        <div className='flex justify-end'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleClearFilters}
            className='text-gray-700 border-gray-300'
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
}

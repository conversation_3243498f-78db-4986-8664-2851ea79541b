# Tukxi Transportation Platform - Development Guide

## Project Overview

Tukxi is a comprehensive transportation platform consisting of multiple interconnected applications:
- **Admin Dashboard** (Next.js 15 with TypeScript)
- **Backend API** (NestJS with Prisma ORM)
- **Mobile Apps** (Flutter - Client & Driver apps)

## Architecture Overview

### Monorepo Structure
```
tukxi/
├── admin/          # Next.js 15 admin dashboard
├── backend/        # NestJS API with Prisma
├── mobile/
│   ├── tukxi_client/  # Flutter client app
│   └── tukxi_driver/  # Flutter driver app
├── docker-compose.yml # Development environment
└── CLAUDE.md       # This file
```

### Tech Stack

#### Admin Dashboard (`/admin`)
- **Framework**: Next.js 15.3.5 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4 with shadcn/ui components
- **State Management**: Zustand 5.0.6
- **Data Fetching**: TanStack Query 5.83.0 with Axios
- **Forms**: React Hook Form 7.60.0 with Zod validation
- **Package Manager**: pnpm 9.15.5

#### Backend (`/backend`)
- **Framework**: NestJS 11.0.1
- **Language**: TypeScript 5.7.3
- **Database**: PostgreSQL with Prisma ORM 6.12.0
- **Cache**: Redis (ioredis 5.6.1)
- **Authentication**: JWT with Passport.js
- **File Storage**: AWS S3
- **API Documentation**: Swagger/OpenAPI
- **Package Manager**: pnpm 10.12.1

#### Mobile Apps (`/mobile`)
- **Framework**: Flutter 3.8+
- **State Management**: 
  - Client: Riverpod 2.6.1
  - Driver: Provider 6.1.5
- **HTTP Client**: Dio (client) / HTTP (driver)
- **Authentication**: Firebase Auth with social login

## Development Setup

### Prerequisites
- Node.js 20+ (for admin & backend)
- pnpm (package manager)
- Docker & Docker Compose
- Flutter SDK 3.8+ (for mobile apps)
- PostgreSQL 15 (via Docker)
- Redis (via Docker)

### Environment Setup

1. **Start development environment:**
```bash
docker-compose up -d  # Starts PostgreSQL & Redis
```

2. **Backend setup:**
```bash
cd backend
pnpm install
pnpm prisma:generate
pnpm prisma:migrate
pnpm prisma:seed  # Optional: seed database
pnpm dev:api      # Starts on http://localhost:3000
```

3. **Admin dashboard setup:**
```bash
cd admin
pnpm install
pnpm dev          # Starts on http://localhost:3000
```

4. **Mobile apps setup:**
```bash
cd mobile/tukxi_client
flutter pub get
flutter run

cd mobile/tukxi_driver
flutter pub get  
flutter run
```

### Common Development Commands

#### Backend
```bash
# Development
pnpm dev:api              # Start API in watch mode
pnpm start:debug          # Start with debugging
pnpm lint                 # ESLint check
pnpm format               # Prettier format

# Database
pnpm prisma:generate      # Generate Prisma client
pnpm prisma:migrate       # Run migrations
pnpm prisma:studio        # Open Prisma Studio
pnpm pmg                  # Migrate + Generate (shortcut)

# Testing
pnpm test                 # Unit tests
pnpm test:e2e            # E2E tests
pnpm test:cov            # Coverage report
```

#### Admin Dashboard
```bash
# Development  
pnpm dev                  # Start with Turbopack
pnpm build               # Production build
pnpm start               # Start production server
pnpm lint                # Next.js linter
```

#### Mobile Apps
```bash
# Client app
cd mobile/tukxi_client
flutter run              # Debug mode
flutter build apk        # Build APK
flutter build ios        # Build iOS

# Driver app  
cd mobile/tukxi_driver
flutter run              # Debug mode
flutter build apk        # Build APK
flutter build ios        # Build iOS
```

## Key Architecture Patterns

### Backend (NestJS)

#### Module Structure
- **Monorepo**: Apps (`api`) + Shared libraries (`libs/shared`)
- **Layered Architecture**: Controllers → Services → Repositories
- **Domain Modules**: Auth, User Profile, Driver KYC, Vehicle Management, etc.

#### Key Modules
```
backend/apps/api/src/v1/
├── auth/                 # Authentication & authorization
├── user-profile/         # User management
├── driver-kyc/          # Driver verification
├── driver-vehicle/      # Vehicle management  
├── city/                # Location management
├── country/             # Country data
└── file-upload/         # File handling
```

#### Database Schema (Prisma)
- **Users**: Core user accounts with roles
- **UserProfile**: Extended user information
- **AuthCredential**: Authentication data
- **DriverKyc**: Driver verification documents
- **DriverVehicle**: Vehicle registration
- **City/Country**: Location hierarchy

#### Configuration
- Environment-based config service (`AppConfigService`)
- Support for development, staging, production
- JWT tokens, OAuth (Google/Apple), AWS S3, Redis

### Admin Dashboard (Next.js)

#### Architecture Patterns
- **App Router**: Next.js 15 with app directory structure
- **Module-based**: Features organized by domain (auth, driver, etc.)
- **Component Library**: shadcn/ui with custom styling
- **API Integration**: TanStack Query for server state

#### Key Modules
```
admin/src/modules/
├── auth/                # Login, OTP, password reset
├── driver/              # Driver management CRUD
└── [future modules]     # User management, vehicles, etc.
```

#### State Management
- **Server State**: TanStack Query with React Query DevTools
- **Client State**: Zustand stores (auth, UI state)
- **Form State**: React Hook Form with Zod validation

#### Design System
- **UI Library**: shadcn/ui components with Radix UI primitives  
- **Styling**: Tailwind CSS 4 with custom design tokens
- **Style Guide**: Comprehensive guidelines in `/admin/style-guideline.md`
- **Theme**: Clean, minimalistic design with consistent spacing

### Mobile Apps (Flutter)

#### Client App (`tukxi_client`)
- **Architecture**: Clean Architecture with feature-based modules
- **State Management**: Riverpod for reactive state
- **Navigation**: go_router 16.0.0
- **Environment**: Staging/Production builds with flavor support

#### Driver App (`tukxi_driver`)  
- **Architecture**: Feature-based with provider pattern
- **State Management**: Provider 6.1.5
- **UI**: Custom widgets with responsive design
- **Camera/ML**: Face detection for driver verification

## API Structure

### Base URL
- Development: `http://localhost:3000/api/v1`
- Swagger Docs: `http://localhost:3000/docs`

### Key Endpoints

#### Authentication
```
POST /auth/email-signup      # Email registration
POST /auth/phone-signup      # Phone registration  
POST /auth/verify-email      # Email OTP verification
POST /auth/verify-phone      # Phone OTP verification
POST /auth/login             # Email/phone login
POST /auth/refresh-token     # Token refresh
POST /auth/forgot-password   # Password reset
```

#### Driver Management
```
GET    /admin/driver         # List drivers (paginated)
POST   /admin/driver         # Create driver
GET    /admin/driver/:id     # Get driver details
PATCH  /admin/driver/:id     # Update driver
DELETE /admin/driver/:id     # Delete driver
```

#### File Management
```
POST /file-upload            # Upload files to S3
```

### Response Format
All APIs follow consistent response structure:
```typescript
{
  success: boolean;
  message: string;
  data?: any;
  errors?: ValidationError[];
  pagination?: PaginationMeta;
}
```

## Database Schema

### Core Tables
- **users**: Base user accounts
- **user_profiles**: Extended profile information  
- **auth_credentials**: Password hashes, OAuth data
- **refresh_tokens**: JWT refresh tokens
- **roles/permissions**: RBAC system

### Driver-Specific Tables
- **driver_kyc**: KYC verification documents
- **driver_vehicles**: Vehicle registrations
- **driver_vehicle_documents**: Vehicle verification docs
- **kyc_documents**: Document type definitions

### Location Tables
- **countries**: Country master data
- **cities**: City data with country relations
- **products**: Service types per city

## Development Guidelines

### Code Standards
- **TypeScript**: Strict mode enabled across all projects
- **ESLint**: Consistent linting rules
- **Prettier**: Automated code formatting
- **Conventional Commits**: Structured commit messages

### Backend Conventions
- **DTOs**: Input/output validation with class-validator
- **Error Handling**: Global exception filters
- **API Versioning**: URI-based versioning (`/api/v1/`)
- **Security**: Helmet, CORS, JWT authentication, rate limiting

### Frontend Conventions
- **Component Structure**: Atomic design principles
- **File Naming**: kebab-case for files, PascalCase for components
- **State Management**: Server state via React Query, client state via Zustand
- **Form Handling**: React Hook Form with Zod schemas

### Mobile Conventions
- **Architecture**: Feature-first folder structure
- **State Management**: Consistent patterns per app
- **API Integration**: Repository pattern with error handling
- **UI Consistency**: Reusable widget components

## Key Features

### Authentication System
- Multi-factor authentication (email/phone + OTP)
- Social login (Google, Apple)
- JWT-based session management
- Role-based access control (RBAC)

### Driver Management
- Driver registration with phone verification
- KYC document upload and verification
- Vehicle registration and document management
- Profile photo capture with face detection
- Admin approval workflow

### File Management
- AWS S3 integration for secure file storage
- Pre-signed URLs for direct uploads
- Image processing and validation
- Document categorization

## Environment Configuration

### Required Environment Variables

#### Backend (.env)
```bash
# Database
DATABASE_URL=postgresql://tukxi_db_user:password@localhost:5432/tukxi

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-secret-key
JWT_ACCESS_TOKEN_EXPIRY=15m
JWT_REFRESH_TOKEN_EXPIRY=7d

# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=your-bucket

# OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-secret
APPLE_CLIENT_ID=your-apple-client-id
```

#### Admin (.env)
```bash
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

## Documentation & Resources

### API Documentation
- Swagger UI: `http://localhost:3000/docs`
- Auto-generated from NestJS decorators
- Interactive API testing interface

### Style Guidelines  
- Admin UI guidelines: `/admin/style-guideline.md`
- Comprehensive design system documentation
- Component usage examples and patterns

### Implementation Plans
- Driver API integration: `/admin/docs/DRIVERS_API_IMPLEMENTATION_PLAN.md`
- Detailed implementation roadmaps for new features

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running via Docker
2. **Redis Connection**: Verify Redis container is running
3. **Prisma Issues**: Run `pnpm prisma:generate` after schema changes
4. **Mobile Build Issues**: Check Flutter SDK version compatibility
5. **CORS Errors**: Verify admin base URL matches backend CORS settings

### Development Tips
- Use `pnpm dev:api` for backend development with auto-reload
- Use `pnpm dev` with Turbopack for fast admin rebuilds
- Use Prisma Studio for database inspection: `pnpm prisma:studio`
- Check Swagger docs for API endpoint details
- Review style guidelines before implementing new UI components

## Future Claude Context

When working on this codebase:

1. **Always check style guidelines** before implementing UI changes
2. **Follow established patterns** in each module (auth, driver, etc.)
3. **Use existing types and schemas** - they're well-defined
4. **Respect the layered architecture** - Controllers → Services → Repositories
5. **Implement proper error handling** using established patterns
6. **Update API documentation** when adding new endpoints
7. **Follow the monorepo structure** - keep modules isolated but connected
8. **Use the established development commands** for consistency
9. **Check implementation plans** for guidance on complex features
10. **Maintain the clean, minimalistic design philosophy** throughout

The codebase is well-structured with clear separation of concerns, comprehensive type safety, and established patterns. Focus on extending existing patterns rather than introducing new architectural concepts.
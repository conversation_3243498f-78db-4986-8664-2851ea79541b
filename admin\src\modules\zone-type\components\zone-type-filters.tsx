'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, MapPin, Settings } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { useGetAlgorithms } from '../api/queries';
import { ZoneAlgorithm } from '../types/zone-type';

export interface ZoneTypeFiltersProps {
   onSearchChange: (search: string) => void;
   onAlgorithmChange: (algorithm: ZoneAlgorithm | undefined) => void;
   onStatusChange: (isActive: boolean | undefined) => void;
   search: string;
   algorithm: ZoneAlgorithm | undefined;
   isActive: boolean | undefined;
}

export function ZoneTypeFilters({
   onSearchChange,
   onAlgorithmChange,
   onStatusChange,
   search,
   algorithm,
   isActive,
   isLoading,
}: ZoneTypeFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [isSearching, setIsSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Fetch algorithms for the dropdown
   const algorithmsQuery = useGetAlgorithms();

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }

      setIsSearching(false);
      setSearchValue('');
      onSearchChange('');
      onAlgorithmChange(undefined);
      onStatusChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!search || !!algorithm || isActive !== undefined;

   return (
      <div className='flex flex-col space-y-4 mb-4'>
         <div className='flex justify-between items-center'>
            {/* Left container - Search field */}
            <div className='flex gap-2 items-center'>
               {/* Zone Type Name Search */}
               <div className='relative w-[200px]'>
                  <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search zone types...'
                     value={searchValue}
                     onChange={handleSearchChange}
                     className='pl-8'
                  />
                  {(isSearching || (isLoading && searchValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {searchValue && !isSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (searchTimeoutRef.current) {
                              clearTimeout(searchTimeoutRef.current);
                              searchTimeoutRef.current = null;
                           }
                           setIsSearching(false);
                           setSearchValue('');
                           onSearchChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Right container - Status and Algorithm filters and Clear button */}
            <div className='flex gap-2 items-center'>
               {/* Status Filter */}
               <Select
                  value={isActive === undefined ? 'all' : isActive ? 'true' : 'false'}
                  onValueChange={value => {
                     if (value === 'all') {
                        onStatusChange(undefined);
                     } else {
                        onStatusChange(value === 'true');
                     }
                  }}
               >
                  <SelectTrigger className='w-auto min-w-[100px] cursor-pointer'>
                     <Settings className='h-4 w-4 mr-1' />
                     <SelectValue placeholder='All Status' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='true'>Active</SelectItem>
                     <SelectItem value='false'>Inactive</SelectItem>
                  </SelectContent>
               </Select>

               {/* Algorithm Filter */}
               <Select
                  value={algorithm || 'all'}
                  onValueChange={value => onAlgorithmChange(value === 'all' ? undefined : value as ZoneAlgorithm)}
               >
                  <SelectTrigger className='w-auto min-w-[140px] cursor-pointer'>
                     <MapPin className='h-4 w-4 mr-1' />
                     <SelectValue placeholder='All Algorithms' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Algorithms</SelectItem>
                     {algorithmsQuery.data?.data?.map((alg: ZoneAlgorithm) => (
                        <SelectItem key={alg} value={alg}>
                           {alg}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>
            </div>
         </div>

         {/* Clear Filters Button - Below the filters */}
         {hasActiveFilters && (
            <div className='flex justify-end'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={handleClearFilters}
                  className='text-gray-700 border-gray-300'
               >
                  Clear Filters
               </Button>
            </div>
         )}
      </div>
   );
}
'use client';

import { Card } from '@/components/ui/card';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useListDriver } from '../api/queries';
import { useAllCities } from '@/modules/city/api/queries';
import { useListVehicleCategory } from '@/modules/vehicle-category/api/queries';
import { CreateDriver } from '../components/create-driver';
import { DriverFilters } from '../components/driver-filters';
import { DriverTable } from '../components/driver-table';

export function DriverPage() {
   const router = useRouter();
   const searchParams = useSearchParams();
   const pageParam = searchParams.get('page');
   const [page, setPage] = useState(pageParam ? parseInt(pageParam, 10) : 1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [cityId, setCityId] = useState<string | undefined>(undefined);
   const [name, setName] = useState<string | undefined>(undefined);
   const [email, setEmail] = useState<string | undefined>(undefined);
   const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);
   const [status, setStatus] = useState<string | undefined>(undefined);
   const [vehicleTypeId, setVehicleTypeId] = useState<string | undefined>(undefined);

   // preload cities and vehicle types
   useAllCities();
   useListVehicleCategory({});

   // Sync page state with URL params
   useEffect(() => {
      if (pageParam) {
         const parsedPage = parseInt(pageParam, 10);
         if (!isNaN(parsedPage) && parsedPage !== page) {
            setPage(parsedPage);
         }
      }
   }, [page, pageParam]);

   // Update URL when page changes
   const updatePage = (newPage: number) => {
      setPage(newPage);
      const params = new URLSearchParams(searchParams.toString());
      params.set('page', newPage.toString());
      router.push(`?${params.toString()}`, { scroll: false });
   };

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      updatePage(1);
   };

   const handleCityChange = (value: string | undefined) => {
      setCityId(value);
      updatePage(1);
   };

   const handleNameChange = (value: string | undefined) => {
      setName(value);
      updatePage(1);
   };

   const handleEmailChange = (value: string | undefined) => {
      setEmail(value);
      updatePage(1);
   };

   const handlePhoneChange = (value: string | undefined) => {
      setPhoneNumber(value);
      updatePage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setStatus(value);
      updatePage(1);
   };

   const handleVehicleTypeChange = (value: string | undefined) => {
      setVehicleTypeId(value);
      updatePage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setCityId(undefined);
      setName(undefined);
      setEmail(undefined);
      setPhoneNumber(undefined);
      setStatus(undefined);
      setVehicleTypeId(undefined);
      updatePage(1);
   };

   const listDriver = useListDriver({
      page,
      limit,
      search: search || undefined,
      cityId: cityId || undefined,
      name: name || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
      status: status || undefined,
      vehicleTypeId: vehicleTypeId || undefined,
   });

   // Calculate total driver count from API meta data
   const totalDrivers = listDriver.data?.meta?.total || 0;

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Drivers</h2>
            <div className='flex items-center gap-4'>
               {/* Driver Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Drivers</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalDrivers}
                     </span>
                  </div>
               </div>
               <CreateDriver />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <DriverFilters
               search={search}
               cityId={cityId}
               name={name}
               email={email}
               phoneNumber={phoneNumber}
               status={status}
               vehicleTypeId={vehicleTypeId}
               onSearchChange={handleSearchChange}
               onCityChange={handleCityChange}
               onNameChange={handleNameChange}
               onEmailChange={handleEmailChange}
               onPhoneChange={handlePhoneChange}
               onStatusChange={handleStatusChange}
               onVehicleTypeChange={handleVehicleTypeChange}
               isLoading={listDriver.isFetching && !listDriver.isLoading}
            />

            <DriverTable
               data={listDriver.data}
               isLoading={listDriver.isLoading}
               currentPage={page}
               onPageChange={updatePage}
               hasFilters={
                  !!search ||
                  !!cityId ||
                  !!name ||
                  !!email ||
                  !!phoneNumber ||
                  !!status ||
                  !!vehicleTypeId
               }
               hasSearch={!!search}
               hasStatus={false}
               hasLocation={!!cityId}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}

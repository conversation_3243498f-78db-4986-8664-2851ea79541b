import { errorParser } from '@/lib/error-parser';
import { cn } from '@/lib/utils';

export const ErrorMessage = ({
  error,
  className,
}: {
  error: unknown;
  className?: string;
}) => {
  return error ? (
    <p
      className={cn(
        'text-sm text-red-500 mt-1 break-words break-all overflow-wrap-anywhere',
        className,
      )}
    >
      {errorParser(error)}
    </p>
  ) : null;
};

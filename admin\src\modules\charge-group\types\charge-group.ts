// Charge group interface for API responses
export interface ChargeGroup {
  id: string;
  name: string;
  description?: string | null;
  identifier?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// API response structure for listing charge groups
export interface ListChargeGroupResponse {
  success: boolean;
  message: string;
  data: ChargeGroup[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  timestamp: number;
}

// API response structure for single charge group
export interface ChargeGroupResponse {
  success: boolean;
  message: string;
  data: ChargeGroup;
  timestamp: number;
}

// Request for creating charge group
export interface CreateChargeGroupRequest {
  name: string;
  description?: string;
  identifier?: string;
}

// Request for updating charge group
export interface UpdateChargeGroupRequest {
  name?: string;
  description?: string;
  identifier?: string;
}

// Parameters for listing charge groups with pagination
export interface ListChargeGroupParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  enabled?: boolean;
}
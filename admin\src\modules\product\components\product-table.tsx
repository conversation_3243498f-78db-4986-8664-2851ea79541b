'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { CircleAlert } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useDisableProduct, useEnableProduct } from '../api/mutations';
import { ListProductResponse, Product } from '../types/product';
import { ProductModal } from './product-modal';
import { ProductTableEmpty } from './product-table-empty';
import { ProductTableLoading } from './product-table-loading';
import { ProductToggleModal } from './product-toggle-modal';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   enableProductMutation,
   disableProductMutation,
   handleToggleClick,
   handleEditClick,
   productToToggle,
   withPermission,
}: {
   handleToggleClick: (product: Product) => void;
   handleEditClick: (id: string) => void;
   enableProductMutation: any;
   disableProductMutation: any;
   productToToggle: Product | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Product>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const product = row.original as Product;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium'>{product.name}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'vehicleTypeId',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Vehicle Type</div>
      ),
      cell: ({ row }) => {
         const product = row.original as Product;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {product.vehicleType?.name || product.vehicleTypeId}
               </div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'productServiceId',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Service</div>,
      cell: ({ row }) => {
         const product = row.original as Product;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {product.productService?.name || product.productServiceId}
               </div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const product = row.original as Product;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {product.description || 'No description'}
               </div>
            </div>
         );
      },
      size: 250,
   },
   {
      accessorKey: 'icon',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Icon</div>,
      cell: ({ row }) => {
         const product = row.original as Product;
         return (
            <div className='flex justify-center'>
               {product.icon ? (
                  <ErrorBoundary fallback={<CircleAlert className='scale-75 text-red-500' />}>
                     <div className='w-8 h-8 overflow-hidden rounded border border-gray-200 flex-shrink-0'>
                        <Image
                           src={product.icon}
                           alt={product.name}
                           width={32}
                           height={32}
                           className='w-full h-full object-cover'
                           onError={e => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                 parent.innerHTML =
                                    '<span class="text-xs text-gray-400">No icon</span>';
                              }
                           }}
                        />
                     </div>
                  </ErrorBoundary>
               ) : (
                  <span className='text-xs text-gray-400'>No icon</span>
               )}
            </div>
         );
      },
      size: 80,
   },
   {
      accessorKey: 'isEnabled',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const product = row.original as Product;

         return (
            <div className='flex justify-center'>
               <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                     product.isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}
               >
                  {product.isEnabled ? 'Active' : 'Inactive'}
               </span>
            </div>
         );
      },
      size: 100,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const product = row.original as Product;
         const isToggling =
            productToToggle?.id === product.id &&
            (enableProductMutation.isPending || disableProductMutation.isPending);

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.PRODUCT.EDIT, () =>
                        handleEditClick(product.id)
                     );
                  }}
               >
                  Edit
               </button>
               <button
                  className={`text-sm font-medium border px-3 py-1 rounded-md transition-colors bg-white cursor-pointer ${
                     product.isEnabled
                        ? 'text-red-600 hover:text-red-700 border-red-300 hover:border-red-400'
                        : 'text-green-600 hover:text-green-700 border-green-300 hover:border-green-400'
                  }`}
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.PRODUCT.STATUS_UPDATE, () =>
                        handleToggleClick(product)
                     );
                  }}
                  disabled={isToggling}
               >
                  {isToggling ? '...' : product.isEnabled ? 'Deactivate' : 'Activate'}
               </button>
            </div>
         );
      },
      size: 200,
   },
];

interface ProductTableProps {
   data: ListProductResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters?: boolean;
   hasSearch?: boolean;
   hasStatus?: boolean;
   onClearFilters?: () => void;
}

export function ProductTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters: _hasFilters,
   hasSearch: _hasSearch,
   hasStatus: _hasStatus,
   onClearFilters: _onClearFilters,
}: ProductTableProps) {
   const [productToToggle, setProductToToggle] = useState<Product | null>(null);
   const [productToEdit, setProductToEdit] = useState<string | null>(null);
   const enableProductMutation = useEnableProduct();
   const disableProductMutation = useDisableProduct();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleToggleClick = (product: Product) => {
      setProductToToggle(product);
   };

   const handleEditClick = (id: string) => {
      setProductToEdit(id);
   };

   const handleToggleConfirm = () => {
      if (!productToToggle) return;

      const mutation = productToToggle.isEnabled ? disableProductMutation : enableProductMutation;
      const action = productToToggle.isEnabled ? 'disabled' : 'enabled';

      mutation.mutate(productToToggle.id, {
         onSuccess: () => {
            toast.success(`Product ${action} successfully`);
            queryClient.invalidateQueries({ queryKey: ['products'] });
         },
         onSettled: () => {
            setProductToToggle(null);
         },
      });
   };

   const columns = getColumns({
      enableProductMutation,
      disableProductMutation,
      handleToggleClick,
      handleEditClick,
      productToToggle,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <ProductTableLoading />;
   }

   if (!data?.data?.length) {
      return <ProductTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         {/* Toggle Confirmation Modal */}
         <ProductToggleModal
            isOpen={!!productToToggle}
            onClose={() => setProductToToggle(null)}
            onConfirm={handleToggleConfirm}
            isLoading={enableProductMutation.isPending || disableProductMutation.isPending}
            productName={productToToggle?.name || ''}
            currentStatus={productToToggle?.isEnabled || false}
         />

         {/* Edit Modal */}
         <ProductModal
            mode='edit'
            productId={productToEdit}
            isOpen={!!productToEdit}
            onClose={() => setProductToEdit(null)}
         />
      </div>
   );
}

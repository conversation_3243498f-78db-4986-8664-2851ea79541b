import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateZoneTypeRequest, ZoneTypeResponse, UpdateZoneTypeRequest } from '../types/zone-type';

/**
 * Hook for creating a new zone type
 */
export const useCreateZoneType = () => {
   return useMutation({
      mutationFn: async (data: CreateZoneTypeRequest): Promise<ZoneTypeResponse> => {
         // Always set isActive to true on creation as per requirements
         const payload = { ...data, isActive: true };
         return apiClient.post('/zone-types', payload);
      },
   });
};

/**
 * Hook for updating a zone type
 */
export const useUpdateZoneType = () => {
   return useMutation({
      mutationFn: async (
         data: { id: string } & UpdateZoneTypeRequest
      ): Promise<ZoneTypeResponse> => {
         const { id, ...payload } = data;
         // Don't include isActive in update payload as per requirements
         return apiClient.put(`/zone-types/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a zone type (soft delete)
 */
export const useDeleteZoneType = () => {
   return useMutation({
      mutationFn: async (
         id: string
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.delete(`/zone-types/${id}`);
      },
   });
};

/**
 * Hook for activating a zone type
 * Uses the UPDATE endpoint with isActive: true
 */
export const useActivateZoneType = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<ZoneTypeResponse> => {
         return apiClient.put(`/zone-types/${id}`, { isActive: true });
      },
   });
};

/**
 * Hook for deactivating a zone type
 * Uses the UPDATE endpoint with isActive: false
 */
export const useDeactivateZoneType = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<ZoneTypeResponse> => {
         return apiClient.put(`/zone-types/${id}`, { isActive: false });
      },
   });
};

/**
 * Hook for restoring a soft-deleted zone type
 */
export const useRestoreZoneType = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<ZoneTypeResponse> => {
         return apiClient.put(`/zone-types/${id}/restore`);
      },
   });
};

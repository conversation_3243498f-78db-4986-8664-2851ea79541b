'use client';

import { CustomPagination } from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { Trash2, Power, PowerOff, Package, CircleAlert, DollarSign } from 'lucide-react';
import Image from 'next/image';
import { CityProduct, ListCityProductResponse } from '../types/city-product';
import { CityProductTableEmpty } from './city-product-table-empty';
import { CityProductTableLoading } from './city-product-table-loading';
import { ErrorBoundary } from 'react-error-boundary';

interface CityProductTableProps {
   data?: ListCityProductResponse;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   onRemoveClick: (cityProduct: CityProduct) => void;
   onToggleClick: (cityProduct: CityProduct) => void;
   onManageFairsClick: (cityProduct: CityProduct) => void;
}

const getColumns = ({
   onRemoveClick,
   onToggleClick,
   onManageFairsClick,
}: {
   onRemoveClick: (cityProduct: CityProduct) => void;
   onToggleClick: (cityProduct: CityProduct) => void;
   onManageFairsClick: (cityProduct: CityProduct) => void;
}): ColumnDef<CityProduct>[] => [
   {
      accessorKey: 'product',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Product</div>,
      cell: ({ row }) => {
         const cityProduct = row.original as CityProduct;
         return (
            <div className='flex items-center gap-3'>
               {cityProduct.product?.icon ? (
                  <ErrorBoundary fallback={<CircleAlert className='scale-75 text-red-500' />}>
                     <div className='w-10 h-10 relative rounded-lg overflow-hidden bg-gray-100 flex-shrink-0'>
                        <Image
                           src={cityProduct.product?.icon}
                           alt={cityProduct.product?.name || 'Product'}
                           fill
                           className='object-cover'
                           sizes='40px'
                        />
                     </div>
                  </ErrorBoundary>
               ) : (
                  <div className='w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0'>
                     <Package className='w-5 h-5 text-gray-400' />
                  </div>
               )}
               <div className='min-w-0 flex-1'>
                  <div className='font-medium text-sm text-gray-900 truncate'>
                     {cityProduct.product?.name || 'Unknown Product'}
                  </div>
                  {cityProduct.product?.description && (
                     <div className='text-xs text-gray-500 truncate'>
                        {cityProduct.product.description}
                     </div>
                  )}
               </div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'product.productService.name',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Product Service</div>
      ),
      cell: ({ row }) => {
         const product = row.original?.product;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{product?.productService?.name ?? ''}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'vehicleType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Vehicle Type</div>
      ),
      cell: ({ row }) => {
         const cityProduct = row.original as CityProduct;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {cityProduct.vehicleType?.name || 'Unknown Type'}
               </div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'product.isEnabled',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Product Status</div>
      ),
      cell: ({ row }) => {
         const cityProduct = row.original as CityProduct;
         const productEnabled = cityProduct.product?.isEnabled ?? true;
         return (
            <Badge
               variant='secondary'
               className={`text-xs ${
                  productEnabled ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
               }`}
            >
               {productEnabled ? 'Active' : 'InActive'}
            </Badge>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'isEnabled',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const cityProduct = row.original as CityProduct;
         return (
            <Badge
               variant='secondary'
               className={`text-xs ${
                  cityProduct.isEnabled ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
               }`}
            >
               {cityProduct.isEnabled ? 'Active' : 'Inactive'}
            </Badge>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'actions',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const cityProduct = row.original as CityProduct;
         return (
            <div className='flex items-center justify-end gap-2'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onManageFairsClick(cityProduct)}
                  className='flex items-center gap-1 text-purple-600 hover:text-purple-700'
               >
                  <DollarSign className='w-3 h-3' />
                  Manage Fairs
               </Button>

               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onToggleClick(cityProduct)}
                  className={`flex items-center gap-1 ${
                     cityProduct.isEnabled
                        ? 'text-red-600 hover:text-red-700'
                        : 'text-green-600 hover:text-green-700'
                  }`}
               >
                  {cityProduct.isEnabled ? (
                     <>
                        <PowerOff className='w-3 h-3' />
                        Disable
                     </>
                  ) : (
                     <>
                        <Power className='w-3 h-3' />
                        Enable
                     </>
                  )}
               </Button>

               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onRemoveClick(cityProduct)}
                  className='text-red-600 hover:text-red-700'
               >
                  <Trash2 className='w-3 h-3' />
               </Button>
            </div>
         );
      },
      size: 200,
   },
];

export function CityProductTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   onRemoveClick,
   onToggleClick,
   onManageFairsClick,
}: CityProductTableProps) {
   const columns = getColumns({
      onRemoveClick,
      onToggleClick,
      onManageFairsClick,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <CityProductTableLoading />;
   }

   if (!data?.data?.length) {
      return <CityProductTableEmpty hasFilters={hasFilters} />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPreviousPage}
            />
         )}
      </div>
   );
}

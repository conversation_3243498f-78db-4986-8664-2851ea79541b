'use client';

import { ErrorBoundary } from 'react-error-boundary';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, ArrowLeft, MapPin } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { useGetCity } from '../api/queries';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CityBoundaryDrawer } from '../components/boundary-drawer';
import { CityProductsTab } from '../components/city-products-tab';
import { CityZonesTab } from '../components/city-zones-tab';
import { CityAdminsTab } from '../components/city-admins-tab';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { cn } from '@/lib/utils';

export function CityDetailsPage() {
   const params = useParams<{ id: string }>();
   const searchParams = useSearchParams();
   const cityId = params.id;
   const returnPage = searchParams.get('returnPage');
   const [activeTab, setActiveTab] = useState('boundaries');
   const { data: city, isLoading, error } = useGetCity(cityId);
   const { hasAnyPermission } = useRoleBasedAccess();
   const allCityAdminPermisssions = Object.keys(RBAC_PERMISSIONS.CITY_ADMIN).map(
      permissionKey => (RBAC_PERMISSIONS.CITY_ADMIN as any)[permissionKey]
   );
   const hasAnyCityAdminPermissions = hasAnyPermission(allCityAdminPermisssions);

   // Construct back URL with returnPage if available
   const backUrl = returnPage ? `/dashboard/cities?page=${returnPage}` : '/dashboard/cities';

   if (isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='animate-pulse'>
               <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
               <div className='h-32 bg-gray-200 rounded mb-4'></div>
               <div className='h-96 bg-gray-200 rounded'></div>
            </div>
         </div>
      );
   }

   if (error || !city?.data) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='flex items-center gap-4 mb-6'>
               <Link href={backUrl}>
                  <Button variant='ghost' size='sm' className='gap-2'>
                     <ArrowLeft className='h-4 w-4' />
                     Back to Cities
                  </Button>
               </Link>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>City Not Found</h3>
               <p className='text-gray-600'>
                  The city you're looking for doesn't exist or has been removed.
               </p>
            </Card>
         </div>
      );
   }

   const cityData = city.data;

   return (
      <div className='flex flex-1 flex-col space-y-6 px-6 py-6'>
         {/* Clean Header */}
         <div className='flex items-center gap-3'>
            <Link href={backUrl}>
               <Button
                  variant='ghost'
                  size='sm'
                  className='gap-2 text-gray-600 hover:text-gray-900'
               >
                  <ArrowLeft className='h-4 w-4' />
                  Back to Cities
               </Button>
            </Link>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>Cities</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>City Details</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-900 font-medium'>{cityData.name}</span>
         </div>

         {/* Compact City Header */}
         <Card className='p-6'>
            <div className='flex items-center gap-4'>
               {/* City Icon Placeholder */}
               <div className='flex-shrink-0'>
                  <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center'>
                     <MapPin className='w-6 h-6 text-gray-400' />
                  </div>
               </div>

               {/* Clean City Info */}
               <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-3 mb-2'>
                     <h1 className='text-xl font-semibold text-gray-900 truncate'>
                        {cityData.name}
                     </h1>
                     <Badge
                        variant='secondary'
                        className={`text-xs ${
                           cityData.status === 'active'
                              ? 'bg-green-100 text-green-700'
                              : 'bg-red-100 text-red-700'
                        }`}
                     >
                        {cityData.status === 'active' ? 'Active' : 'Inactive'}
                     </Badge>
                  </div>

                  <div className='flex items-center gap-6 text-sm text-gray-600'>
                     {cityData.state && (
                        <div className='flex items-center gap-2'>
                           <span className='text-gray-500'>State:</span>
                           <span>{cityData.state}</span>
                        </div>
                     )}
                     {cityData.country && (
                        <div className='flex items-center gap-2'>
                           <span className='text-gray-500'>Country:</span>
                           <span>{cityData.country}</span>
                        </div>
                     )}
                  </div>
               </div>
            </div>
         </Card>

         {/* Clean Tabs */}
         <Card>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
               <div className='border-b border-gray-200 px-6 pt-4 pb-0'>
                  <TabsList
                     className={cn(
                        'grid grid-cols-3 w-full max-w-3xl bg-gray-50',
                        hasAnyCityAdminPermissions && 'grid-cols-4'
                     )}
                  >
                     <TabsTrigger value='boundaries' className='text-sm'>
                        City Boundaries
                     </TabsTrigger>
                     <TabsTrigger value='products' className='text-sm'>
                        Products
                     </TabsTrigger>
                     <TabsTrigger value='zones' className='text-sm'>
                        City Zones
                     </TabsTrigger>

                     {hasAnyCityAdminPermissions && (
                        <TabsTrigger value='admins' className='text-sm'>
                           Admins
                        </TabsTrigger>
                     )}
                  </TabsList>
               </div>

               <div className='p-6'>
                  <TabsContent value='boundaries' className='mt-0'>
                     {/* <div className='h-96 w-full rounded-lg overflow-hidden border'> */}
                     <div className='h-[60vh] w-full rounded-lg overflow-hidden border'>
                        <ErrorBoundary
                           fallback={
                              <div className='h-full w-full bg-gray-100 flex items-center justify-center'>
                                 <div className='text-center'>
                                    <MapPin className='w-12 h-12 text-gray-400 mx-auto mb-2' />
                                    <p className='text-gray-600'>Map could not be loaded</p>
                                 </div>
                              </div>
                           }
                        >
                           <CityBoundaryDrawer cityDetails={cityData} />
                        </ErrorBoundary>
                     </div>
                  </TabsContent>

                  <TabsContent value='products' className='mt-0'>
                     <CityProductsTab cityId={cityId} />
                  </TabsContent>

                  <TabsContent value='zones' className='mt-0'>
                     <CityZonesTab cityId={cityId} />
                  </TabsContent>

                  {hasAnyCityAdminPermissions && (
                     <TabsContent value='admins' className='mt-0'>
                        <CityAdminsTab cityId={cityId} />
                     </TabsContent>
                  )}
               </div>
            </Tabs>
         </Card>
      </div>
   );
}

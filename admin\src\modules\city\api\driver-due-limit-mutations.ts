import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  SetDriverDueLimitRequest,
  UpdateDriverDueLimitRequest,
  DriverDueLimitResponse,
} from '../types/driver-due-limit';

/**
 * Hook for creating a new driver due limit configuration
 */
export const useCreateDriverDueLimit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: SetDriverDueLimitRequest
    ): Promise<DriverDueLimitResponse> => {
      return apiClient.post('/admin/config/driver-due-limit', data);
    },
    onSuccess: (response) => {
      // Invalidate the query for this specific city
      queryClient.invalidateQueries({
        queryKey: ['driver-due-limit', response.data.cityId],
      });
    },
  });
};

/**
 * Hook for updating an existing driver due limit configuration
 */
export const useUpdateDriverDueLimit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      cityId: string;
      payload: UpdateDriverDueLimitRequest;
    }): Promise<DriverDueLimitResponse> => {
      const { cityId, payload } = data;
      return apiClient.put(`/admin/config/driver-due-limit/${cityId}`, payload);
    },
    onSuccess: (response) => {
      // Invalidate the query for this specific city
      queryClient.invalidateQueries({
        queryKey: ['driver-due-limit', response.data.cityId],
      });
    },
  });
};

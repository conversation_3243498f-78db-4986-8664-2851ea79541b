'use client';

import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ProductToggleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  productName: string;
  currentStatus: boolean; // true = enabled, false = disabled
}

export const ProductToggleModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  productName,
  currentStatus,
}: ProductToggleModalProps) => {
  const action = currentStatus ? 'disable' : 'enable';
  const actionCapitalized = currentStatus ? 'Disable' : 'Enable';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-sm'>
        <DialogHeader>
          <DialogTitle>{actionCapitalized} Product</DialogTitle>
          <DialogDescription>
            Are you sure you want to {action} the product "{productName}"?
            {currentStatus 
              ? ' This will make the product unavailable to users.'
              : ' This will make the product available to users.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className='flex gap-3 pt-4'>
          <Button 
            type='button' 
            variant='outline' 
            onClick={onClose} 
            className='flex-1'
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            type='button' 
            onClick={onConfirm} 
            className={`flex-1 ${
              !currentStatus 
                ? 'bg-green-600 hover:bg-green-700 text-white' 
                : ''
            }`}
            variant={currentStatus ? 'destructive' : 'default'}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                {actionCapitalized}...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              actionCapitalized
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

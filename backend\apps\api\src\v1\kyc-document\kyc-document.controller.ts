import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { KycDocumentService } from '../../../../../libs/shared/src/modules/kyc-document/kyc-document.service';
import { CreateKycDocumentDto } from './dto/create-kyc-document.dto';
import { UpdateKycDocumentDto } from './dto/update-kyc-document.dto';
import { KycDocumentResponseDto } from './dto/kyc-document-response.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('KYC Documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('kyc-documents')
export class KycDocumentController {
  constructor(private readonly kycDocumentService: KycDocumentService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new KYC document' })
  @ApiResponse({
    status: 201,
    description: 'KYC document created successfully',
    type: ApiResponseDto<KycDocumentResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Country not found',
    type: ApiErrorResponseDto,
  })
  async createKycDocument(@Body() createKycDocumentDto: CreateKycDocumentDto) {
    const kycDocument =
      await this.kycDocumentService.createKycDocument(createKycDocumentDto);
    return {
      success: true,
      message: 'KYC document created successfully',
      data: kycDocument,
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated KYC documents' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiResponse({
    status: 200,
    description: 'KYC documents retrieved successfully',
    type: PaginatedResponseDto<KycDocumentResponseDto>,
  })
  async getKycDocuments(@Query() paginationDto: PaginationDto) {
    const result = await this.kycDocumentService.paginateKycDocuments(
      paginationDto.page,
      paginationDto.limit,
      paginationDto,
    );
    return {
      success: true,
      message: 'KYC documents retrieved successfully',
      data: result.data,
      meta: result.meta,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all KYC documents' })
  @ApiResponse({
    status: 200,
    description: 'All KYC documents retrieved successfully',
    type: ApiResponseDto<KycDocumentResponseDto[]>,
  })
  async getAllKycDocuments() {
    const kycDocuments = await this.kycDocumentService.findAllKycDocuments();
    return {
      success: true,
      message: 'All KYC documents retrieved successfully',
      data: kycDocuments,
    };
  }

  @Get('country/:countryId')
  @ApiOperation({ summary: 'Get KYC documents by country ID' })
  @ApiResponse({
    status: 200,
    description: 'KYC documents for country retrieved successfully',
    type: ApiResponseDto<KycDocumentResponseDto[]>,
  })
  @ApiResponse({
    status: 404,
    description: 'Country not found',
    type: ApiErrorResponseDto,
  })
  async getKycDocumentsByCountry(@Param('countryId') countryId: string) {
    const kycDocuments =
      await this.kycDocumentService.findKycDocumentsByCountryId(countryId);
    return {
      success: true,
      message: 'KYC documents for country retrieved successfully',
      data: kycDocuments,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get KYC document by ID' })
  @ApiResponse({
    status: 200,
    description: 'KYC document retrieved successfully',
    type: ApiResponseDto<KycDocumentResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document not found',
    type: ApiErrorResponseDto,
  })
  async getKycDocumentById(@Param('id') id: string) {
    const kycDocument = await this.kycDocumentService.findKycDocumentById(id);
    return {
      success: true,
      message: 'KYC document retrieved successfully',
      data: kycDocument,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update KYC document' })
  @ApiResponse({
    status: 200,
    description: 'KYC document updated successfully',
    type: ApiResponseDto<KycDocumentResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  async updateKycDocument(
    @Param('id') id: string,
    @Body() updateKycDocumentDto: UpdateKycDocumentDto,
  ) {
    const kycDocument = await this.kycDocumentService.updateKycDocument(
      id,
      updateKycDocumentDto,
    );
    return {
      success: true,
      message: 'KYC document updated successfully',
      data: kycDocument,
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete KYC document (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'KYC document deleted successfully',
    type: ApiResponseDto<KycDocumentResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document not found',
    type: ApiErrorResponseDto,
  })
  async deleteKycDocument(@Param('id') id: string) {
    const kycDocument = await this.kycDocumentService.deleteKycDocument(id);
    return {
      success: true,
      message: 'KYC document deleted successfully',
      data: kycDocument,
    };
  }
}

'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListTaxGroup } from '../api/queries';
import { TaxGroupFilters } from '../components/tax-group-filters';
import { TaxGroupModal } from '../components/tax-group-modal';
import { TaxGroupTable } from '../components/tax-group-table';

export function TaxGroupPage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setPage(1);
   };

   const listTaxGroupQuery = useListTaxGroup({
      page,
      limit,
      search: search || undefined,
   });

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Tax Groups</h2>
            <div className='flex items-center gap-4'>
               <TaxGroupModal mode='create' />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <TaxGroupFilters
               search={search}
               onSearchChange={handleSearchChange}
               isLoading={listTaxGroupQuery.isFetching && !listTaxGroupQuery.isLoading}
            />

            <TaxGroupTable
               data={listTaxGroupQuery.data}
               isLoading={listTaxGroupQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search}
               hasSearch={!!search}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}

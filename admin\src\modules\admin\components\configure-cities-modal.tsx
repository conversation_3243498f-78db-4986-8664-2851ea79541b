'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Spinner } from '@/components/ui/spinner';
import { Settings } from 'lucide-react';
import { useState, useEffect, useMemo } from 'react';
import { useAllCities } from '@/modules/city/api/queries';
import { useGetAdminProfile } from '../api/queries';
import { apiClient } from '@/lib/api-client';
import { Admin } from '../types/admin';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';

interface ConfigureCitiesModalProps {
   admin: Admin;
   open: boolean;
   onOpenChange: (open: boolean) => void;
}

export function ConfigureCitiesModal({ admin, open, onOpenChange }: ConfigureCitiesModalProps) {
   const [selectedOptions, setSelectedOptions] = useState<Option[]>([]);
   const [initialCityIds, setInitialCityIds] = useState<string[]>([]);
   const [isSubmitting, setIsSubmitting] = useState(false);

   const queryClient = useQueryClient();

   const adminProfileQuery = useGetAdminProfile(admin.id, open);
   const allCitiesQuery = useAllCities();

   // Convert all cities to MultipleSelector Option format
   const cityOptions: Option[] = useMemo(() => {
      return (allCitiesQuery.data?.data || []).map(city => ({
         value: city.id,
         label: city.name,
         cityId: city.id,
      }));
   }, [allCitiesQuery.data?.data]);

   // Set initial selected options when admin profile loads
   useEffect(() => {
      if (adminProfileQuery.data?.data?.cityAdmins) {
         const adminCities = adminProfileQuery.data.data.cityAdmins;
         const adminCityIds = adminCities.map(cityAdmin => cityAdmin.cityId);
         setInitialCityIds(adminCityIds);

         const initialOptions = adminCities.map(cityAdmin => ({
            value: cityAdmin.cityId,
            label: cityAdmin.city.name,
            cityId: cityAdmin.cityId,
         }));
         setSelectedOptions(initialOptions);
      }
   }, [adminProfileQuery.data?.data?.cityAdmins]);

   const handleSubmit = async () => {
      setIsSubmitting(true);
      try {
         const currentCityIds = selectedOptions.map((option: any) => option.cityId);
         const citiesToRemove = initialCityIds.filter(cityId => !currentCityIds.includes(cityId));
         const citiesToAdd = currentCityIds.filter(cityId => !initialCityIds.includes(cityId));

         const promises: Promise<any>[] = [];

         // Remove city assignments if needed
         for (const cityId of citiesToRemove) {
            const promise = apiClient.post(`/admin/sub-admin/cities/${cityId}/remove-admin`, {
               userProfileId: admin.id,
            });
            promises.push(promise);
         }

         // Add city assignments if needed
         for (const cityId of citiesToAdd) {
            const promise = apiClient.post(`/admin/sub-admin/cities/${cityId}/add-admin`, {
               adminIds: [admin.id],
            });
            promises.push(promise);
         }

         await Promise.all(promises);

         // Invalidate relevant queries
         queryClient.invalidateQueries({ queryKey: ['admin-profile', admin.id] });
         queryClient.invalidateQueries({ queryKey: ['admins'] });

         toast.success('City assignments updated successfully');
         onOpenChange(false);
      } catch (error: any) {
         console.error('Error updating city assignments:', error);
         toast.error(
            error?.response?.data?.message ||
            error?.message ||
            'Failed to update city assignments'
         );
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleClose = () => {
      onOpenChange(false);
      // Reset to initial state
      if (adminProfileQuery.data?.data?.cityAdmins) {
         const adminCities = adminProfileQuery.data.data.cityAdmins;
         const initialOptions = adminCities.map(cityAdmin => ({
            value: cityAdmin.cityId,
            label: cityAdmin.city.name,
            cityId: cityAdmin.cityId,
         }));
         setSelectedOptions(initialOptions);
      }
   };

   const isLoading = adminProfileQuery.isLoading || allCitiesQuery.isLoading;
   const hasChanges = useMemo(() => {
      const currentCityIds = selectedOptions.map((option: any) => option.cityId).sort();
      const originalCityIds = [...initialCityIds].sort();
      return JSON.stringify(currentCityIds) !== JSON.stringify(originalCityIds);
   }, [selectedOptions, initialCityIds]);

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle className='flex items-center gap-2'>
                  <Settings className='w-5 h-5' />
                  Configure Cities
               </DialogTitle>
               <DialogDescription>
                  Configure which cities {admin.firstName} {admin.lastName} is assigned to manage.
               </DialogDescription>
            </DialogHeader>

            <div className='py-4'>
               {isLoading ? (
                  <div className='flex items-center justify-center py-8'>
                     <Spinner className='mr-2' />
                     <span className='text-sm text-gray-600'>Loading cities...</span>
                  </div>
               ) : (
                  <div className='space-y-2'>
                     <Label htmlFor='cities'>Assigned Cities</Label>
                     <MultipleSelector
                        value={selectedOptions}
                        onChange={setSelectedOptions}
                        defaultOptions={cityOptions}
                        placeholder='Select cities to assign...'
                        emptyIndicator={
                           <p className='text-center text-sm text-gray-500'>No cities found</p>
                        }
                        commandProps={{
                           label: 'Select cities',
                        }}
                     />
                     <div className='text-xs text-gray-500 mt-1'>
                        Selected: {selectedOptions.length} cit{selectedOptions.length !== 1 ? 'ies' : 'y'}
                     </div>
                  </div>
               )}
            </div>

            <DialogFooter>
               <Button
                  variant='outline'
                  onClick={handleClose}
                  disabled={isSubmitting}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !hasChanges || isLoading}
                  className='min-w-[100px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Updating...
                     </div>
                  ) : (
                     'Update Cities'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
import { apiClient } from '@/lib/api-client';
import { toast } from '@/lib/toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  AddCityProductsToDriverRequest,
  RemoveCityProductsFromDriverRequest,
  UpdatePrimaryStatusRequest,
} from '../types/driver-product';

export const useAddCityProductsToDriver = (vehicleId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: AddCityProductsToDriverRequest): Promise<any> => {
      return apiClient.post('/driver-city-products/add-city-products', {
        driverVehicleId: vehicleId,
        cityProductIds: data.cityProductIds
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicle-products', vehicleId] });
      toast.success('City products added to vehicle successfully');
    },
  });
};

export const useRemoveCityProductsFromDriver = (vehicleId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: RemoveCityProductsFromDriverRequest): Promise<any> => {
      return apiClient.post('/driver-city-products/remove-city-products', {
        driverVehicleId: vehicleId,
        cityProductIds: data.cityProductIds
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicle-products', vehicleId] });
      toast.success('City products removed from vehicle successfully');
    },
  });
};

export const useUpdatePrimaryStatus = (vehicleId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: UpdatePrimaryStatusRequest): Promise<any> => {
      return apiClient.patch('/driver-city-products/update-primary-status', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicle-products', vehicleId] });
      toast.success('Primary status updated successfully');
    },
  });
};
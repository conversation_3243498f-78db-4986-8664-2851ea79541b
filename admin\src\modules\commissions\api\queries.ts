import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
  CommissionResponse,
  ListCommissionsParams,
  ListCommissionsResponse,
  AllCommissionsResponse,
} from '../types/commissions';

/**
 * Hook for listing commissions with pagination and filters
 */
export const useListCommissions = ({ page, limit, search }: ListCommissionsParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.COMMISSION.LIST),
    queryKey: ['commissions', page, limit, search],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListCommissionsResponse> => {
      return apiClient.get('/commissions', {
        params: { page, limit, search },
      });
    },
  });
};

/**
 * Hook for getting a single commission by ID (for edit mode)
 */
export const useGetCommission = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['commission', id],
    queryFn: (): Promise<CommissionResponse> => {
      return apiClient.get(`/commissions/${id || ''}`);
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.COMMISSION.EDIT),
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting all commissions without pagination (for dropdowns/selects)
 */
export const useGetAllCommissions = () => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    enabled: hasPermission(RBAC_PERMISSIONS.COMMISSION.LIST),
    queryKey: ['commissions-all'],
    queryFn: (): Promise<AllCommissionsResponse> => {
      return apiClient.get('/commissions/all');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

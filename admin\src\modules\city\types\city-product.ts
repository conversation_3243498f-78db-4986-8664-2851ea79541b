export interface CityProduct {
   id: string;
   cityId: string;
   productId: string;
   vehicleTypeId: string;
   isEnabled: boolean;
   createdAt: string;
   updatedAt: string;
   deletedAt?: string | null;
   city?: {
      id: string;
      name: string;
      state?: string;
      country?: string;
      status: string;
   };
   product?: {
      id: string;
      name: string;
      description?: string;
      identifier?: string;
      icon?: string;
      isEnabled: boolean;
      productService: {
         id: string;
         name: string;
      };
   };
   vehicleType?: {
      id: string;
      name: string;
      description?: string;
      image?: string;
   };
}

export interface ListCityProductParams {
   page?: number;
   limit?: number;
   search?: string;
   productName?: string;
   vehicleTypeId?: string;
}

export interface ListCityProductResponse {
   success: boolean;
   message: string;
   data: CityProduct[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

export interface CityProductResponse {
   success: boolean;
   message: string;
   data: CityProduct;
   timestamp: number;
}

export interface AddProductsToCityRequest {
   products: {
      productId: string;
      vehicleTypeId: string;
   }[];
}

export interface RemoveProductsFromCityRequest {
   products: {
      productId: string;
      vehicleTypeId: string;
   }[];
}

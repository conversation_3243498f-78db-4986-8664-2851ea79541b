// ============================================
// COORDINATE & BOUNDS INTERFACES
// ============================================
export interface Coordinate {
  lat: number;
  lng: number;
}

export interface RadarMapBounds {
  northeast: Coordinate;
  southwest: Coordinate;
}

// ============================================
// DRIVER DATA INTERFACES
// ============================================
export interface RadarDriver {
  id: string;
  lat: number;
  lng: number;
  status: 'active' | 'pending' | 'disabled' | 'inactive';
  cityProductId: string;
  productName: string;
  productIcon?: string | null;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================
export interface RadarMapData {
  drivers: RadarDriver[];
  total: number;
}

export interface RadarMapResponse {
  success: boolean;
  message: string;
  data: RadarMapData;
  timestamp: number;
}

// ============================================
// COMPONENT PROPS
// ============================================
export interface DriverMarkerProps {
  driver: RadarDriver;
  onClick: (driver: RadarDriver) => void;
}

export interface DriverInfoPopoverProps {
  driver: RadarDriver | null;
  isOpen: boolean;
  onClose: () => void;
  position: Coordinate | null;
}

export interface PollingIndicatorProps {
  isFetching: boolean;
  lastUpdated: number | null;
  nextUpdate: number;
  onManualRefresh: () => void;
  isPaused: boolean;
}

export interface MapControlsProps {
  drivers: RadarDriver[];
}

// ============================================
// UTILITY TYPES
// ============================================
export interface MapCenter {
  lat: number;
  lng: number;
  zoom: number;
}

export const KOCHI_CENTER: MapCenter = {
   lat: 9.9312,
   lng: 76.2673,
   zoom: 11,
};

export const MAP_CONSTANTS = {
   MAX_RADIUS_KM: 18,
   POLLING_INTERVAL_MS: 5000,
   MIN_ZOOM: 11,
   MAX_ZOOM: 18,
   DEFAULT_ZOOM: 11,
} as const;

// ============================================
// STATUS COLOR MAPPING
// ============================================
export const STATUS_COLORS = {
  active: '#10B981', // Green
  pending: '#F59E0B', // Amber
  inactive: '#6B7280', // Gray
  disabled: '#EF4444', // Red
} as const;

export type DriverStatus = keyof typeof STATUS_COLORS;

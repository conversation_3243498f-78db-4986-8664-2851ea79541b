import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import { env } from '@/config/env';
import { useAuthStore } from '@/store/auth-store';
import { resetStore } from '@/store/store-helpers';
import { toast } from './toast';

interface RefreshTokenResponse {
   success: boolean;
   message: string;
   data: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
      isProfileUpdated: boolean;
      emailVerified: boolean;
      phoneVerified: boolean;
      isPolicyAllowed: boolean;
      phoneNumber: null;
      email: string;
   };
}

interface ErrorResponse {
   message: 'Authentication failed';
   error: 'Unauthorized';
   statusCode: number; // 401
}

// Helper function to handle unauthorized user logout
const handleUnauthorizedUser = () => {
   resetStore();
   toast.error('Unauthorized user');
   window.location.href = '/';
};

// Request interceptor function
const requestInterceptor = (config: InternalAxiosRequestConfig) => {
   if (config.headers && typeof window !== 'undefined') {
      if (!navigator.onLine) {
         return Promise.reject({
            code: 'ERR_NETWORK',
            message: 'Network Error',
            isAxiosError: true,
            config,
            toJSON: () => ({}),
         });
      }

      const token = useAuthStore.getState().authToken;
      config.headers.authorization = `Bearer ${token || ''}`;
      config.headers['x-app-type'] = 'super_admin';
   }
   return config;
};

const requestErrorInterceptor = (error: AxiosError) => {
   return Promise.reject(error);
};

// Response interceptor function
const responseInterceptor = (response: AxiosResponse) => {
   return response.data;
};

// Response error interceptor function
export const responseErrorInterceptor = async (error: any, finalAction?: () => void) => {
   if (error.response?.status === 401) {
      // Skip 401 handling for login endpoint
      if (error.config?.url?.includes('/auth/password/login')) {
         return Promise.reject(error);
      }

      const refreshToken = useAuthStore.getState().refreshToken;
      const errorResponse = error.response?.data as ErrorResponse;
      const errorMsg = errorResponse?.error;

      if (refreshToken && errorMsg === 'Unauthorized') {
         try {
            const response = await axios({
               baseURL: env?.NEXT_PUBLIC_BASE_URL,
               url: '/auth/refresh-token',
               method: 'POST',
               data: {
                  refreshToken: refreshToken,
               },
               headers: {
                  'x-app-type': 'super_admin',
               },
            });

            const responseStatus = response.status;
            const refreshExpiryStatus = responseStatus === 401;

            if (refreshExpiryStatus) {
               handleUnauthorizedUser();
            }

            const refreshResponse = response.data as RefreshTokenResponse;

            const { accessToken: newAccessToken, refreshToken: newRefreshToken } =
               refreshResponse.data;
            useAuthStore.getState().setToken(newAccessToken, newRefreshToken);

            if (finalAction) {
               finalAction();
            } else {
               if (error.config) {
                  error.config.headers['authorization'] = `Bearer ${newAccessToken}`;
                  return apiClient(error.config);
               }
            }
         } catch (err) {
            console.error(err);
            handleUnauthorizedUser();
         }
      } else {
         handleUnauthorizedUser();
      }
   }
   return Promise.reject(error);
};

// Factory function to create API clients
export const createApiClient = (baseURL: string): AxiosInstance => {
   const client = axios.create({
      baseURL,
   });

   // Add interceptors
   client.interceptors.request.use(requestInterceptor, requestErrorInterceptor);
   client.interceptors.response.use(responseInterceptor, responseErrorInterceptor);

   return client;
};

export const apiClient = createApiClient(env?.NEXT_PUBLIC_BASE_URL || '');

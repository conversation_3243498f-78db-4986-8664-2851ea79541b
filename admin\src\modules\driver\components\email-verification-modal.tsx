'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Spinner } from '@/components/ui/spinner';
import { ErrorMessage } from '@/components/error-message';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useSendEmailVerification, useVerifyEmailOtp, useUpdateDriver } from '../api/mutations';

// Email validation schema
const emailVerificationSchema = z.object({
   email: z.string().min(1, 'Email is required').email('Please enter a valid email address'),
});

// OTP validation schema
const otpVerificationSchema = z.object({
   otp: z.string().min(4, 'OTP must be 4 digits').max(4, 'OTP must be 4 digits'),
});

type EmailVerificationFormValues = z.infer<typeof emailVerificationSchema>;
type OtpVerificationFormValues = z.infer<typeof otpVerificationSchema>;
type VerificationStep = 'confirm-email' | 'enter-otp';

interface EmailVerificationModalProps {
   isOpen: boolean;
   onClose: () => void;
   onSuccess: () => void;
   driverEmail: string;
   userProfileId: string;
   driverId: string;
}

export const EmailVerificationModal = ({
   isOpen,
   onClose,
   onSuccess,
   driverEmail,
   userProfileId,
   driverId,
}: EmailVerificationModalProps) => {
   const [currentStep, setCurrentStep] = useState<VerificationStep>('confirm-email');
   const [timeLeft, setTimeLeft] = useState(30);
   const [canResend, setCanResend] = useState(false);
   const [currentEmail, setCurrentEmail] = useState(driverEmail);

   const sendEmailVerificationMutation = useSendEmailVerification();
   const verifyEmailOtpMutation = useVerifyEmailOtp();
   const updateDriverMutation = useUpdateDriver();

   const emailForm = useForm<EmailVerificationFormValues>({
      resolver: zodResolver(emailVerificationSchema),
      defaultValues: {
         email: driverEmail,
      },
   });

   const otpForm = useForm<OtpVerificationFormValues>({
      resolver: zodResolver(otpVerificationSchema),
      defaultValues: {
         otp: '',
      },
   });

   const {
      control: emailControl,
      handleSubmit: handleEmailSubmit,
      formState: { errors: emailErrors },
      reset: resetEmailForm,
   } = emailForm;

   const {
      control: otpControl,
      handleSubmit: handleOtpSubmit,
      formState: { errors: otpErrors },
      setValue: setOtpValue,
      setFocus: setOtpFocus,
      reset: resetOtpForm,
   } = otpForm;

   // Reset form and state when modal opens
   useEffect(() => {
      if (isOpen) {
         resetEmailForm({ email: driverEmail });
         resetOtpForm();
         setCurrentEmail(driverEmail);
         setCurrentStep('confirm-email');
         setTimeLeft(30);
         setCanResend(false);
      }
   }, [isOpen, driverEmail, resetEmailForm, resetOtpForm]);

   // Countdown timer for resend
   useEffect(() => {
      if (currentStep === 'enter-otp' && timeLeft > 0) {
         const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
         return () => clearTimeout(timer);
      } else if (timeLeft === 0) {
         setCanResend(true);
      }
   }, [currentStep, timeLeft]);

   const handleSendVerification = (data: EmailVerificationFormValues) => {
      const payload = {
         userProfileId,
         email: data.email,
      };

      setCurrentEmail(data.email);
      sendEmailVerificationMutation.mutate(payload, {
         onSuccess: () => {
            toast.success('Verification email sent successfully');
            setCurrentStep('enter-otp');
            setTimeLeft(30);
            setCanResend(false);
         },
      });
   };

   const onSubmit = async (data: OtpVerificationFormValues) => {
      const verifyPayload = {
         userProfileId,
         email: currentEmail,
         otp: data.otp,
      };

      verifyEmailOtpMutation.mutate(verifyPayload, {
         onSuccess: () => {
            // If email was changed, update the driver's email
            if (currentEmail !== driverEmail) {
               updateDriverMutation.mutate(
                  {
                     id: driverId,
                     email: currentEmail,
                  },
                  {
                     onSuccess: () => {
                        toast.success('Email verified and updated successfully');
                        onSuccess();
                        onClose();
                     },
                     onSettled: () => {
                        onSuccess();
                        onClose();
                     },
                  }
               );
            } else {
               toast.success('Email verified successfully');
               onSuccess();
               onClose();
            }
         },
      });
   };

   const handleResendOtp = () => {
      if (!canResend) return;

      const payload = {
         userProfileId,
         email: currentEmail,
      };

      sendEmailVerificationMutation.mutate(payload, {
         onSuccess: () => {
            setOtpValue('otp', '');
            setOtpFocus('otp');
            toast.success('Verification email resent successfully');
            setTimeLeft(30);
            setCanResend(false);
         },
      });
   };

   const handleBack = () => {
      setCurrentStep('confirm-email');
      resetOtpForm();
   };

   const handleClose = () => {
      onClose();
      resetEmailForm({ email: driverEmail });
      resetOtpForm();
      setCurrentEmail(driverEmail);
      setCurrentStep('confirm-email');
      setTimeLeft(30);
      setCanResend(false);
   };

   const renderStepContent = () => {
      if (currentStep === 'confirm-email') {
         return (
            <Form {...emailForm}>
               <form onSubmit={handleEmailSubmit(handleSendVerification)} className='space-y-6'>
                  <FormField
                     control={emailControl}
                     name='email'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel htmlFor='email'>Email Address</FormLabel>
                           <FormControl>
                              <Input
                                 id='email'
                                 type='email'
                                 placeholder='Enter email address'
                                 {...field}
                                 className='w-full'
                              />
                           </FormControl>
                           <ErrorMessage error={emailErrors.email} />
                        </FormItem>
                     )}
                  />

                  <div className='flex gap-3'>
                     <Button
                        type='button'
                        variant='outline'
                        onClick={handleClose}
                        className='flex-1'
                     >
                        Cancel
                     </Button>
                     <Button
                        type='submit'
                        disabled={sendEmailVerificationMutation.isPending}
                        className='flex-1'
                     >
                        {sendEmailVerificationMutation.isPending ? (
                           <>
                              Sending...
                              <Spinner className='ml-2 h-4 w-4' />
                           </>
                        ) : (
                           'Send Verification Code'
                        )}
                     </Button>
                  </div>
               </form>
            </Form>
         );
      }

      return (
         <div className='space-y-6'>
            <div className='text-center space-y-2'>
               <p className='text-sm text-muted-foreground'>
                  Enter the 4-digit code sent to{' '}
                  <span className='font-medium text-foreground'>{currentEmail}</span>
               </p>
            </div>

            <Form {...otpForm}>
               <form onSubmit={handleOtpSubmit(onSubmit)} className='space-y-6'>
                  <div className='flex justify-center'>
                     <FormField
                        control={otpControl}
                        name='otp'
                        render={({ field }) => (
                           <FormItem>
                              <FormControl>
                                 <InputOTP maxLength={4} {...field}>
                                    <InputOTPGroup className='flex justify-center gap-2 w-[200px]'>
                                       {[0, 1, 2, 3].map(index => (
                                          <InputOTPSlot
                                             key={index}
                                             index={index}
                                             className='rounded-md border border-input w-12 h-12'
                                          />
                                       ))}
                                    </InputOTPGroup>
                                 </InputOTP>
                              </FormControl>
                              <div className='flex justify-center'>
                                 <ErrorMessage error={otpErrors.otp} />
                              </div>
                           </FormItem>
                        )}
                     />
                  </div>

                  <Button
                     type='submit'
                     className='w-full'
                     disabled={verifyEmailOtpMutation.isPending}
                  >
                     {verifyEmailOtpMutation.isPending ? (
                        <>
                           Verifying...
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : (
                        'Verify Email'
                     )}
                  </Button>

                  <div className='text-center space-y-3'>
                     <p className='text-sm text-muted-foreground'>
                        Didn't receive the code?{' '}
                        <Button
                           type='button'
                           variant='link'
                           className='p-0 h-auto font-semibold'
                           disabled={!canResend || sendEmailVerificationMutation.isPending}
                           onClick={handleResendOtp}
                        >
                           {sendEmailVerificationMutation.isPending ? (
                              <>
                                 Resending...
                                 <Spinner className='ml-2 h-3 w-3' />
                              </>
                           ) : canResend ? (
                              'Resend Code'
                           ) : (
                              `Resend in ${timeLeft}s`
                           )}
                        </Button>
                     </p>
                  </div>

                  <div className='flex justify-center'>
                     <Button
                        type='button'
                        variant='link'
                        onClick={handleBack}
                        className='flex items-center gap-2 text-sm text-primary hover:underline'
                     >
                        <ArrowLeft className='h-4 w-4' />
                        Back
                     </Button>
                  </div>
               </form>
            </Form>
         </div>
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-md'
         >
            <DialogHeader>
               <DialogTitle>
                  {currentStep === 'confirm-email'
                     ? 'Verify Email Address'
                     : 'Enter Verification Code'}
               </DialogTitle>
               <DialogDescription>
                  {currentStep === 'confirm-email'
                     ? 'We will send a verification code to your email address'
                     : 'Enter the verification code sent to your email'}
               </DialogDescription>
            </DialogHeader>

            <div className='py-4'>{renderStepContent()}</div>
         </DialogContent>
      </Dialog>
   );
};

import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsUUID,
  MinLength,
  MaxLength,
} from 'class-validator';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';

export class UpdateDriverDto {
  @ApiProperty({
    example: 'John',
    description: 'Driver first name',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Driver last name',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  // @MinLength(2)
  @MaxLength(50)
  lastName?: string;

  @ApiProperty({
    enum: Gender,
    example: Gender.MALE,
    description: 'Driver gender',
    required: false,
  })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiProperty({
    example: '1990-01-15',
    description: 'Driver date of birth in YYYY-MM-DD format',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dob?: string;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL (uploaded via file upload API)',
    required: false,
  })
  @IsOptional()
  @IsString()
  profilePictureUrl?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'City ID where the driver is located',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Language ID for the driver',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  languageId?: string;
}

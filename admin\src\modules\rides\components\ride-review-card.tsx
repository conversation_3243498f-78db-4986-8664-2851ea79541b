import { Card } from '@/components/ui/card';
import { Star } from 'lucide-react';
import { Review } from '../types/ride';

interface RideReviewCardProps {
   reviews: Review[];
}

export function RideReviewCard({ reviews }: RideReviewCardProps) {
   return (
      <Card className='p-4 rounded-sm'>
         <h3 className='text-base font-semibold text-gray-900 mb-3'>Review</h3>
         {reviews && reviews.length > 0 ? (
            <div>
               {reviews.map(review => {
                  const rating = Math.round(review.rating);
                  return (
                     <div key={review.id} className='flex flex-col items-center text-center'>
                        {/* Rating Display */}
                        <div className='mb-3'>
                           <div className='text-4xl font-medium text-gray-700 mb-2'>
                              {review.rating.toFixed(1)}
                           </div>
                           <div className='flex items-center justify-center gap-1 mb-2'>
                              {[1, 2, 3, 4, 5].map(star => (
                                 <Star
                                    key={star}
                                    className={`w-5 h-5 ${
                                       star <= rating
                                          ? 'text-yellow-400 fill-yellow-400'
                                          : 'text-gray-300 fill-gray-300'
                                    }`}
                                 />
                              ))}
                           </div>
                        </div>

                        {/* Review Info */}
                        <div className='w-full max-w-md'>
                           <h3 className='text-base font-semibold text-gray-900 mb-1'>
                              Rider Review
                           </h3>
                           <p className='text-sm text-gray-600 mb-3'>
                              Reviewed by {review.reviewBy.firstName} {review.reviewBy.lastName}
                           </p>

                           {/* Review Note/Comment */}
                           <div className='bg-gray-50 rounded-lg p-4'>
                              {review.review ? (
                                 <p className='text-sm text-gray-700 leading-relaxed italic'>
                                    "{review.review}"
                                 </p>
                              ) : (
                                 <p className='text-sm text-gray-500 italic'>No note added</p>
                              )}
                           </div>
                        </div>
                     </div>
                  );
               })}
            </div>
         ) : (
            <div className='flex flex-col items-center justify-center py-8 text-center'>
               <div className='w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3'>
                  <Star className='w-6 h-6 text-gray-400' />
               </div>
               <div className='text-sm font-medium text-gray-900 mb-1'>No reviews yet</div>
               <div className='text-xs text-gray-500'>
                  This ride hasn't been reviewed by any participant
               </div>
            </div>
         )}
      </Card>
   );
}

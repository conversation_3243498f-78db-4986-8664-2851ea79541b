'use client';

import { useState } from 'react';
import { useListProductService } from '../api/queries';
import { ProductServiceTable } from '../components/product-service-table';

export function ProductServicePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);

   const listProductService = useListProductService({
      page,
      limit,
   });

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Product Services</h2>
         </div>

         <ProductServiceTable
            data={listProductService.data}
            isLoading={listProductService.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />
      </div>
   );
}

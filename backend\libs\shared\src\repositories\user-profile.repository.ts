import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { UserProfile, UserProfileStatus } from './models/userProfile.model';

@Injectable()
export class UserProfileRepository extends BaseRepository<UserProfile> {
  protected readonly modelName = 'userProfile';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new user profile.
   */
  async createUserProfile(
    userId: string,
    roleId: string,
    data: Omit<
      UserProfile,
      'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
    >,
  ): Promise<UserProfile> {
    return this.create({ userId, roleId, ...data });
  }

  /**
   * Update a user profile by userId and roleId.
   */
  async updateUserProfile(
    userId: string,
    roleId: string,
    data: Partial<
      Omit<
        UserProfile,
        'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
      >
    >,
  ): Promise<UserProfile> {
    return this.update({
      where: { userId, roleId },
      data,
    });
  }

  async updateUserProfileById(
    id: string,
    data: Partial<
      Omit<
        UserProfile,
        'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
      >
    >,
  ): Promise<UserProfile> {
    return this.update({
      where: { id },
      data,
    });
  }

  async findWithAdminDetails(id: string): Promise<UserProfile | null> {
    return this.findOne({
      where: { id },
      include: {
        user: {
          select: {
            email: true,
            phoneNumber: true,
            id: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
        cityAdmins: {
          select: {
            cityId: true,
            isEnabled: true,
            city: { select: { name: true } },
          },
        },
      },
    });
  }

  /**
   * Get a user profile by userId and roleId.
   */
  async getOneByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserProfile | null> {
    return this.findOne({
      where: { userId, roleId },
      include: { metaData: true, city: {
        select: { name: true, state: true, country: true, id: true },
      }},
    });
  }

  /**
   * Create or update a user profile by userId and roleId.
   * If a profile exists for the user and role, it will be updated; otherwise, a new one is created.
   */
  async createOrUpdateUserProfile(
    userId: string,
    roleId: string,
    data: Partial<
      Omit<UserProfile, 'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<UserProfile> {
    // Ensure all required fields for creation are present
    const requiredFields = ['firstName', 'lastName', 'referralCode'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field as keyof typeof data] === undefined) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    const createObj: any = {
      userId,
      roleId,
      firstName: data.firstName!,
      lastName: data.lastName!,
      referralCode: data.referralCode!,
    };
    if (data.cityId !== undefined) createObj.cityId = data.cityId;
    if (data.profilePictureUrl !== undefined)
      createObj.profilePictureUrl = data.profilePictureUrl;
    if (data.gender !== undefined) createObj.gender = data.gender;
    if (data.dob !== undefined) createObj.dob = data.dob;
    return this.upsert({ userId_roleId: { userId, roleId } }, createObj, data);
  }

  async findOrCreateByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserProfile> {
    const existing = await this.getOneByUserIdAndRoleId(userId, roleId);
    if (existing) {
      return existing;
    }
    return this.create({ userId, roleId, status: 'pending' as any });
  }

  async getUserRoleIds(userId: string): Promise<string[]> {
    const profiles = await this.prisma.userProfile.findMany({
      where: { userId, deletedAt: null, status: UserProfileStatus.ACTIVE },
      select: { roleId: true },
    });
    return profiles.map((profile) => profile.roleId);
  }

  async getUserRoleIdsAndNames(
    userId: string,
  ): Promise<{ profileIds: string[]; roleIds: string[]; roleNames: string[] }> {
    const profiles = await this.prisma.userProfile.findMany({
      //TODO:need to update with active status only
      where: { userId, deletedAt: null },
      select: { id: true, roleId: true, role: { select: { name: true } } },
    });
    // console.log('profiles', profiles);
    return {
      profileIds: profiles.map((profile) => profile.id),
      roleIds: profiles.map((profile) => profile.roleId),
      roleNames: profiles.map((profile) => profile.role.name),
    };
  }
}

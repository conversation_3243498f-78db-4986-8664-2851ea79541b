'use client';

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { formatDateForDisplay } from '@/lib/date-utils';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { AlertCircle, Check, FileText, Upload, X } from 'lucide-react';
import { useState } from 'react';
import { useDeleteKycDocument } from '../api/mutations';
import { useGetDriverKycDocuments } from '../api/queries';
import { KycDocument } from '../types/driver';
import { ApproveKycModal } from './approve-kyc-modal';
import { KycDocumentUploadModal } from './kyc-document-upload-modal';
import { RejectKycModal } from './reject-kyc-modal';
import { openFilePreview } from '@/lib/file-preview';

interface DriverKycDocumentsProps {
   driverId: string;
}

export function DriverKycDocuments({ driverId }: DriverKycDocumentsProps) {
   const [uploadingDocumentId, setUploadingDocumentId] = useState<string | null>(null);
   const [approvingDocument, setApprovingDocument] = useState<{
      id: string;
      name: string;
      identifier: string;
   } | null>(null);
   const [rejectingDocument, setRejectingDocument] = useState<{ id: string; name: string } | null>(
      null
   );
   const queryClient = useQueryClient();

   const { data: kycDocuments, isLoading, error } = useGetDriverKycDocuments(driverId);
   const deleteKycMutation = useDeleteKycDocument();

   const handleDeleteDocument = async (documentId: string) => {
      deleteKycMutation.mutate(documentId, {
         onSuccess: () => {
            toast.success('Document deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
            queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] });
            queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
         },
      });
   };

   const getStatusBadge = (status: string) => {
      switch (status) {
         case 'APPROVED':
            return (
               <Badge className='bg-green-100 text-green-700 text-xs font-medium px-2 py-1'>
                  Approved
               </Badge>
            );
         case 'REJECTED':
            return (
               <Badge className='bg-red-100 text-red-700 text-xs font-medium px-2 py-1'>
                  Rejected
               </Badge>
            );
         case 'PENDING':
         default:
            return (
               <Badge className='bg-yellow-100 text-yellow-700 text-xs font-medium px-2 py-1'>
                  Pending
               </Badge>
            );
      }
   };

   if (isLoading) {
      return (
         <div className='space-y-6'>
            {[1, 2, 3].map(i => (
               <Card key={i} className='p-6'>
                  <div className='animate-pulse'>
                     <div className='h-6 bg-gray-200 rounded w-1/4 mb-4'></div>
                     <div className='h-32 bg-gray-200 rounded'></div>
                  </div>
               </Card>
            ))}
         </div>
      );
   }

   if (error || !kycDocuments?.data) {
      return (
         <Card className='p-8 text-center'>
            <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>Failed to Load KYC Documents</h3>
            <p className='text-gray-600'>
               There was an error loading the KYC documents. Please try again.
            </p>
         </Card>
      );
   }

   return (
      <div className='space-y-6'>
         <div>
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>KYC Verification</h3>
            <p className='text-sm text-gray-600'>Verify your identity documents.</p>
         </div>

         {/* 2x2 Grid Layout */}
         <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {kycDocuments.data.map((document: KycDocument) => (
               <Card
                  key={document.id}
                  className='p-6 bg-white border border-gray-200 hover:shadow-md transition-shadow'
               >
                  {/* Document Header */}
                  <div className='flex items-center justify-between mb-4'>
                     <div className='flex items-center gap-3'>
                        <div className='w-10 h-10 rounded-lg bg-gray-50 flex items-center justify-center border border-gray-200'>
                           <FileText className='w-5 h-5 text-gray-600' />
                        </div>
                        <div>
                           <h4 className='font-semibold text-gray-900 text-sm'>{document.name}</h4>
                           {document.isMandatory && (
                              <span className='text-xs text-gray-500'>Required</span>
                           )}
                        </div>
                     </div>

                     <div className='flex items-center gap-2'>
                        {document.driverKyc
                           ? getStatusBadge(document.driverKyc.status)
                           : document.isMandatory && (
                                <Badge className='bg-orange-100 text-orange-700 text-xs font-medium px-2 py-1'>
                                   Mandatory
                                </Badge>
                             )}
                     </div>
                  </div>

                  {document.driverKyc ? (
                     <div className='space-y-4'>
                        {/* Document Status */}
                        <div className='bg-gray-50 rounded-lg p-4'>
                           <div className='flex items-center justify-between mb-3'>
                              <span className='text-sm font-medium text-gray-900'>
                                 Document Uploaded
                              </span>
                              <div className='flex items-center gap-1'>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() => openFilePreview(document.driverKyc!.documentUrl)}
                                    className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 text-xs px-2 py-1'
                                 >
                                    View Document
                                 </Button>
                                 {document.driverKyc.status === 'PENDING' && (
                                    <Button
                                       variant='outline'
                                       size='sm'
                                       onClick={() => setUploadingDocumentId(document.id)}
                                       className='text-gray-600 hover:text-gray-700 border-gray-200 hover:border-gray-300 text-xs px-2 py-1'
                                    >
                                       Edit
                                    </Button>
                                 )}
                                 <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                       <Button
                                          variant='outline'
                                          size='sm'
                                          disabled={deleteKycMutation.isPending}
                                          className='text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 text-xs px-2 py-1'
                                       >
                                          Delete
                                       </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                       <AlertDialogHeader>
                                          <AlertDialogTitle>Delete Document</AlertDialogTitle>
                                          <AlertDialogDescription>
                                             Are you sure you want to delete this document? This
                                             action cannot be undone.
                                          </AlertDialogDescription>
                                       </AlertDialogHeader>
                                       <AlertDialogFooter>
                                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                                          <AlertDialogAction
                                             onClick={() =>
                                                handleDeleteDocument(document.driverKyc!.id)
                                             }
                                             className='bg-red-600 hover:bg-red-700'
                                          >
                                             Delete
                                          </AlertDialogAction>
                                       </AlertDialogFooter>
                                    </AlertDialogContent>
                                 </AlertDialog>
                              </div>
                           </div>

                           {/* Compact Document Details */}
                           <div className='space-y-2 text-xs'>
                              {/* Document-specific fields first */}
                              {document.driverKyc.documentNumber && (
                                 <div className='flex justify-between'>
                                    <span className='text-gray-500'>Document Number:</span>
                                    <span className='text-gray-900 font-medium'>
                                       {document.driverKyc.documentNumber}
                                    </span>
                                 </div>
                              )}

                              {/* Driving License specific fields */}
                              {document.identifier === 'driving_licence' &&
                                 document.driverKyc.expiryDate && (
                                    <div className='flex justify-between'>
                                       <span className='text-gray-500'>Expiry Date:</span>
                                       <span className='text-gray-900 font-medium'>
                                          {formatDateForDisplay(document.driverKyc.expiryDate)}
                                       </span>
                                    </div>
                                 )}

                              {/* Bank Details specific fields */}
                              {document.identifier === 'bank_details' &&
                                 document.driverKyc.documentFields && (
                                    <>
                                       {document.driverKyc.documentFields.account_number && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>Account Number:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {document.driverKyc.documentFields.account_number}
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.ifsc_code && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>IFSC Code:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {document.driverKyc.documentFields.ifsc_code}
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.account_holder_name && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>Account Holder:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {
                                                   document.driverKyc.documentFields
                                                      .account_holder_name
                                                }
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.bank_name && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>Bank Name:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {document.driverKyc.documentFields.bank_name}
                                             </span>
                                          </div>
                                       )}
                                    </>
                                 )}

                              {/* Police Clearance Certificate specific fields */}
                              {document.identifier === 'police_clearance_certificate' &&
                                 document.driverKyc.documentFields && (
                                    <>
                                       {document.driverKyc.documentFields.certificate_number && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>
                                                Certificate Number:
                                             </span>
                                             <span className='text-gray-900 font-medium'>
                                                {
                                                   document.driverKyc.documentFields
                                                      .certificate_number
                                                }
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.issue_date && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>Issue Date:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {formatDateForDisplay(
                                                   document.driverKyc.documentFields.issue_date
                                                )}
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.expiry_date && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>Expiry Date:</span>
                                             <span className='text-gray-900 font-medium'>
                                                {formatDateForDisplay(
                                                   document.driverKyc.documentFields.expiry_date
                                                )}
                                             </span>
                                          </div>
                                       )}
                                       {document.driverKyc.documentFields.issuing_authority && (
                                          <div className='flex justify-between'>
                                             <span className='text-gray-500'>
                                                Issuing Authority:
                                             </span>
                                             <span className='text-gray-900 font-medium'>
                                                {
                                                   document.driverKyc.documentFields
                                                      .issuing_authority
                                                }
                                             </span>
                                          </div>
                                       )}
                                    </>
                                 )}

                              {/* Upload date and uploader info */}
                              <div className='flex justify-between'>
                                 <span className='text-gray-500'>Uploaded by:</span>
                                 <span className='text-gray-900 font-medium'>
                                    {document.driverKyc.createdByUser.role.name === 'super_admin'
                                       ? 'Admin'
                                       : document.driverKyc.createdByUser.role.name === 'driver'
                                       ? 'Driver'
                                       : document.driverKyc.createdByUser.role.name}
                                 </span>
                              </div>
                              <div className='flex justify-between'>
                                 <span className='text-gray-500'>Upload date:</span>
                                 <span className='text-gray-900 font-medium'>
                                    {formatDateForDisplay(document.driverKyc.createdAt)}
                                 </span>
                              </div>
                           </div>
                        </div>

                        {/* Rejection Note */}
                        {document.driverKyc.status === 'REJECTED' &&
                           document.driverKyc.rejectionNote && (
                              <div className='bg-red-50 border border-red-200 rounded-lg p-3 mt-3'>
                                 <div className='flex items-start gap-2'>
                                    <AlertCircle className='w-3 h-3 text-red-600 mt-0.5 flex-shrink-0' />
                                    <div>
                                       <h5 className='text-xs font-medium text-red-800 mb-1'>
                                          Rejection Reason
                                       </h5>
                                       <p className='text-xs text-red-700'>
                                          {document.driverKyc.rejectionNote}
                                       </p>
                                    </div>
                                 </div>
                              </div>
                           )}

                        {/* Approve/Reject buttons */}
                        {document.driverKyc.status === 'PENDING' && (
                           <div className='pt-3 border-t border-gray-200 mt-3'>
                              <div className='flex items-center justify-end gap-2'>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       setApprovingDocument({
                                          id: document.driverKyc!.id,
                                          identifier: document.identifier,
                                          name: document.name,
                                       })
                                    }
                                    className='gap-1 text-green-600 hover:text-green-700 border-green-200 hover:border-green-300 text-xs px-2 py-1'
                                 >
                                    <Check className='w-3 h-3' />
                                    Approve
                                 </Button>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       setRejectingDocument({
                                          id: document.driverKyc!.id,
                                          name: document.name,
                                       })
                                    }
                                    className='gap-1 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 text-xs px-2 py-1'
                                 >
                                    <X className='w-3 h-3' />
                                    Reject
                                 </Button>
                              </div>
                           </div>
                        )}

                        {/* Reject button for approved documents */}
                        {document.driverKyc.status === 'APPROVED' && (
                           <div className='pt-3 border-t border-gray-200 mt-3'>
                              <div className='flex items-center justify-end'>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       setRejectingDocument({
                                          id: document.driverKyc!.id,
                                          name: document.name,
                                       })
                                    }
                                    className='gap-1 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 text-xs px-2 py-1'
                                 >
                                    <X className='w-3 h-3' />
                                    Reject
                                 </Button>
                              </div>
                           </div>
                        )}

                        {/* Approve button for rejected documents */}
                        {document.driverKyc.status === 'REJECTED' && (
                           <div className='pt-3 border-t border-gray-200 mt-3'>
                              <div className='flex items-center justify-end gap-2'>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                       setApprovingDocument({
                                          id: document.driverKyc!.id,
                                          identifier: document.identifier,
                                          name: document.name,
                                       })
                                    }
                                    className='gap-1 text-green-600 hover:text-green-700 border-green-200 hover:border-green-300 text-xs px-2 py-1'
                                 >
                                    <Check className='w-3 h-3' />
                                    Approve
                                 </Button>
                                 <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() => setUploadingDocumentId(document.id)}
                                    className='gap-1 text-xs px-2 py-1'
                                 >
                                    <Upload className='w-3 h-3' />
                                    Re-upload Document
                                 </Button>
                              </div>
                           </div>
                        )}
                     </div>
                  ) : (
                     /* Upload Section */
                     <div className='text-center py-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50'>
                        <div className='w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-3'>
                           <FileText className='w-5 h-5 text-gray-400' />
                        </div>
                        <h4 className='text-sm font-medium text-gray-900 mb-2'>
                           No document uploaded
                        </h4>
                        <p className='text-xs text-gray-500 mb-3'>
                           Upload your {document.name.toLowerCase()} to complete verification
                        </p>
                        <Button
                           variant='outline'
                           size='sm'
                           onClick={() => setUploadingDocumentId(document.id)}
                           className='gap-1 text-xs px-2 py-1'
                        >
                           <Upload className='w-3 h-3' />
                           Upload Document
                        </Button>
                     </div>
                  )}

                  {/* Upload Modal */}
                  <KycDocumentUploadModal
                     document={document}
                     driverId={driverId}
                     open={uploadingDocumentId === document.id}
                     onClose={() => setUploadingDocumentId(null)}
                     editMode={document.driverKyc?.status === 'PENDING'}
                     onSuccess={() => {
                        setUploadingDocumentId(null);
                        queryClient.invalidateQueries({
                           queryKey: ['driver-kyc-documents', driverId],
                        });
                     }}
                  />
               </Card>
            ))}
         </div>

         {/* Approve KYC Modal */}
         {approvingDocument && (
            <ApproveKycModal
               open={approvingDocument !== null}
               onClose={() => setApprovingDocument(null)}
               onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
                  queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] });
               }}
               documentId={approvingDocument.id}
               documentName={approvingDocument.name}
            />
         )}

         {/* Reject KYC Modal */}
         {rejectingDocument && (
            <RejectKycModal
               open={rejectingDocument !== null}
               onClose={() => setRejectingDocument(null)}
               onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
                  queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] });
               }}
               documentId={rejectingDocument.id}
               documentName={rejectingDocument.name}
            />
         )}
      </div>
   );
}

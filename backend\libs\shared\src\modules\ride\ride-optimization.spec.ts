import { Test, TestingModule } from '@nestjs/testing';
import { RideService } from './ride.service';
import { RideUtilsService } from './services/ride-utlis.service';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { CityProductRepository } from '@shared/shared/repositories/city-product.repository';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
import { Logger } from '@nestjs/common';
import { CreateRideData, AdminCreateRideData } from './ride.service';
import {
  BookFor,
  PickupType,
} from '@shared/shared/repositories/models/ride.model';

describe('RideService Optimization Tests', () => {
  let rideService: RideService;
  let rideUtilsService: RideUtilsService;
  let userProfileRepository: UserProfileRepository;
  let cityProductRepository: CityProductRepository;
  let googleRouteMatrixService: GoogleRouteMatrixService;

  // Mock data
  const mockRiderId = 'test-rider-123';
  const mockCityProductId = 'test-city-product-456';
  const mockProductId = 'test-product-789';

  const mockCreateRideData: CreateRideData = {
    riderId: mockRiderId,
    cityProductId: mockCityProductId,
    productId: mockProductId,
    pickup: { lat: 12.9716, lng: 77.5946 },
    destination: { lat: 12.9716, lng: 77.5946 },
    pickupType: PickupType.NOW,
    bookFor: BookFor.ME,
  };

  const mockAdminCreateRideData: AdminCreateRideData = {
    cityProductId: mockCityProductId,
    productId: mockProductId,
    pickup: { lat: 12.9716, lng: 77.5946 },
    destination: { lat: 12.9716, lng: 77.5946 },
    pickupType: PickupType.NOW,
    bookFor: BookFor.OTHER,
    riderMeta: {
      phoneNumber: '+**********',
      name: 'Test Rider',
    },
    createdBy: 'admin-123',
  };

  const mockRiderProfile = {
    id: mockRiderId,
    rideOtp: '1234',
    user: { phoneNumber: '+**********' },
  };

  const mockCityProduct = {
    id: mockCityProductId,
    product: {
      id: mockProductId,
      name: 'Test Product',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RideService,
        RideUtilsService,
        {
          provide: RideRepository,
          useValue: {
            createRideWithLifecycle: jest.fn(),
            findActiveRideByRiderId: jest.fn().mockResolvedValue(null),
          },
        },
        {
          provide: UserProfileRepository,
          useValue: {
            findById: jest.fn().mockResolvedValue(mockRiderProfile),
            findUnique: jest.fn().mockResolvedValue(mockRiderProfile),
            updateById: jest.fn(),
          },
        },
        {
          provide: CityProductRepository,
          useValue: {
            findCityProductById: jest.fn().mockResolvedValue(mockCityProduct),
          },
        },
        {
          provide: GoogleRouteMatrixService,
          useValue: {
            computeDistanceAndDuration: jest.fn().mockResolvedValue({
              distance: 5000,
              duration: 1200,
            }),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    rideService = module.get<RideService>(RideService);
    rideUtilsService = module.get<RideUtilsService>(RideUtilsService);
    userProfileRepository = module.get<UserProfileRepository>(
      UserProfileRepository,
    );
    cityProductRepository = module.get<CityProductRepository>(
      CityProductRepository,
    );
    googleRouteMatrixService = module.get<GoogleRouteMatrixService>(
      GoogleRouteMatrixService,
    );
  });

  describe('RideUtilsService Common Methods', () => {
    it('should validate and fetch rider profile', async () => {
      const result =
        await rideUtilsService.validateAndFetchRiderProfile(mockRiderId);

      expect(userProfileRepository.findById).toHaveBeenCalledWith(mockRiderId);
      expect(result).toEqual(mockRiderProfile);
    });

    it('should validate and fetch city product', async () => {
      const result =
        await rideUtilsService.validateAndFetchCityProduct(mockCityProductId);

      expect(cityProductRepository.findCityProductById).toHaveBeenCalledWith(
        mockCityProductId,
      );
      expect(result).toEqual(mockCityProduct);
    });

    it('should calculate distance and duration', async () => {
      const pickup = { lat: 12.9716, lng: 77.5946 };
      const destination = { lat: 12.9716, lng: 77.5946 };

      const result = await rideUtilsService.calculateDistanceAndDuration(
        pickup,
        destination,
      );

      expect(
        googleRouteMatrixService.computeDistanceAndDuration,
      ).toHaveBeenCalled();
      expect(result).toEqual({ distance: 5000, duration: 1200 });
    });

    it('should resolve rider ID for normal rides', async () => {
      const result = await rideUtilsService.resolveRiderId(
        mockCreateRideData,
        false,
      );

      expect(result).toBe(mockRiderId);
    });

    it('should resolve rider ID for admin rides with existing rider', async () => {
      const result = await rideUtilsService.resolveRiderId(
        mockAdminCreateRideData,
        true,
      );

      expect(userProfileRepository.findUnique).toHaveBeenCalled();
      expect(result).toBe(mockRiderId);
    });

    it('should generate verification code', () => {
      const code = rideUtilsService.generateVerificationCode();

      expect(code).toMatch(/^\d{4}$/);
      expect(code.length).toBe(4);
    });

    it('should ensure rider has OTP', async () => {
      const result = await rideUtilsService.ensureRiderOtp(mockRiderProfile);

      expect(result).toBe('1234');
    });
  });

  describe('Integration Tests', () => {
    it('should use utility methods in requestRide', async () => {
      // Mock the required methods
      jest
        .spyOn(rideUtilsService, 'validateRideRequest')
        .mockResolvedValue(true);
      jest
        .spyOn(rideUtilsService, 'resolveRiderId')
        .mockResolvedValue(mockRiderId);
      jest
        .spyOn(rideUtilsService, 'validateAndFetchRiderProfile')
        .mockResolvedValue(mockRiderProfile);
      jest
        .spyOn(rideUtilsService, 'validateAndFetchCityProduct')
        .mockResolvedValue(mockCityProduct);
      jest
        .spyOn(rideUtilsService, 'validateProduct')
        .mockReturnValue(mockCityProduct.product);
      jest
        .spyOn(rideUtilsService, 'validateActiveRideExists')
        .mockResolvedValue(null);
      jest.spyOn(rideUtilsService, 'ensureRiderOtp').mockResolvedValue('1234');
      jest
        .spyOn(rideUtilsService, 'calculateDistanceAndDuration')
        .mockResolvedValue({ distance: 5000, duration: 1200 });
      jest
        .spyOn(rideUtilsService, 'createRideDataObject')
        .mockReturnValue({} as any);
      jest
        .spyOn(rideUtilsService, 'createLifecycleDataObject')
        .mockReturnValue({} as any);
      jest
        .spyOn(rideUtilsService, 'logRideCreationSuccess')
        .mockImplementation();

      // Mock other required methods
      jest
        .spyOn(rideService as any, 'calculateFareForRideRequest')
        .mockResolvedValue(null);
      jest
        .spyOn(rideService as any, 'createRideMetersAndFareAsync')
        .mockImplementation();
      jest
        .spyOn(rideService as any, 'publishRideRequestedEventAsync')
        .mockImplementation();

      const mockRide = { id: 'ride-123' };
      jest
        .spyOn(rideService['rideRepository'], 'createRideWithLifecycle')
        .mockResolvedValue(mockRide as any);

      const result = await rideService.requestRide(mockCreateRideData);

      // Verify utility methods were called
      expect(rideUtilsService.validateRideRequest).toHaveBeenCalledWith(
        mockCreateRideData,
      );
      expect(rideUtilsService.resolveRiderId).toHaveBeenCalledWith(
        mockCreateRideData,
        false,
      );
      expect(
        rideUtilsService.validateAndFetchRiderProfile,
      ).toHaveBeenCalledWith(mockRiderId);
      expect(rideUtilsService.validateAndFetchCityProduct).toHaveBeenCalledWith(
        mockCityProductId,
      );
      expect(rideUtilsService.calculateDistanceAndDuration).toHaveBeenCalled();
      expect(rideUtilsService.createRideDataObject).toHaveBeenCalled();
      expect(rideUtilsService.createLifecycleDataObject).toHaveBeenCalled();
      expect(rideUtilsService.logRideCreationSuccess).toHaveBeenCalled();

      expect(result).toEqual(mockRide);
    });
  });
});

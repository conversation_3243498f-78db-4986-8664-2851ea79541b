import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Payment, PaymentType } from './models/payment.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class PaymentRepository extends BaseRepository<Payment> {
  protected readonly modelName = 'payment';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new payment record
   */
  async createPayment(
    data: Omit<Payment, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Payment> {
    return this.create(data);
  }

  /**
   * Find payment by ride ID
   */
  async findByRideId(rideId: string): Promise<Payment | null> {
    return this.findOne({
      where: { rideId },
      include: {
        ride: true,
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  /**
   * Update payment with payment type and received timestamp
   */
  async confirmPayment(
    rideId: string,
    paymentType: PaymentType,
    amount: number,
  ): Promise<Payment> {
    return this.update({
      where: { rideId },
      data: {
        paymentType,
        amount,
        receivedAt: new Date(),
      },
    });
  }

  /**
   * Find payments by driver ID with pagination and optional filtering
   */
  async findPaymentsByDriverId(
    driverId: string,
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
    paymentType?: 'CASH' | 'ONLINE',
  ): Promise<{ payments: Payment[]; total: number }> {
    const skip = (page - 1) * limit;

    // Build where clause with filters
    const where: any = { driverId };

    if (fromDate || toDate) {
      where.createdAt = {};
      if (fromDate) {
        where.createdAt.gte = fromDate;
      }
      if (toDate) {
        where.createdAt.lte = toDate;
      }
    }

    if (paymentType) {
      where.paymentType = paymentType;
    }

    const [payments, total] = await Promise.all([
      this.findMany({
        where,
        include: {
          ride: {
            select: {
              id: true,
              pickupLocation: true,
              destinationLocation: true,
              completedAt: true,
            },
          },
          rider: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.count(where),
    ]);

    return { payments, total };
  }

  /**
   * Find payments by rider ID with pagination
   */
  async findPaymentsByRiderId(
    riderId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ payments: Payment[]; total: number }> {
    const skip = (page - 1) * limit;

    const [payments, total] = await Promise.all([
      this.findMany({
        where: { riderId },
        include: {
          ride: {
            select: {
              id: true,
              pickupLocation: true,
              destinationLocation: true,
              completedAt: true,
            },
          },
          driver: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.count({ where: { riderId } }),
    ]);

    return { payments, total };
  }
}

import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { RidesCronService } from './rides.cron.service';
import { CronsController } from './crons.controller';
import { RideModule } from '@shared/shared/modules/ride/ride.module';

@Module({
  imports: [
    ScheduleModule.forRoot(), // Enable cron jobs
    RideModule, // Import RideModule to access RideScheduleService
  ],
  controllers: [CronsController],
  providers: [RidesCronService],
  exports: [RidesCronService],
})
export class CronsModule {}

import { Module } from '@nestjs/common';
import { DriverController } from './driver.controller';
import { UserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { AuthModule } from '@shared/shared/modules/auth/auth.module';
import { LocationIngestorModule } from '@shared/shared/modules/location/location-injestor.module';

@Module({
  imports: [AuthModule, UserProfileModule, LocationIngestorModule],
  controllers: [DriverController],
  providers: [],
  exports: [],
})
export class ApiDriverModule {}

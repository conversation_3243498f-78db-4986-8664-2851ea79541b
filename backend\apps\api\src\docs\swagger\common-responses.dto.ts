import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T = any> {
  @ApiProperty({ example: true, description: 'Request success status' })
  success!: boolean;

  @ApiProperty({
    example: 'Operation completed successfully',
    description: 'Response message',
  })
  message!: string;

  @ApiProperty({ description: 'Response data' })
  data?: T;

  @ApiProperty({ example: Date.now(), description: 'Response timestamp' })
  timestamp!: number;
}

export class ApiErrorResponseDto {
  @ApiProperty({ example: false, description: 'Request success status' })
  success!: boolean;

  @ApiProperty({ example: 'An error occurred', description: 'Error message' })
  message!: string;

  @ApiProperty({ example: 'VALIDATION_ERROR', description: 'Error code' })
  error?: string;

  @ApiProperty({
    example: ['field is required'],
    description: 'Validation errors',
  })
  errors?: string[];

  @ApiProperty({ example: Date.now(), description: 'Response timestamp' })
  timestamp!: number;
}

export class PaginationMetaDto {
  @ApiProperty({ example: 1, description: 'Current page number' })
  page!: number;

  @ApiProperty({ example: 10, description: 'Number of items per page' })
  limit!: number;

  @ApiProperty({ example: 100, description: 'Total number of items' })
  total!: number;

  @ApiProperty({ example: 10, description: 'Total number of pages' })
  totalPages!: number;

  @ApiProperty({ example: true, description: 'Whether there is a next page' })
  hasNextPage!: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether there is a previous page',
  })
  hasPreviousPage!: boolean;
}

export class PaginatedResponseDto<T = any> extends ApiResponseDto {
  @ApiProperty({ description: 'Paginated data' })
  declare data: T[];

  @ApiProperty({ type: PaginationMetaDto, description: 'Pagination metadata' })
  meta!: PaginationMetaDto;
}

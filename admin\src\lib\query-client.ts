import {
  useQueryClient as useReactQueryClient,
  QueryKey,
  QueryFilters,
} from '@tanstack/react-query';
import { queryKeys as allQueryKeys } from './query-keys';

type QueryKeysType = typeof allQueryKeys;

type InvalidateQueryKey = {
  invalidateKey: (keys: QueryKeysType) => QueryKey;
  filters?: QueryFilters;
};

export function useTypeSafeQueryClient() {
  const queryClient = useReactQueryClient();

  return {
    ...queryClient,
    invalidateQueries: ({ invalidateKey, filters }: InvalidateQueryKey) => {
      const queryKey = invalidateKey(allQueryKeys);
      return queryClient.invalidateQueries({ queryKey, ...filters });
    },
  };
}

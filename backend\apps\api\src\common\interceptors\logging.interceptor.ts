import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { method, url, body, params, query } = request;
    const userAgent = request.get('user-agent') || '';
    const ip = this.getIp(request);

    const requestData = {
      timestamp: new Date().toISOString(),
      method,
      url,
      body,
      params,
      query,
      userAgent,
      ip,
    };

    this.logger.log(`Request: ${method} ${url}`, JSON.stringify(requestData));

    const now = Date.now();
    return next.handle().pipe(
      tap({
        next: (_data: any) => {
          const responseData = {
            timestamp: new Date().toISOString(),
            duration: `${Date.now() - now}ms`,
            statusCode: response.statusCode,
            method,
            url,
          };

          this.logger.log(
            `Response: ${method} ${url} ${response.statusCode} - ${Date.now() - now}ms`,
            JSON.stringify(responseData),
          );
        },
        error: (error: any) => {
          const responseData = {
            timestamp: new Date().toISOString(),
            duration: `${Date.now() - now}ms`,
            statusCode: error.status || 500,
            method,
            url,
            error: error.message,
          };

          this.logger.error(
            `Response Error: ${method} ${url} ${error.status || 500} - ${Date.now() - now}ms`,
            JSON.stringify(responseData),
            error.stack,
          );
        },
      }),
    );
  }

  private getIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] ||
      request.connection.remoteAddress ||
      'unknown'
    ).toString();
  }
}

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DriverAccountRepository } from '@shared/shared/repositories';
import {
  DriverDueConfigRepository,
  DriverDuePaymentTransactionRepository,
  DriverDueConfig,
  DriverDuePaymentTransaction,
  CreateDriverDueConfigData,
  CreateDriverDuePaymentTransactionData,
  UpdateDriverDueConfigData,
} from '@shared/shared/repositories/driver-due.repository';

@Injectable()
export class DriverDueService {
  private readonly logger = new Logger(DriverDueService.name);

  constructor(
    private readonly dueConfigRepository: DriverDueConfigRepository,
    private readonly duePaymentTransactionRepository: DriverDuePaymentTransactionRepository,
    private readonly driverAccountRepository: DriverAccountRepository,
  ) {}

  /**
   * Get due config for a city
   */
  async getDueConfig(cityId: string): Promise<DriverDueConfig | null> {
    return this.dueConfigRepository.findByCityId(cityId);
  }

  /**
   * Create or update due limit config for a city
   */
  async setDueLimit(
    cityId: string,
    data: CreateDriverDueConfigData,
  ): Promise<DriverDueConfig> {
    this.logger.log(
      `Setting due limit for city ${cityId}: ₹${data.maxDueLimit}`,
    );

    try {
      const config = await this.dueConfigRepository.upsertDueConfig(
        cityId,
        data,
      );

      this.logger.log(`Successfully set due limit for city ${cityId}`);

      return config;
    } catch (error) {
      this.logger.error(`Failed to set due limit for city ${cityId}:`, error);
      throw error;
    }
  }

  /**
   * Update due limit config
   */
  async updateDueLimit(
    cityId: string,
    data: UpdateDriverDueConfigData,
  ): Promise<DriverDueConfig> {
    const config = await this.getDueConfig(cityId);
    if (!config) {
      throw new NotFoundException(`Due config not found for city ${cityId}`);
    }

    return this.dueConfigRepository.updateDueConfig(cityId, data);
  }

  /**
   * Check if driver's balance exceeds due limit for a city
   */
  async isDriverDueExceeded(
    driverId: string,
    cityId: string,
  ): Promise<boolean> {
    const [account, config] = await Promise.all([
      this.driverAccountRepository.findByDriverId(driverId),
      this.getDueConfig(cityId),
    ]);

    if (!account || !config) {
      return false;
    }

    // Balance is negative when driver owes money
    // Check if balance is more negative than the limit
    const balance = Number(account.availableBalance);
    const limit = Number(config.maxDueLimit);

    return balance < -limit;
  }

  /**
   * Get driver's due status for a city
   */
  async getDriverDueStatus(
    driverId: string,
    cityId: string,
  ): Promise<{
    isDueExceeded: boolean;
    currentBalance: number;
    dueLimit: number;
    remainingDue: number;
  }> {
    const [account, config] = await Promise.all([
      this.driverAccountRepository.findByDriverId(driverId),
      this.getDueConfig(cityId),
    ]);

    if (!account) {
      throw new NotFoundException(`Driver account not found for ${driverId}`);
    }

    if (!config) {
      return {
        isDueExceeded: false,
        currentBalance: Number(account.availableBalance),
        dueLimit: 0,
        remainingDue: 0,
      };
    }

    const balance = Number(account.availableBalance);
    const limit = Number(config.maxDueLimit);
    const isDueExceeded = balance < -limit;
    const remainingDue = isDueExceeded ? Math.abs(balance) - limit : 0;

    return {
      isDueExceeded,
      currentBalance: balance,
      dueLimit: limit,
      remainingDue,
    };
  }

  /**
   * Record a due payment transaction
   */
  async recordDuePayment(
    data: CreateDriverDuePaymentTransactionData,
  ): Promise<DriverDuePaymentTransaction> {
    this.logger.log(
      `Recording due payment for driver ${data.driverId}: ₹${data.amount}`,
    );

    try {
      const transaction =
        await this.duePaymentTransactionRepository.createTransaction(data);

      this.logger.log(
        `Successfully recorded due payment transaction ${transaction.id}`,
      );

      return transaction;
    } catch (error) {
      this.logger.error(
        `Failed to record due payment for driver ${data.driverId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get driver's due transaction history
   */
  async getDriverDueTransactions(
    driverId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    transactions: DriverDuePaymentTransaction[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    const { transactions, total } =
      await this.duePaymentTransactionRepository.findByDriverId(
        driverId,
        page,
        limit,
      );

    return {
      transactions,
      total,
      totalPages: Math.ceil(total / limit),
      page,
      limit,
    };
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(
    transactionId: string,
    paymentStatus: string,
    balanceAfter?: number,
  ): Promise<DriverDuePaymentTransaction> {
    return this.duePaymentTransactionRepository.updateTransactionStatus(
      transactionId,
      paymentStatus,
      balanceAfter,
    );
  }

  /**
   * Get driver due transaction by id
   */
  async getDriverDueTransactionByid(
    transactionId: string,
  ): Promise<DriverDuePaymentTransaction | null> {
    return this.duePaymentTransactionRepository.findByTransactionId(
      transactionId,
    );
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DailyEarningsDto {
  @ApiProperty({
    description: 'Date of earnings',
    example: '2024-01-15',
    type: 'string',
    format: 'date',
  })
  date!: string;

  @ApiProperty({
    description: 'Total fare amount earned on this date',
    example: 1250.5,
    type: 'number',
  })
  totalFareAmount!: number;

  @ApiProperty({
    description: 'Number of completed rides on this date',
    example: 8,
    type: 'number',
  })
  completedRides!: number;

  @ApiProperty({
    description: 'Average earnings per ride on this date',
    example: 156.31,
    type: 'number',
  })
  averageEarningsPerRide!: number;
}

export class EarningsSummaryDto {
  @ApiProperty({
    description: 'Total earnings for the period',
    example: 15750.25,
    type: 'number',
  })
  totalEarnings!: number;

  @ApiProperty({
    description: 'Total number of completed rides',
    example: 125,
    type: 'number',
  })
  totalRides!: number;

  @ApiProperty({
    description: 'Average earnings per ride',
    example: 126.0,
    type: 'number',
  })
  averageEarningsPerRide!: number;

  @ApiProperty({
    description: 'Average earnings per day',
    example: 525.01,
    type: 'number',
  })
  averageEarningsPerDay!: number;

  @ApiProperty({
    description: 'Number of days worked in the period',
    example: 30,
    type: 'number',
  })
  daysWorked!: number;

  @ApiPropertyOptional({
    description: 'Best earning day',
    type: 'object',
    properties: {
      date: { type: 'string', format: 'date', example: '2024-01-15' },
      earnings: { type: 'number', example: 850.0 },
    },
  })
  bestDay?: {
    date: string;
    earnings: number;
  } | null;

  @ApiPropertyOptional({
    description: 'Worst earning day',
    type: 'object',
    properties: {
      date: { type: 'string', format: 'date', example: '2024-01-08' },
      earnings: { type: 'number', example: 125.5 },
    },
  })
  worstDay?: {
    date: string;
    earnings: number;
  } | null;
}

export class DriverAccountDto {
  @ApiProperty({
    description: 'Driver account ID',
    example: 'account-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'Driver ID',
    example: 'driver-uuid-123',
  })
  driverId!: string;

  @ApiProperty({
    description: 'Available balance in driver account',
    example: 2500.75,
    type: 'number',
  })
  availableBalance!: number;

  @ApiProperty({
    description: 'Account creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt!: string;

  @ApiProperty({
    description: 'Last updated date',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt!: string;

  @ApiPropertyOptional({
    description: 'Driver information',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'driver-uuid-123' },
      firstName: { type: 'string', example: 'John' },
      lastName: { type: 'string', example: 'Doe' },
    },
  })
  driver?:
    | {
        id: string;
        firstName: string;
        lastName: string;
      }
    | undefined;
}

export class PaymentTransactionDto {
  @ApiProperty({
    description: 'Payment ID',
    example: 'payment-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'Ride ID',
    example: 'ride-uuid-123',
  })
  rideId!: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 125.5,
    type: 'number',
  })
  amount!: number;

  @ApiProperty({
    description: 'Payment type',
    enum: ['CASH', 'ONLINE'],
    example: 'CASH',
  })
  paymentType!: string;

  @ApiPropertyOptional({
    description: 'Date when payment was received',
    example: '2024-01-15T11:15:00.000Z',
  })
  receivedAt?: string | null;

  @ApiProperty({
    description: 'Payment creation date',
    example: '2024-01-15T11:15:00.000Z',
  })
  createdAt!: string;

  @ApiPropertyOptional({
    description: 'Ride information',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'ride-uuid-123' },
      pickupLocation: {
        type: 'object',
        properties: {
          lat: { type: 'number', example: 12.9716 },
          lng: { type: 'number', example: 77.5946 },
          address: { type: 'string', example: 'Koramangala, Bangalore' },
        },
      },
      destinationLocation: {
        type: 'object',
        properties: {
          lat: { type: 'number', example: 12.9716 },
          lng: { type: 'number', example: 77.5946 },
          address: { type: 'string', example: 'Indiranagar, Bangalore' },
        },
      },
      completedAt: { type: 'string', example: '2024-01-15T11:15:00.000Z' },
    },
  })
  ride?: any;
}

export class DriverEarningsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Driver earnings retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [DailyEarningsDto],
    description: 'List of daily earnings',
  })
  data!: DailyEarningsDto[];

  @ApiProperty({
    description: 'Pagination and summary information',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 30 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    summary: EarningsSummaryDto;
  };

  @ApiProperty({ example: ************* })
  timestamp!: number;
}

export class DriverAccountResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Driver account retrieved successfully' })
  message!: string;

  @ApiProperty({ type: DriverAccountDto })
  data!: DriverAccountDto;

  @ApiProperty({ example: ************* })
  timestamp!: number;
}

export class PaymentHistoryResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Payment history retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [PaymentTransactionDto],
    description: 'List of payment transactions',
  })
  data!: PaymentTransactionDto[];

  @ApiProperty({
    description: 'Pagination information',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 125 },
      totalPages: { type: 'number', example: 13 },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({ example: ************* })
  timestamp!: number;
}

export class AggregatedDriverEarningsDto {
  @ApiProperty({
    description: 'Driver ID',
    example: 'driver-uuid-123',
  })
  driverId!: string;

  @ApiProperty({
    description: 'Driver first name',
    example: 'John',
  })
  driverFirstName!: string;

  @ApiProperty({
    description: 'Driver last name',
    example: 'Doe',
  })
  driverLastName!: string;

  @ApiProperty({
    description: 'City name',
    example: 'Bangalore',
  })
  city!: string;

  @ApiProperty({
    description: 'Total fare earned',
    example: 15750.25,
    type: 'number',
  })
  totalFare!: number;

  @ApiProperty({
    description: 'Total taxes on charges',
    example: 1575.0,
    type: 'number',
  })
  totalTaxesOnCharge!: number;

  @ApiProperty({
    description: 'Total commission deducted',
    example: 2362.5,
    type: 'number',
  })
  totalCommission!: number;

  @ApiProperty({
    description: 'Total tax on commission',
    example: 236.25,
    type: 'number',
  })
  totalTaxOnCommission!: number;

  @ApiProperty({
    description: 'Net driver earnings (fare - commission - tax on commission)',
    example: 13151.5,
    type: 'number',
  })
  netDriverEarnings!: number;

  @ApiProperty({
    description: 'Total number of completed rides',
    example: 125,
    type: 'number',
  })
  completedRides!: number;
}

export class DailyDriverEarningsDto {
  @ApiProperty({
    description: 'Date of earnings',
    example: '2024-01-15',
    type: 'string',
    format: 'date',
  })
  date!: string;

  @ApiProperty({
    description: 'Total fare for the day',
    example: 1250.5,
    type: 'number',
  })
  totalFare!: number;

  @ApiProperty({
    description: 'Total taxes on charges for the day',
    example: 125.0,
    type: 'number',
  })
  totalTaxesOnCharge!: number;

  @ApiProperty({
    description: 'Total commission for the day',
    example: 187.5,
    type: 'number',
  })
  totalCommission!: number;

  @ApiProperty({
    description: 'Total tax on commission for the day',
    example: 18.75,
    type: 'number',
  })
  totalTaxOnCommission!: number;

  @ApiProperty({
    description: 'Net driver earnings for the day',
    example: 919.25,
    type: 'number',
  })
  netDriverEarnings!: number;

  @ApiProperty({
    description: 'Number of completed rides for the day',
    example: 8,
    type: 'number',
  })
  completedRides!: number;
}

export class AggregatedDriverEarningsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Aggregated driver earnings retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [AggregatedDriverEarningsDto],
    description: 'List of aggregated driver earnings',
  })
  data!: AggregatedDriverEarningsDto[];

  @ApiProperty({
    description: 'Pagination information',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 50 },
      totalPages: { type: 'number', example: 5 },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({ example: ************* })
  timestamp!: number;
}

export class DailyDriverEarningsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Daily driver earnings retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [DailyDriverEarningsDto],
    description: 'List of daily driver earnings',
  })
  data!: DailyDriverEarningsDto[];

  @ApiProperty({
    description: 'Pagination information',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 30 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({ example: ************* })
  timestamp!: number;
}

'use client';

import { useRef } from 'react';
import { Autocomplete } from '@react-google-maps/api';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface LocationSearchProps {
  onLocationSelect: (place: google.maps.places.PlaceResult) => void;
}

export function LocationSearch({ onLocationSelect }: LocationSearchProps) {
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

  const onLoad = (autocomplete: google.maps.places.Autocomplete) => {
    autocompleteRef.current = autocomplete;

    // Configure to search for all types of places
    autocomplete.setOptions({
      fields: ['name', 'geometry', 'place_id', 'formatted_address'],
    });
  };

  const onPlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();

      if (place.geometry) {
        onLocationSelect(place);
      }
    }
  };

  return (
    <div className='bg-white rounded-lg shadow-lg border border-gray-200 px-3 py-2'>
      <div className='flex items-center gap-2'>
        <Search className='h-4 w-4 text-gray-400 flex-shrink-0' />
        <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
          <Input
            type='text'
            placeholder='Search location...'
            className='w-56 text-sm border-none shadow-none focus-visible:ring-0 p-0 h-7'
          />
        </Autocomplete>
      </div>
    </div>
  );
}

import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiResponse,
  ApiOperation,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
  PaginatedResponseDto,
} from './common-responses.dto';

// Common response decorators
export const ApiSuccessResponse = <TModel extends Type<any>>(
  model?: TModel,
  description = 'Success',
) =>
  applyDecorators(
    ApiResponse({
      status: 200,
      description,
      type: model ? ApiResponseDto<TModel> : ApiResponseDto,
    }),
  );

export const ApiCreatedResponse = <TModel extends Type<any>>(
  model?: TModel,
  description = 'Created',
) =>
  applyDecorators(
    ApiResponse({
      status: 201,
      description,
      type: model ? ApiResponseDto<TModel> : ApiResponseDto,
    }),
  );

export const ApiPaginatedResponse = <TModel extends Type<any>>(
  _model: TModel,
  description = 'Paginated results',
) =>
  applyDecorators(
    ApiResponse({
      status: 200,
      description,
      type: PaginatedResponseDto<TModel>,
    }),
  );

export const ApiBadRequestResponse = (description = 'Bad Request') =>
  applyDecorators(
    ApiResponse({
      status: 400,
      description,
      type: ApiErrorResponseDto,
    }),
  );

export const ApiUnauthorizedResponse = (description = 'Unauthorized') =>
  applyDecorators(
    ApiResponse({
      status: 401,
      description,
      type: ApiErrorResponseDto,
    }),
  );

export const ApiForbiddenResponse = (description = 'Forbidden') =>
  applyDecorators(
    ApiResponse({
      status: 403,
      description,
      type: ApiErrorResponseDto,
    }),
  );

export const ApiNotFoundResponse = (description = 'Not Found') =>
  applyDecorators(
    ApiResponse({
      status: 404,
      description,
      type: ApiErrorResponseDto,
    }),
  );

export const ApiInternalServerErrorResponse = (
  description = 'Internal Server Error',
) =>
  applyDecorators(
    ApiResponse({
      status: 500,
      description,
      type: ApiErrorResponseDto,
    }),
  );

// Common combined decorators
export const ApiCommonResponses = () =>
  applyDecorators(
    ApiBadRequestResponse(),
    ApiUnauthorizedResponse(),
    ApiInternalServerErrorResponse(),
  );

export const ApiAuthResponses = () =>
  applyDecorators(
    ApiBadRequestResponse(),
    ApiUnauthorizedResponse(),
    ApiForbiddenResponse(),
    ApiInternalServerErrorResponse(),
  );

// Swagger operation decorators
export const ApiOperationWithAuth = (summary: string, description?: string) =>
  applyDecorators(
    ApiOperation(description ? { summary, description } : { summary }),
    ApiBearerAuth('JWT-auth'),
    ApiAuthResponses(),
  );

export const ApiOperationPublic = (summary: string, description?: string) =>
  applyDecorators(
    ApiOperation(description ? { summary, description } : { summary }),
    ApiCommonResponses(),
  );

// Pagination decorators
export const ApiPaginationQuery = () =>
  applyDecorators(
    ApiQuery({
      name: 'page',
      required: false,
      type: Number,
      description: 'Page number',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: false,
      type: Number,
      description: 'Number of items per page',
      example: 10,
    }),
    ApiQuery({
      name: 'search',
      required: false,
      type: String,
      description: 'Search term',
    }),
    ApiQuery({
      name: 'sortBy',
      required: false,
      type: String,
      description: 'Field to sort by',
    }),
    ApiQuery({
      name: 'sortOrder',
      required: false,
      enum: ['asc', 'desc'],
      description: 'Sort order',
    }),
  );

// ID parameter decorator
export const ApiIdParam = (name = 'id', description = 'Resource ID') =>
  applyDecorators(
    ApiParam({
      name,
      type: 'string',
      description,
      format: 'uuid',
    }),
  );

// Tag with auth decorator
export const ApiTagsWithAuth = (tag: string) =>
  applyDecorators(ApiTags(tag), ApiBearerAuth('JWT-auth'));

import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsUUID } from 'class-validator';
import { WaitingMeterType } from './start-waiting.dto';

/**
 * DTO for ending a waiting period
 */
export class EndWaitingDto {
  @ApiProperty({
    description: 'The ID of the ride',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  rideId!: string;

  @ApiProperty({
    description: 'The type of waiting meter to end',
    enum: WaitingMeterType,
    example: WaitingMeterType.PICKUP_WAIT_TIME,
  })
  @IsEnum(WaitingMeterType)
  meterType!: WaitingMeterType;

  @ApiProperty({
    description: 'The ID of the meter log entry to end',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  meterLogId!: string;
}

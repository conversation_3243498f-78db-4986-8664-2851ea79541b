import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CountryService } from '../../../../../libs/shared/src/modules/country/country.service';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { Ability } from '@shared/shared/casl/decorators/ability.decorator';
import { CaslAbilityGuard } from '../../common/guards/casl-ability.guard';

@ApiTags('Countries')
@UseGuards(JwtAuthGuard, CaslAbilityGuard)
@ApiBearerAuth()
@Controller('countries')
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new country' })
  @ApiResponse({ status: 201, type: ApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @Ability('country:create')
  async create(@Body() body: CreateCountryDto) {
    const data = await this.countryService.createCountry(body);
    return {
      success: true,
      message: 'Country created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all countries' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @Ability('country:list')
  async findAll() {
    const data = await this.countryService.findAllCountries();
    return {
      success: true,
      message: 'Countries fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated countries' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  @Ability('country:list')
  async paginate(@Query() query: PaginationDto) {
    const result = await this.countryService.paginateCountries(
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'Countries paginated successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a country by ID' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @Ability('country:list')
  async findOne(@Param('id') id: string) {
    const data = await this.countryService.findCountryById(id);
    return {
      success: true,
      message: 'Country fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a country' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @Ability('country:edit')
  async update(@Param('id') id: string, @Body() body: UpdateCountryDto) {
    const data = await this.countryService.updateCountry(id, body);
    return {
      success: true,
      message: 'Country updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a country (soft delete)' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @Ability('country:manage')
  async remove(@Param('id') id: string) {
    const data = await this.countryService.deleteCountry(id);
    return {
      success: true,
      message: 'Country deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

import { Module } from '@nestjs/common';
import { DriverVehicleController } from './driver-vehicle.controller';
import { DriverVehicleModule as SharedDriverVehicleModule } from '@shared/shared/modules/driver-vehicle/driver-vehicle.module';
import { DriverVehicleDocumentModule as SharedDriverVehicleDocumentModule } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.module';

@Module({
  imports: [SharedDriverVehicleModule, SharedDriverVehicleDocumentModule],
  controllers: [DriverVehicleController],
})
export class ApiDriverVehicleModule {}

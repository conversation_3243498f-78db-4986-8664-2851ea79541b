'use client';

import { useCallback, useEffect, useState } from 'react';
import { GoogleMap, DirectionsRenderer, Marker } from '@react-google-maps/api';
import { Location } from '../types/ride';
import { Button } from '@/components/ui/button';
import { Maximize2, Minimize2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface RideRouteMapProps {
   pickupLocation: Location;
   destinationLocation: Location;
   stops?: Location[] | null;
}

const mapContainerStyle = {
   width: '100%',
   height: '100%',
};

const defaultMapOptions = {
   gestureHandling: 'greedy',
   disableDefaultUI: true,
   zoomControl: true,
   mapTypeControl: false,
   streetViewControl: false,
   fullscreenControl: false,
};

export function RideRouteMap({ pickupLocation, destinationLocation, stops }: RideRouteMapProps) {
   const [directions, setDirections] = useState<google.maps.DirectionsResult | null>(null);
   const [isFullscreen, setIsFullscreen] = useState(false);
   const [mapCenter, _setMapCenter] = useState<{ lat: number; lng: number }>(pickupLocation);
   const [mapZoom, _setMapZoom] = useState(13);
   const [isCalculating, setIsCalculating] = useState(false);
   const [error, setError] = useState<string | null>(null);

   const calculateRoute = useCallback(() => {
      if (!window.google) return;

      setIsCalculating(true);
      setError(null);

      const directionsService = new google.maps.DirectionsService();

      // Build waypoints from stops if they exist
      const waypoints: google.maps.DirectionsWaypoint[] =
         stops?.map(stop => ({
            location: new google.maps.LatLng(stop.lat, stop.lng),
            stopover: true,
         })) || [];

      const request: google.maps.DirectionsRequest = {
         origin: new google.maps.LatLng(pickupLocation.lat, pickupLocation.lng),
         destination: new google.maps.LatLng(destinationLocation.lat, destinationLocation.lng),
         waypoints: waypoints,
         travelMode: google.maps.TravelMode.DRIVING,
         optimizeWaypoints: false, // Keep stops in order
      };

      directionsService.route(request, (result, status) => {
         setIsCalculating(false);
         if (status === 'OK' && result) {
            setDirections(result);
         } else {
            setError('Could not calculate route. Please try again.');
            console.error('Directions request failed:', status);
         }
      });
   }, [pickupLocation, destinationLocation, stops]);

   // Calculate route when component mounts or locations change
   useEffect(() => {
      calculateRoute();
   }, [calculateRoute]);

   const onMapLoad = useCallback(
      (map: google.maps.Map) => {
         // Auto-fit bounds to show all locations
         const bounds = new google.maps.LatLngBounds();
         bounds.extend(new google.maps.LatLng(pickupLocation.lat, pickupLocation.lng));
         bounds.extend(new google.maps.LatLng(destinationLocation.lat, destinationLocation.lng));

         stops?.forEach(stop => {
            bounds.extend(new google.maps.LatLng(stop.lat, stop.lng));
         });

         map.fitBounds(bounds);
      },
      [pickupLocation, destinationLocation, stops]
   );

   const MapContent = () => (
      <div className='relative w-full h-full'>
         <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={mapCenter}
            zoom={mapZoom}
            onLoad={onMapLoad}
            options={defaultMapOptions}
         >
            {/* Render the route */}
            {directions && (
               <DirectionsRenderer
                  directions={directions}
                  options={{
                     suppressMarkers: false,
                     polylineOptions: {
                        strokeColor: '#3B82F6',
                        strokeWeight: 5,
                        strokeOpacity: 0.7,
                     },
                  }}
               />
            )}

            {/* Show markers if directions haven't loaded yet */}
            {!directions && (
               <>
                  <Marker
                     position={{ lat: pickupLocation.lat, lng: pickupLocation.lng }}
                     icon={{
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#10B981',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                     }}
                     title='Pickup Location'
                  />
                  {stops?.map((stop, index) => (
                     <Marker
                        key={index}
                        position={{ lat: stop.lat, lng: stop.lng }}
                        icon={{
                           path: google.maps.SymbolPath.CIRCLE,
                           scale: 8,
                           fillColor: '#3B82F6',
                           fillOpacity: 1,
                           strokeColor: '#ffffff',
                           strokeWeight: 2,
                        }}
                        title={`Stop ${index + 1}`}
                     />
                  ))}
                  <Marker
                     position={{ lat: destinationLocation.lat, lng: destinationLocation.lng }}
                     icon={{
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#EF4444',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                     }}
                     title='Destination'
                  />
               </>
            )}
         </GoogleMap>

         {/* Fullscreen toggle button */}
         {!isFullscreen && (
            <Button
               variant='outline'
               size='icon'
               className='absolute top-2 right-2 bg-white shadow-md hover:bg-gray-50'
               onClick={() => setIsFullscreen(true)}
            >
               <Maximize2 className='h-4 w-4' />
            </Button>
         )}

         {/* Loading overlay */}
         {isCalculating && (
            <div className='absolute inset-0 bg-white/50 flex items-center justify-center'>
               <div className='bg-white rounded-lg shadow-lg p-4 text-sm text-gray-700'>
                  Calculating route...
               </div>
            </div>
         )}

         {/* Error message */}
         {error && (
            <div className='absolute bottom-4 left-4 right-4 bg-red-50 border border-red-200 rounded-lg p-3 text-sm text-red-700'>
               {error}
            </div>
         )}
      </div>
   );

   return (
      <>
         {/* Regular view */}
         <div className='h-64 w-full rounded-lg overflow-hidden border border-gray-200'>
            <MapContent />
         </div>

         {/* Fullscreen dialog */}
         <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
            <DialogContent showCloseButton={false} className='!max-w-[85vw] w-full h-[85vh] p-0'>
               <DialogHeader className='p-6 pb-0'>
                  <div className='flex items-center justify-between'>
                     <DialogTitle>Route Map</DialogTitle>
                     <Button variant='outline' size='icon' onClick={() => setIsFullscreen(false)}>
                        <Minimize2 className='h-4 w-4' />
                     </Button>
                  </div>
               </DialogHeader>
               <div className='h-[calc(85vh-5rem)] p-6 pt-4'>
                  <MapContent />
               </div>
            </DialogContent>
         </Dialog>
      </>
   );
}

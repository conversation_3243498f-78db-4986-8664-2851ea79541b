import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { AdminListParams, AdminListResponse, AdminProfileResponse } from '../types/admin';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';

export const useGetAdminsForRole = (roleIdentifier: string | null, params: AdminListParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();
   const hasCityAdminPermission = hasPermission(RBAC_PERMISSIONS.CITY_ADMIN.LIST);
   const hasSubAdminPermission = hasPermission(RBAC_PERMISSIONS.SUB_ADMIN.LIST);

   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['admins', 'role', roleIdentifier, params],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<AdminListResponse> => {
         return apiClient.get(`/roles/identifier/${roleIdentifier}/admins`, {
            params: {
               page: params.page || 1,
               limit: params.limit || 10,
               search: params.search,
               status: params.status,
            },
         });
      },
      enabled:
         !!roleIdentifier &&
         (roleIdentifier === ROLE_IDENTIFIERS.CITY_ADMIN
            ? hasCityAdminPermission
            : roleIdentifier === ROLE_IDENTIFIERS.SUB_ADMIN
            ? hasSubAdminPermission
            : true),
   });
};

export const useGetAdminProfile = (profileId: string | null, enabled: boolean = true) => {
   return useQuery({
      queryKey: ['admin-profile', profileId],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<AdminProfileResponse> => {
         return apiClient.get(`/admin/sub-admin/profile/${profileId}`);
      },
      enabled: !!profileId && enabled,
   });
};

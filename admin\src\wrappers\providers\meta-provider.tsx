'use client';

import Head from 'next/head';

const MetaProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Head>
        <title>Tukxi Admin</title>
        <meta name="viewport" content="initial-scale=1.0, width=device-width" />
        <meta
          httpEquiv="Content-Security-Policy"
          content="upgrade-insecure-requests"
        ></meta>
        <meta name="description" content="Tukxi Admin" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {children}
    </>
  );
};

export default MetaProvider;

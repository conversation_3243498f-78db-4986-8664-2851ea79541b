'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListProduct } from '../api/queries';
import { useListVehicleCategory } from '@/modules/vehicle-category/api/queries';
import { useListProductService } from '@/modules/product-service/api/queries';
import { ProductModal } from '../components/product-modal';
import { ProductFilters } from '../components/product-filters';
import { ProductTable } from '../components/product-table';

export function ProductPage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [vehicleTypeId, setVehicleTypeId] = useState<string | undefined>(undefined);
   const [productServiceId, setProductServiceId] = useState<string | undefined>(undefined);
   const [isEnabled, setIsEnabled] = useState<string | undefined>(undefined);

   // preload vehicle types and product services
   useListVehicleCategory({});
   useListProductService({});

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleVehicleTypeChange = (value: string | undefined) => {
      setVehicleTypeId(value);
      setPage(1);
   };

   const handleProductServiceChange = (value: string | undefined) => {
      setProductServiceId(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setIsEnabled(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setVehicleTypeId(undefined);
      setProductServiceId(undefined);
      setIsEnabled(undefined);
      setPage(1);
   };

   const listProductQuery = useListProduct({
      page,
      limit,
      search: search || undefined,
      vehicleTypeId: vehicleTypeId || undefined,
      productServiceId: productServiceId || undefined,
      isEnabled: isEnabled === 'true' ? 'true' : undefined,
      isDisabled: isEnabled === 'false' ? 'true' : undefined,
      sortOrder: 'asc',
   });

   // Calculate total product count from API meta data
   const totalProducts = listProductQuery.data?.meta?.total || 0;

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Products</h2>
            <div className='flex items-center gap-4'>
               {/* Product Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Products</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalProducts}
                     </span>
                  </div>
               </div>
               <ProductModal mode='create' />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <ProductFilters
               search={search}
               vehicleTypeId={vehicleTypeId}
               productServiceId={productServiceId}
               isEnabled={isEnabled}
               onSearchChange={handleSearchChange}
               onVehicleTypeChange={handleVehicleTypeChange}
               onProductServiceChange={handleProductServiceChange}
               onStatusChange={handleStatusChange}
               isLoading={listProductQuery.isFetching && !listProductQuery.isLoading}
            />

            <ProductTable
               data={listProductQuery.data}
               isLoading={listProductQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search || !!vehicleTypeId || !!productServiceId || !!isEnabled}
               hasSearch={!!search}
               hasStatus={!!isEnabled}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}

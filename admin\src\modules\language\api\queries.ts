import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { LanguageResponse, ListLanguageParams, ListLanguageResponse } from '../types/language';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListLanguage = ({ page, limit, sortBy, sortOrder }: ListLanguageParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      enabled: hasPermission(RBAC_PERMISSIONS.LANGUAGE.LIST),
      placeholderData: keepPreviousData,
      queryKey: ['languages', page, limit, sortBy, sortOrder],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListLanguageResponse> => {
         return apiClient.get('/languages', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetLanguage = (id: string | null) => {
   return useQuery({
      queryKey: ['language', id],
      queryFn: (): Promise<LanguageResponse> => {
         return apiClient.get(`/languages/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

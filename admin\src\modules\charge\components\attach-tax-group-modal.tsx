'use client';

import { useState, useEffect, useId } from 'react';
import { CheckIcon, ChevronDownIcon } from 'lucide-react';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAttachTaxGroup } from '../api/charge-tax-group-mutations';
import { useListTaxGroup } from '@/modules/tax-group/api/queries';
import { cn } from '@/lib/utils';
import { TaxGroup } from '@/modules/tax-group/types/tax-group';

interface AttachTaxGroupModalProps {
   chargeId: string;
   chargeName: string;
   isOpen: boolean;
   onClose: () => void;
}

export function AttachTaxGroupModal({
   chargeId,
   chargeName,
   isOpen,
   onClose
}: AttachTaxGroupModalProps) {
   const id = useId();
   const [open, setOpen] = useState<boolean>(false);
   const [selectedTaxGroupId, setSelectedTaxGroupId] = useState<string>('');
   const [searchQuery, setSearchQuery] = useState<string>('');

   const queryClient = useQueryClient();
   const attachTaxGroupMutation = useAttachTaxGroup();
   const { data: taxGroupsData, isLoading: isLoadingTaxGroups } = useListTaxGroup({
      page: 1,
      limit: 50,
      search: searchQuery || undefined,
   });

   const availableTaxGroups = taxGroupsData?.data || [];

   // Reset form when modal opens
   useEffect(() => {
      if (isOpen) {
         setSelectedTaxGroupId('');
         setSearchQuery('');
         setOpen(false);
      }
   }, [isOpen]);

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (!selectedTaxGroupId) {
         toast.error('Please select a tax group');
         return;
      }

      attachTaxGroupMutation.mutate(
         {
            chargeId,
            taxGroupId: selectedTaxGroupId,
         },
         {
            onSuccess: () => {
               toast.success('Tax group attached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges'] });
               queryClient.invalidateQueries({ queryKey: ['all-charges'] });
               onClose();
            },
            onError: (error: any) => {
               const errorMessage = error?.response?.data?.message || 'Failed to add tax group';
               toast.error(errorMessage);
            },
         }
      );
   };

   const getTaxGroupDisplay = (taxGroup: TaxGroup) => {
      return `${taxGroup.name} (${taxGroup.totalPercentage}%)`;
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Add Tax Group</DialogTitle>
               <p className='text-sm text-muted-foreground mt-2'>
                  Select a tax group to add to "{chargeName}"
               </p>
            </DialogHeader>

            <form onSubmit={handleSubmit}>
               <div className='space-y-4 py-4'>
                  {/* Tax Group Selection */}
                  <div className='space-y-2'>
                     <Label htmlFor={id}>Select Tax Group</Label>
                     <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                           <Button
                              id={id}
                              variant='outline'
                              role='combobox'
                              aria-expanded={open}
                              className='bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]'
                           >
                              <span
                                 className={cn(
                                    'truncate',
                                    !selectedTaxGroupId && 'text-muted-foreground'
                                 )}
                              >
                                 {selectedTaxGroupId
                                    ? getTaxGroupDisplay(
                                         availableTaxGroups.find(
                                            (t: TaxGroup) => t.id === selectedTaxGroupId
                                         )!
                                      )
                                    : 'Select a tax group'}
                              </span>
                              <ChevronDownIcon
                                 size={16}
                                 className='text-muted-foreground/80 shrink-0'
                                 aria-hidden='true'
                              />
                           </Button>
                        </PopoverTrigger>
                        <PopoverContent
                           className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
                           align='start'
                        >
                           <Command shouldFilter={false}>
                              <CommandInput
                                 placeholder='Search tax group...'
                                 value={searchQuery}
                                 onValueChange={setSearchQuery}
                              />
                              {isLoadingTaxGroups && (
                                 <div className='flex items-center justify-center py-4'>
                                    <Spinner className='h-4 w-4' />
                                 </div>
                              )}
                              <CommandList>
                                 <CommandEmpty>
                                    {searchQuery ? 'No tax group found matching your search.' : 'No tax groups available.'}
                                 </CommandEmpty>
                                 <CommandGroup>
                                    {availableTaxGroups.map((taxGroup: TaxGroup) => (
                                       <CommandItem
                                          key={taxGroup.id}
                                          value={`${taxGroup.name} ${taxGroup.totalPercentage}`}
                                          onSelect={() => {
                                             setSelectedTaxGroupId(taxGroup.id);
                                             setOpen(false);
                                          }}
                                       >
                                          <div className='flex flex-col flex-1'>
                                             <span className='font-medium'>{taxGroup.name}</span>
                                             <span className='text-xs text-muted-foreground'>
                                                Total: {taxGroup.totalPercentage}%
                                             </span>
                                          </div>
                                          {selectedTaxGroupId === taxGroup.id && (
                                             <CheckIcon size={16} className='ml-auto' />
                                          )}
                                       </CommandItem>
                                    ))}
                                 </CommandGroup>
                              </CommandList>
                           </Command>
                        </PopoverContent>
                     </Popover>
                  </div>
               </div>

               <DialogFooter>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onClose}
                     disabled={attachTaxGroupMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={attachTaxGroupMutation.isPending || !selectedTaxGroupId}
                  >
                     {attachTaxGroupMutation.isPending ? (
                        <>
                           <Spinner className='h-4 w-4 mr-2' />
                           Attaching...
                        </>
                     ) : (
                        'Add Tax Group'
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}

export interface DriverProduct {
  id: string;
  userProfileId: string;
  cityProductId: string;
  driverVehicleId: string;
  isPrimary: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  cityProduct?: {
    id: string;
    cityId: string;
    productId: string;
    vehicleTypeId: string;
    isEnabled: boolean;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string | null;
    city?: {
      id: string;
      name: string;
      state?: string;
      country?: string;
    };
    product?: {
      id: string;
      name: string;
      description?: string;
      icon?: string;
    };
    vehicleType?: {
      id: string;
      name: string;
      description?: string;
    };
  };
  userProfile?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface ListDriverProductParams {
  page?: number;
  limit?: number;
  productName?: string;
}

export interface ListDriverProductResponse {
  success: boolean;
  message: string;
  data: {
    data: DriverProduct[];
    meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
}

// Vehicle-specific endpoint has a different response structure
export interface ListVehicleProductResponse {
  success: boolean;
  message: string;
  data: DriverProduct[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  timestamp: number;
}

export interface DriverProductResponse {
  success: boolean;
  message: string;
  data: DriverProduct;
  timestamp: number;
}

export interface AddCityProductsToDriverRequest {
  driverVehicleId: string;
  cityProductIds: string[];
}

export interface RemoveCityProductsFromDriverRequest {
  driverVehicleId: string;
  cityProductIds: string[];
}

export interface UpdatePrimaryStatusRequest {
  driverVehicleId: string;
  cityProductId: string;
  isPrimary: boolean;
}
'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateRole, useUpdateRole } from '../api/mutations';
import { useGetRole } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const roleSchema = z.object({
   name: z
      .string()
      .min(1, 'Role name is required')
      .min(2, 'Role name must be at least 2 characters')
      .max(50, 'Role name must not exceed 50 characters')
      .regex(/^[a-zA-Z\-@_]+$/, 'Role name can only contain letters, hyphens, at symbols, and underscores'),
   description: z
      .string()
      .optional()
      .refine((val) => !val || val.length >= 5, {
         message: 'Description must be at least 5 characters if provided',
      })
      .refine((val) => !val || val.length <= 255, {
         message: 'Description must not exceed 255 characters',
      }),
});

type RoleFormValues = z.infer<typeof roleSchema>;

interface RoleModalProps {
   roleId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const RoleModal = ({ roleId, isOpen, onClose, mode = 'create' }: RoleModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const createRoleMutation = useCreateRole();
   const updateRoleMutation = useUpdateRole();
   const roleQuery = useGetRole(roleId || null);
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<RoleFormValues>({
      resolver: zodResolver(roleSchema),
      defaultValues: {
         name: '',
         description: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   // Reset form when roleId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && roleQuery.data?.data) {
         const role = roleQuery.data.data;
         reset({
            name: role.name,
            description: role.description || '',
         });
      } else if (mode === 'create') {
         reset({
            name: '',
            description: '',
         });
      }
   }, [roleQuery.data, reset, mode]);

   const onSubmit = async (data: RoleFormValues) => {
      if (mode === 'create') {
         createRoleMutation.mutate(data, {
            onSuccess: () => {
               toast.success('Role created successfully');
               handleClose();
               queryClient.invalidateQueries({ queryKey: ['roles'] });
            },
         });
      } else if (mode === 'edit' && roleId) {
         updateRoleMutation.mutate(
            { id: roleId, ...data },
            {
               onSuccess: () => {
                  toast.success('Role updated successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['roles'] });
                  queryClient.invalidateQueries({ queryKey: ['role', roleId] });
               },
            }
         );
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
   };

   const isLoading = mode === 'create' ? createRoleMutation.isPending : updateRoleMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && roleQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>Please wait while we load the role data.</DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && roleQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load role data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load role data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-md'
      >
         <DialogHeader>
            <DialogTitle>
               {mode === 'create' ? 'Create New Role' : 'Edit Role'}
            </DialogTitle>
            <DialogDescription>
               {mode === 'create' 
                  ? 'Add a new role to the system' 
                  : 'Update the role information'
               }
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Role Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. Administrator, User, Manager'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='description'>Description</Label>
               <Controller
                  control={control}
                  name='description'
                  render={({ field }) => (
                     <Textarea
                        id='description'
                        placeholder='Describe the role and its responsibilities'
                        {...field}
                        className='w-full'
                        rows={3}
                     />
                  )}
               />
               {errors.description && <ErrorMessage error={errors.description} />}
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     mode === 'create' ? 'Create Role' : 'Update Role'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, return button and dialog separately
   if (mode === 'create' && isOpen === undefined) {
      return (
         <>
            <Button
               className='cursor-pointer'
               variant='outline'
               onClick={() =>
                  withPermission(RBAC_PERMISSIONS.ROLES.CREATE, () => setModalOpen(true))
               }
            >
               <Plus />
               Add Role
            </Button>
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
               {content}
            </Dialog>
         </>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};
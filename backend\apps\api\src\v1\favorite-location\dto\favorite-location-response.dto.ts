import { ApiProperty } from '@nestjs/swagger';

export class LocationPointResponseDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
  })
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
  })
  lng!: number;
}

export class FavoriteLocationMetaResponseDto {
  @ApiProperty({
    description: 'Address of the location',
    example: '123 Main Street, Bangalore, Karnataka, India',
    required: false,
  })
  address?: string;

  // Allow additional properties
  [key: string]: any;
}

export class UserProfileBasicResponseDto {
  @ApiProperty({
    description: 'User profile ID',
    example: 'user-profile-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'First name',
    example: 'John',
  })
  firstName!: string;

  @ApiProperty({
    description: 'Last name',
    example: 'Doe',
  })
  lastName!: string;
}

export class FavoriteLocationResponseDto {
  @ApiProperty({
    description: 'Favorite location ID',
    example: 'favorite-location-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'User profile ID',
    example: 'user-profile-uuid-123',
  })
  userProfileId!: string;

  @ApiProperty({
    description: 'Name of the favorite location',
    example: 'Home',
  })
  name!: string;

  @ApiProperty({
    description: 'Description of the favorite location',
    example: 'My home address in Bangalore',
    required: false,
  })
  description?: string | null;

  @ApiProperty({
    description: 'Location coordinates and address',
    type: LocationPointResponseDto,
  })
  location!: LocationPointResponseDto;

  @ApiProperty({
    description: 'Additional metadata for the favorite location',
    type: FavoriteLocationMetaResponseDto,
    required: false,
    example: {
      address: '123 Main Street, Bangalore, Karnataka, India',
    },
  })
  meta?: FavoriteLocationMetaResponseDto | null;

  @ApiProperty({
    description: 'User profile information',
    type: UserProfileBasicResponseDto,
    required: false,
  })
  userProfile?: UserProfileBasicResponseDto;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  updatedAt!: Date;
}

export class FavoriteLocationListResponseDto {
  @ApiProperty({
    description: 'List of favorite locations',
    type: [FavoriteLocationResponseDto],
  })
  data!: FavoriteLocationResponseDto[];

  @ApiProperty({
    description: 'Total number of favorite locations',
    example: 25,
  })
  total!: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit!: number;
}

export class CreateFavoriteLocationResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Favorite location created successfully' })
  message!: string;

  @ApiProperty({ type: FavoriteLocationResponseDto })
  data!: FavoriteLocationResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class FavoriteLocationDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Favorite location retrieved successfully' })
  message!: string;

  @ApiProperty({ type: FavoriteLocationResponseDto })
  data!: FavoriteLocationResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class FavoriteLocationPaginatedResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Favorite locations retrieved successfully' })
  message!: string;

  @ApiProperty({ type: FavoriteLocationListResponseDto })
  data!: FavoriteLocationListResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

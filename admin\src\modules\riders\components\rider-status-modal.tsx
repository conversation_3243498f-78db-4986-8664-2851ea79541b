'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Ban } from 'lucide-react';
import { useUpdateRiderStatus } from '../api/mutations';
import { toast } from 'sonner';

interface RiderStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  riderId: string;
  riderName: string;
  currentStatus: 'active' | 'inactive' | 'pending' | 'disabled' | 'invited' | undefined;
  newStatus: 'active' | 'inactive' | 'disabled';
  onStatusUpdated: () => void;
}

export function RiderStatusModal({
  isOpen,
  onClose,
  riderId,
  riderName,
  currentStatus: _currentStatus,
  newStatus,
  onStatusUpdated,
}: RiderStatusModalProps) {
  const updateRiderStatusMutation = useUpdateRiderStatus();

  const handleConfirm = () => {
    updateRiderStatusMutation.mutate(
      {
        id: riderId,
        status: newStatus,
      },
      {
        onSuccess: () => {
          let message = 'Rider status updated successfully';
          if (newStatus === 'active') message = 'Rider activated successfully';
          else if (newStatus === 'inactive') message = 'Rider deactivated successfully';
          else if (newStatus === 'disabled') message = 'Rider disabled successfully';

          toast.success(message);
          onStatusUpdated();
          onClose();
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.message || 'Failed to update rider status');
        },
      }
    );
  };

  const getActionText = () => {
    switch (newStatus) {
      case 'active':
        return { action: 'activate', capitalized: 'Activate' };
      case 'inactive':
        return { action: 'deactivate', capitalized: 'Deactivate' };
      case 'disabled':
        return { action: 'disable', capitalized: 'Disable' };
      default:
        return { action: 'update', capitalized: 'Update' };
    }
  };

  const { action, capitalized } = getActionText();

  const getIcon = () => {
    switch (newStatus) {
      case 'active':
        return (
          <div className='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center'>
            <CheckCircle className='w-5 h-5 text-green-600' />
          </div>
        );
      case 'disabled':
        return (
          <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
            <Ban className='w-5 h-5 text-red-600' />
          </div>
        );
      default:
        return (
          <div className='w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center'>
            <AlertTriangle className='w-5 h-5 text-yellow-600' />
          </div>
        );
    }
  };

  const getWarningNote = () => {
    switch (newStatus) {
      case 'active':
        return (
          <div className='mt-3 p-3 bg-blue-50 rounded-lg'>
            <div className='text-sm text-blue-800'>
              <strong>Note:</strong> Activating the rider will allow them to book and request rides.
            </div>
          </div>
        );
      case 'inactive':
        return (
          <div className='mt-3 p-3 bg-yellow-50 rounded-lg'>
            <div className='text-sm text-yellow-800'>
              <strong>Note:</strong> Deactivating will temporarily prevent the rider from booking new
              rides.
            </div>
          </div>
        );
      case 'disabled':
        return (
          <div className='mt-3 p-3 bg-red-50 rounded-lg'>
            <div className='text-sm text-red-800'>
              <strong>Warning:</strong> Disabling the rider will permanently block their account from
              accessing the platform.
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const getButtonColor = () => {
    switch (newStatus) {
      case 'active':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'disabled':
        return 'bg-red-600 hover:bg-red-700 text-white';
      default:
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-3 mb-2'>
            {getIcon()}
            <DialogTitle className='text-lg font-semibold'>{capitalized} Rider</DialogTitle>
          </div>
          <DialogDescription className='text-base text-gray-600'>
            Are you sure you want to {action} <span className='font-medium'>{riderName}</span>?
          </DialogDescription>
          {getWarningNote()}
        </DialogHeader>
        <DialogFooter className='flex gap-3 sm:gap-3'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={updateRiderStatusMutation.isPending}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            onClick={handleConfirm}
            disabled={updateRiderStatusMutation.isPending}
            className={`flex-1 ${getButtonColor()}`}
          >
            {updateRiderStatusMutation.isPending ? 'Processing...' : `${capitalized} Rider`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

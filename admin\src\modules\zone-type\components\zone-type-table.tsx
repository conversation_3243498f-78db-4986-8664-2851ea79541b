'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useActivateZoneType, useDeactivateZoneType, useDeleteZoneType } from '../api/mutations';
import { ListZoneTypeResponse, ZoneType } from '../types/zone-type';
import { ZoneTypeModal } from './zone-type-modal';
import { ZoneTypeTableEmpty } from './zone-type-table-empty';
import { ZoneTypeTableLoading } from './zone-type-table-loading';
import { ZoneTypeToggleModal } from './zone-type-toggle-modal';
import { ZoneTypeDeleteModal } from './zone-type-delete-modal';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   activateZoneTypeMutation,
   deactivateZoneTypeMutation,
   deleteZoneTypeMutation,
   handleToggleClick,
   handleEditClick,
   handleDeleteClick,
   zoneTypeToToggle,
   zoneTypeToDelete,
   withPermission,
}: {
   handleToggleClick: (zoneType: ZoneType) => void;
   handleEditClick: (id: string) => void;
   handleDeleteClick: (zoneType: ZoneType) => void;
   activateZoneTypeMutation: any;
   deactivateZoneTypeMutation: any;
   deleteZoneTypeMutation: any;
   zoneTypeToToggle: ZoneType | null;
   zoneTypeToDelete: ZoneType | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<ZoneType>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const zoneType = row.original as ZoneType;
         return (
            <div className='text-left max-w-[200px]'>
               <div className='text-sm font-medium break-words'>{zoneType.name}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const zoneType = row.original as ZoneType;
         return (
            <div className='text-left max-w-[300px]'>
               <div className='text-sm text-gray-600 break-words'>
                  {zoneType.description || 'No description'}
               </div>
            </div>
         );
      },
      size: 150,
   },
   // {
   //    accessorKey: 'algorithm',
   //    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Algorithm</div>,
   //    cell: ({ row }) => {
   //       const zoneType = row.original as ZoneType;
   //       return (
   //          <div className='text-left'>
   //             <span className='px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
   //                {zoneType.algorithm}
   //             </span>
   //          </div>
   //       );
   //    },
   //    size: 120,
   // },
   {
      accessorKey: 'isActive',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const zoneType = row.original as ZoneType;

         return (
            <div className='flex justify-center'>
               <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                     zoneType.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}
               >
                  {zoneType.isActive ? 'Active' : 'Inactive'}
               </span>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'createdAt',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Created Date</div>
      ),
      cell: ({ row }) => {
         const zoneType = row.original as ZoneType;
         const date = new Date(zoneType.createdAt);
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {date.toLocaleDateString('en-US', {
                     year: 'numeric',
                     month: 'short',
                     day: 'numeric',
                  })}
               </div>
            </div>
         );
      },
      size: 120,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const zoneType = row.original as ZoneType;
         const isToggling =
            zoneTypeToToggle?.id === zoneType.id &&
            (activateZoneTypeMutation.isPending || deactivateZoneTypeMutation.isPending);
         const isDeleting =
            zoneTypeToDelete?.id === zoneType.id && deleteZoneTypeMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.ZONE_TYPE.EDIT, () =>
                        handleEditClick(zoneType.id)
                     );
                  }}
                  disabled={isToggling || isDeleting}
               >
                  Edit
               </button>
               <button
                  className={`text-sm font-medium border px-3 py-1 rounded-md transition-colors bg-white cursor-pointer ${
                     zoneType.isActive
                        ? 'text-red-600 hover:text-red-700 border-red-300 hover:border-red-400'
                        : 'text-green-600 hover:text-green-700 border-green-300 hover:border-green-400'
                  }`}
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.ZONE_TYPE.STATUS_UPDATE, () =>
                        handleToggleClick(zoneType)
                     );
                  }}
                  disabled={isToggling || isDeleting}
               >
                  {isToggling ? '...' : zoneType.isActive ? 'Deactivate' : 'Activate'}
               </button>
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.ZONE_TYPE.DELETE, () =>
                        handleDeleteClick(zoneType)
                     );
                  }}
                  disabled={isToggling || isDeleting}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 250,
   },
];

interface ZoneTypeTableProps {
   data: ListZoneTypeResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters?: boolean;
   hasSearch?: boolean;
   hasStatus?: boolean;
   onClearFilters?: () => void;
}

export function ZoneTypeTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters: _hasFilters,
   hasSearch: _hasSearch,
   hasStatus: _hasStatus,
   onClearFilters: _onClearFilters,
}: ZoneTypeTableProps) {
   const [zoneTypeToToggle, setZoneTypeToToggle] = useState<ZoneType | null>(null);
   const [zoneTypeToEdit, setZoneTypeToEdit] = useState<string | null>(null);
   const [zoneTypeToDelete, setZoneTypeToDelete] = useState<ZoneType | null>(null);
   const activateZoneTypeMutation = useActivateZoneType();
   const deactivateZoneTypeMutation = useDeactivateZoneType();
   const deleteZoneTypeMutation = useDeleteZoneType();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleToggleClick = (zoneType: ZoneType) => {
      setZoneTypeToToggle(zoneType);
   };

   const handleEditClick = (id: string) => {
      setZoneTypeToEdit(id);
   };

   const handleDeleteClick = (zoneType: ZoneType) => {
      setZoneTypeToDelete(zoneType);
   };

   const handleToggleConfirm = () => {
      if (!zoneTypeToToggle) return;

      const mutation = zoneTypeToToggle.isActive
         ? deactivateZoneTypeMutation
         : activateZoneTypeMutation;
      const action = zoneTypeToToggle.isActive ? 'deactivated' : 'activated';

      mutation.mutate(zoneTypeToToggle.id, {
         onSuccess: () => {
            toast.success(`Zone type ${action} successfully`);
            queryClient.invalidateQueries({ queryKey: ['zone-types'] });
         },
         onSettled: () => {
            setZoneTypeToToggle(null);
         },
      });
   };

   const handleDeleteConfirm = () => {
      if (!zoneTypeToDelete) return;

      deleteZoneTypeMutation.mutate(zoneTypeToDelete.id, {
         onSuccess: () => {
            toast.success('Zone type deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['zone-types'] });
         },
         onSettled: () => {
            setZoneTypeToDelete(null);
         },
      });
   };

   const columns = getColumns({
      activateZoneTypeMutation,
      deactivateZoneTypeMutation,
      deleteZoneTypeMutation,
      handleToggleClick,
      handleEditClick,
      handleDeleteClick,
      zoneTypeToToggle,
      zoneTypeToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <ZoneTypeTableLoading />;
   }

   if (!data?.data?.data?.length) {
      return <ZoneTypeTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.data && data.data.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.data.totalPages}
               onPageChange={onPageChange}
               hasNext={data.data.hasNext}
               hasPrev={data.data.hasPrev}
            />
         )}

         {/* Toggle Confirmation Modal */}
         <ZoneTypeToggleModal
            isOpen={!!zoneTypeToToggle}
            onClose={() => setZoneTypeToToggle(null)}
            onConfirm={handleToggleConfirm}
            isLoading={activateZoneTypeMutation.isPending || deactivateZoneTypeMutation.isPending}
            zoneTypeName={zoneTypeToToggle?.name || ''}
            currentStatus={zoneTypeToToggle?.isActive || false}
         />

         {/* Delete Confirmation Modal */}
         <ZoneTypeDeleteModal
            isOpen={!!zoneTypeToDelete}
            onClose={() => setZoneTypeToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteZoneTypeMutation.isPending}
            zoneTypeName={zoneTypeToDelete?.name || ''}
         />

         {/* Edit Modal */}
         <ZoneTypeModal
            mode='edit'
            zoneTypeId={zoneTypeToEdit}
            isOpen={!!zoneTypeToEdit}
            onClose={() => setZoneTypeToEdit(null)}
         />
      </div>
   );
}

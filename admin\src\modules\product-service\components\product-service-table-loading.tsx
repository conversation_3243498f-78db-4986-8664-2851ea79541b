'use client';

import { Skeleton } from '@/components/ui/skeleton';

export function ProductServiceTableLoading() {
  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
                </th>
                <th className='h-11 px-4 text-center align-middle'>
                  <div className='text-center font-semibold text-gray-600 text-sm'>Icon</div>
                </th>
                <th className='h-11 px-4 text-center align-middle'>
                  <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className='border-b'>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-24' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-32' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-48' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center'>
                      <Skeleton className='h-8 w-8 rounded' />
                    </div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center gap-1'>
                      <Skeleton className='h-8 w-12' />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
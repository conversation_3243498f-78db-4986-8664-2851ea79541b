import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { LocationPoint } from '@shared/shared/event-emitter';
import {
  PickupType,
  Ride,
  RiderMeta,
} from '@shared/shared/repositories/models/ride.model';
import {
  RideLifecycleRepository,
  RideRepository,
} from '@shared/shared/repositories';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { RabbitMQEventPublisher } from '@shared/shared/event-emitter/publishers/rabbitmq-event.publisher';
import { DriverVehicleRepository } from '@shared/shared/repositories/driver-vehicle.repository';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';
import { RideLifecycle } from '@shared/shared/repositories/models/rideLifecycle.model';
import {
  RideOfferStatus,
  RideStatus,
} from '@shared/shared/modules/ride-matching/constants';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { UserMetaDataService } from '../user-meta-data/user-meta-data.service';
import { RideOfferRepository } from '@shared/shared/repositories/ride-offer.repository';

import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { BookFor } from '@shared/shared/repositories/models/ride.model';
import { FareEngineService } from '@shared/shared/modules/fare-engine/fare-engine.service';
import { RideMeterService } from '@shared/shared/modules/ride-meter/ride-meter.service';
import { RideFareService } from '@shared/shared/modules/ride-fare/ride-fare.service';
import {
  FareContext,
  CityProductWithMetrics,
} from '@shared/shared/modules/fare-engine/interfaces/interface';
import { CityProductRepository } from '@shared/shared/repositories/city-product.repository';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { FareCalculationOptions } from '@shared/shared/modules/fare-engine/fare-calculator/interfaces/fare-calculation.interface';
import {
  RideMeterType,
  RideMeterUnit,
} from '@shared/shared/repositories/models/rideMeter.model';
import { PaymentService } from '../payment/payment.service';
import { DriverAccountService } from '../driver-account/driver-account.service';
import { DriverEarningsService } from '../driver-earnings/driver-earnings.service';
import { PaymentType } from '@shared/shared/repositories/models/payment.model';
import { Product } from '@shared/shared/repositories/models/product.model';
import { RideUtilsService } from './services/ride-utlis.service';

export enum ChargeMeter {
  PICKUP_DISTANCE = 'pickup_distance',
  PICKUP_DURATION = 'pickup_duration',
  PICKUP_WAIT_DURATION = 'pickup_wait_duration',
  TRIP_DURATION = 'trip_duration',
  TRIP_WAIT_DURATION = 'trip_wait_duration',
  TRIP_DISTANCE = 'trip_distance',
}

export interface BaseRideData {
  productId?: string;
  cityProductId: string;
  pickup: LocationPoint;
  destination: LocationPoint;
  stops?: LocationPoint[];
  scheduledFor?: Date;
  riderMeta?: RiderMeta;
  bookFor?: BookFor;
  pickupType?: PickupType;
  pickupTime?: string;
  createdBy?: string;
}

export interface CreateRideData extends BaseRideData {
  riderId: string; // required for normal rides
}

export interface AdminCreateRideData extends BaseRideData {
  riderId?: string; // optional for admin-created rides
}

export interface RideListFilters {
  excludeStatuses?: RideStatus[];
  status?: RideStatus;
  page?: number;
  limit?: number;
}
// function istToUtc(istString: string): string {
//   const date = new Date(istString); // Parses as local or UTC depending on format

//   // Subtract 5h 30m in ms
//   const utcTime = new Date(date.getTime() - (5 * 60 + 30) * 60 * 1000);

//   return utcTime.toISOString(); // UTC in ISO format (ends with Z)
// }
@Injectable()
export class RideService {
  private readonly logger = new Logger(RideService.name);

  constructor(
    private readonly rideRepository: RideRepository,
    private readonly rideLifecycleRepository: RideLifecycleRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly rabbitmqEventPublisher: RabbitMQEventPublisher,
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly fileUploadService: FileUploadService,
    private readonly userMetaDataService: UserMetaDataService,
    private readonly rideOfferRepository: RideOfferRepository,

    private readonly h3UtilityService: H3UtilityService,
    private readonly fareEngineService: FareEngineService,
    private readonly rideMeterService: RideMeterService,
    private readonly rideFareService: RideFareService,
    private readonly cityProductRepository: CityProductRepository,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly paymentService: PaymentService,
    private readonly driverAccountService: DriverAccountService,
    private readonly driverEarningsService: DriverEarningsService,
    private readonly rideUtilsService: RideUtilsService,
  ) {}

  /**
   * Create a new ride - OPTIMIZED for high concurrency
   */

  async requestRide(data: CreateRideData): Promise<Ride> {
    const startTime = Date.now();
    const rideMode = data.pickupType || PickupType.NOW;
    const pickupTime = this.rideUtilsService.parsePickupTime(data.pickupTime);

    this.logger.log(`Requesting ride for rider ${data.riderId}`);

    // Validate ride request
    await this.rideUtilsService.validateRideRequest(data);

    try {
      // Resolve rider ID (handles booking for others)
      const riderId = await this.rideUtilsService.resolveRiderId(data, false);

      // Check for active rides
      await this.rideUtilsService.validateActiveRideExists(riderId);

      // Validate and fetch required data in parallel
      const [riderProfile, cityProduct] = await Promise.all([
        this.rideUtilsService.validateAndFetchRiderProfile(riderId),
        this.rideUtilsService.validateAndFetchCityProduct(data.cityProductId),
      ]);

      // Validate product
      const product = this.rideUtilsService.validateProduct(
        cityProduct,
        data.productId,
      );

      // Ensure rider has OTP
      const verificationCode =
        await this.rideUtilsService.ensureRiderOtp(riderProfile);

      // Calculate distance and duration
      const { duration, distance } =
        await this.rideUtilsService.calculateDistanceAndDuration(
          data.pickup,
          data.destination,
          data.stops,
        );

      // Calculate fare
      let fareSpec = null;
      try {
        fareSpec = await this.calculateFareForRideRequest(
          cityProduct,
          { duration, distance },
          data.pickup,
          data.destination,
          data.stops,
        );
        this.logger.log(
          `✓ Fare calculated for ride request: ${fareSpec.passengerFare} ${fareSpec.currency}`,
        );
      } catch (fareError) {
        this.logger.warn(
          `Failed to calculate fare for ride request: ${fareError instanceof Error ? fareError.message : 'Unknown error'}`,
        );
        // Continue with null fareSpec - will be calculated later
      }

      // Create ride data object
      const rideData = this.rideUtilsService.createRideDataObject(
        data,
        riderId,
        product,
        rideMode,
        duration,
        distance,
        fareSpec,
        verificationCode,
        pickupTime,
      );

      // Create lifecycle data object
      const lifecycleData = this.rideUtilsService.createLifecycleDataObject(
        rideMode,
        pickupTime,
        startTime,
        fareSpec !== null,
      );

      // Create ride with lifecycle and fare data in a single transaction
      const ride = await this.rideRepository.createRideWithLifecycle(
        rideData,
        lifecycleData,
      );

      // Create ride meters and fare records asynchronously (don't block ride creation)
      this.createRideMetersAndFareAsync(
        ride,
        cityProduct,
        { duration, distance },
        fareSpec,
      );

      // Publish ride requested event asynchronously
      this.publishRideRequestedEventAsync(ride, data, rideMode, product);

      // Log success and return
      const processingTime = Date.now() - startTime;
      this.rideUtilsService.logRideCreationSuccess(
        ride.id,
        processingTime,
        rideMode,
      );

      return ride;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.rideUtilsService.logRideCreationError(
        data.riderId,
        processingTime,
        error,
      );
      throw error;
    }
  }
  async rideRequestAdmin(data: AdminCreateRideData): Promise<Ride> {
    const startTime = Date.now();
    const rideMode = data.pickupType || PickupType.NOW;
    const pickupTime = this.rideUtilsService.parsePickupTime(data.pickupTime);

    this.logger.log(
      `Admin requesting ride for rider ${data.riderId || 'new rider'}`,
    );

    // Validate admin ride request specific requirements
    this.rideUtilsService.validateAdminRideRequest(data);

    // Validate ride request
    await this.rideUtilsService.validateRideRequest(data);
    try {
      // Resolve rider ID (handles admin booking for others)
      const riderId = await this.rideUtilsService.resolveRiderId(data, true);

      // Validate and fetch required data in parallel
      const [riderProfile, cityProduct] = await Promise.all([
        this.rideUtilsService.validateAndFetchRiderProfile(riderId),
        this.rideUtilsService.validateAndFetchCityProduct(data.cityProductId),
      ]);

      // Validate product
      const product = this.rideUtilsService.validateProduct(
        cityProduct,
        data.productId,
      );

      // Check for active rides
      await this.rideUtilsService.validateActiveRideExists(riderId);

      // Ensure rider has OTP
      const verificationCode =
        await this.rideUtilsService.ensureRiderOtp(riderProfile);

      // Calculate distance and duration
      const { duration, distance } =
        await this.rideUtilsService.calculateDistanceAndDuration(
          data.pickup,
          data.destination,
          data.stops,
        );

      // Calculate fare
      let fareSpec = null;
      try {
        fareSpec = await this.calculateFareForRideRequest(
          cityProduct,
          { duration, distance },
          data.pickup,
          data.destination,
          data.stops,
        );
        this.logger.log(
          `✓ Fare calculated for ride request: ${fareSpec.passengerFare} ${fareSpec.currency}`,
        );
      } catch (fareError) {
        this.logger.warn(
          `Failed to calculate fare for ride request: ${fareError instanceof Error ? fareError.message : 'Unknown error'}`,
        );
        // Continue with null fareSpec - will be calculated later
      }

      // Create ride data object
      const rideData = this.rideUtilsService.createRideDataObject(
        data,
        riderId,
        product,
        rideMode,
        duration,
        distance,
        fareSpec,
        verificationCode,
        pickupTime,
      );

      // Create lifecycle data object
      const lifecycleData = this.rideUtilsService.createLifecycleDataObject(
        rideMode,
        pickupTime,
        startTime,
        fareSpec !== null,
      );

      // Create ride with lifecycle and fare data in a single transaction
      const ride = await this.rideRepository.createRideWithLifecycle(
        rideData,
        lifecycleData,
      );

      // Create ride meters and fare records asynchronously (don't block ride creation)
      this.createRideMetersAndFareAsync(
        ride,
        cityProduct,
        { duration, distance },
        fareSpec,
      );

      // Publish ride requested event asynchronously
      // Note: We need to cast data to CreateRideData for the event publisher
      const eventData = { ...data, riderId } as CreateRideData;
      this.publishRideRequestedEventAsync(ride, eventData, rideMode, product);

      // Log success and return
      const processingTime = Date.now() - startTime;
      this.rideUtilsService.logRideCreationSuccess(
        ride.id,
        processingTime,
        rideMode,
      );

      return ride;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.rideUtilsService.logRideCreationError(
        data.riderId || 'unknown',
        processingTime,
        error,
      );
      throw error;
    }
  }

  async publishRideRequestedEventAsync(
    ride: Ride,
    data: CreateRideData,
    rideMode: PickupType,
    product: Product,
  ): Promise<void> {
    console.log(
      `RIDE_SERVICE: Publishing ride requested event for ride ${ride.id}, mode: ${rideMode}`,
    );
    setTimeout(async () => {
      try {
        const eventData: any = {
          rideId: ride.id,
          riderId: data.riderId,
          productId: ride.productId,
          cityProductId: ride.cityProductId,
          pickupLocation: data.pickup,
          destinationLocation: data.destination,
          stops: data.stops || [],
          distance: ride.distance,
          duration: ride.duration,
          fareSpec: ride.fareSpec,
          requestedAt: new Date().toISOString(),
          mode: rideMode as 'now' | 'later',
          ...(data.scheduledFor && {
            scheduledFor: data.scheduledFor.toISOString(),
          }),
          passengerLimit: product.passengerLimit,
        };

        // Only publish immediately for 'now' rides
        if (rideMode === PickupType.NOW) {
          this.logger.log(
            ` RIDE_SERVICE: About to publish IMMEDIATE ride ${ride.id} to RabbitMQ`,
          );
          await this.rabbitmqEventPublisher.publishRideRequested(eventData);
          this.logger.log(
            ` RIDE_SERVICE: Successfully published ride requested event for immediate ride ${ride.id}`,
          );
        } else {
          this.logger.log(
            `RIDE_SERVICE: Scheduled ride ${ride.id} created, will be processed at ${data.scheduledFor} - NOT PUBLISHING NOW`,
          );
        }
      } catch (error) {
        this.logger.error(
          `RIDE_SERVICE: Failed to publish ride requested event for ride ${ride.id}:`,
          error,
        );
        // Consider adding to a retry queue here
      }
    }, 0);
  }

  /**
   * Get rides for a rider
   */
  async getRiderRides(
    riderId: string,
    filters?: RideListFilters,
  ): Promise<Ride[]> {
    this.logger.log(`Getting rides for rider ${riderId}`);

    // Validate rider exists
    const rider = await this.userProfileRepository.findById(riderId);
    if (!rider) {
      throw new NotFoundException(`Rider with ID ${riderId} not found`);
    }

    const excludeStatuses = filters?.excludeStatuses || [
      RideStatus.TRIP_COMPLETED,
      RideStatus.CANCELLED,
      // RideStatus.UNASSIGNED,
      RideStatus.SCHEDULED,
    ];

    return this.rideRepository.findRidesByRiderId(riderId, excludeStatuses);
  }

  /**
   * Get ride details by ID
   */
  async getRideById(rideId: string): Promise<Ride> {
    this.logger.log(`Getting ride details for ID ${rideId}`);

    const ride = await this.rideRepository.findRideByIdWithDetailsRider(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }
    if (ride.driver?.profilePictureUrl) {
      ride['driver']['profilePictureUrl'] =
        await this.fileUploadService.getSignedUrl(
          ride.driver?.profilePictureUrl,
          3600, // 1 hour expiry
        );
    }
    if (ride.product?.icon) {
      ride['product']['icon'] = await this.fileUploadService.getSignedUrl(
        ride.product?.icon,
        3600, // 1 hour expiry
      );
    }
    return ride;
  }

  /**
   * Accept ride by driver
   */
  async acceptRide(
    rideId: string,
    driverId: string,
    driverVehicleId?: string,
    driverLocation?: { lat: number; lng: number },
  ): Promise<Ride> {
    this.logger.log(`Driver ${driverId} accepting ride ${rideId}`);

    // Step 1: Validate ride and driver eligibility
    const { ride } = await this.validateRideAcceptanceEligibility(
      rideId,
      driverId,
    );
    // console.log({driver})
    // Step 2: Resolve driver vehicle
    const resolvedDriverVehicleId = await this.resolveDriverVehicle(
      driverId,
      driverVehicleId,
    );

    // Step 3: Calculate driver to pickup metrics
    const driverToPickupSpec = await this.calculateDriverToPickupMetrics(
      ride,
      driverLocation,
      rideId,
    );

    // Step 4: Update ride and create lifecycle
    const updatedRide = await this.updateRideWithAcceptance(
      rideId,
      driverId,
      resolvedDriverVehicleId,
      driverToPickupSpec,
    );

    // Step 5: Publish ride matched event
    await this.publishRideMatchedEvent(ride, driverId, rideId);

    // Step 6: Return ride with enhanced details
    return await this.getRideWithEnhancedDetails(rideId, updatedRide);
  }

  /**
   * Validate that the ride and driver are eligible for acceptance
   */
  private async validateRideAcceptanceEligibility(
    rideId: string,
    driverId: string,
  ): Promise<{ ride: Ride }> {
    // Validate ride exists and is available
    const ride = await this.validateRideAvailability(rideId);

    // Validate driver exists and is active
    await this.validateDriverEligibility(driverId);

    return { ride };
  }

  /**
   * Validate ride exists and is available for acceptance
   */
  private async validateRideAvailability(rideId: string): Promise<Ride> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Check if ride already has a driver
    if (ride.driverId) {
      throw new BadRequestException(
        `Ride has already been accepted by another driver`,
      );
    }

    // Check if ride is in correct status to be accepted
    if (ride.status !== RideStatus.REQUESTED) {
      throw new BadRequestException(
        `Cannot accept ride with status ${ride.status}. Only requested rides can be accepted.`,
      );
    }

    // Check for existing accepted ride offers
    const rideOffer = await this.rideOfferRepository.findRideOfferByStatus(
      ride.id,
      RideOfferStatus.ACCEPTED,
    );
    if (rideOffer) {
      throw new BadRequestException(`Ride already accepted by another driver`);
    }

    return ride;
  }

  /**
   * Validate driver exists and is eligible to accept rides
   */
  private async validateDriverEligibility(driverId: string): Promise<any> {
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException(`Driver with ID ${driverId} not found`);
    }

    // Ensure driver profile is active
    if (driver.status !== UserProfileStatus.ACTIVE) {
      throw new BadRequestException(
        'Your account is not active. Please complete onboarding steps to activate your account before accepting rides.',
      );
    }

    return driver;
  }

  /**
   * Resolve driver vehicle ID (provided or primary vehicle)
   */
  private async resolveDriverVehicle(
    driverId: string,
    driverVehicleId?: string,
  ): Promise<string> {
    let resolvedDriverVehicleId = driverVehicleId;

    if (!resolvedDriverVehicleId) {
      const primaryVehicle = await this.driverVehicleRepository.findOne({
        where: { userProfileId: driverId, isPrimary: true },
      });
      resolvedDriverVehicleId = primaryVehicle?.id;
    }

    if (!resolvedDriverVehicleId) {
      throw new BadRequestException(
        'Driver vehicle not found. Please set a primary vehicle.',
      );
    }

    return resolvedDriverVehicleId;
  }

  /**
   * Calculate driver to pickup metrics and update ride meters
   */
  private async calculateDriverToPickupMetrics(
    ride: Ride,
    driverLocation?: { lat: number; lng: number },
    rideId?: string,
  ): Promise<any> {
    if (!driverLocation || !ride.pickupLocation) {
      return null;
    }

    try {
      // Format locations for calculation
      const formattedDriverLocation: LocationPoint = {
        lat: driverLocation.lat,
        lng: driverLocation.lng,
      };

      const formattedPickupLocation: LocationPoint = {
        lat: ride.pickupLocation.lat,
        lng: ride.pickupLocation.lng,
      };

      // Calculate distance and duration
      const { duration, distance } =
        await this.rideUtilsService.calculateDistanceAndDuration(
          formattedDriverLocation,
          formattedPickupLocation,
        );

      console.log({
        message: 'driver to pickup distance and duration',
        duration,
        distance,
      });

      // Update ride meters
      this.updateDriverToPickupMeters(ride.id, distance, duration);

      this.logger.log(
        `Calculated driver to pickup: distance=${distance ? distance / 1000 : 0}km, duration=${duration}s for ride ${rideId}`,
      );

      return {
        duration: duration, // in seconds
        distance: distance, // in meters
      };
    } catch (error) {
      this.logger.error(
        `Failed to calculate driver to pickup distance for ride ${rideId}:`,
        error,
      );
      // Continue with ride acceptance even if distance calculation fails
      return null;
    }
  }

  /**
   * Update ride meters for driver to pickup distance and duration
   */
  private async updateDriverToPickupMeters(
    rideId: string,
    distance: number | null,
    duration: number | null,
  ): Promise<void> {
    const distanceInKm = distance ? distance / 1000 : 0;

    await Promise.all([
      this.rideMeterService.updateRideMeterByNameAndRideId(
        rideId,
        RideMeterType.PICKUP_DISTANCE,
        {
          value: distanceInKm,
          unit: RideMeterUnit.KILOMETERS,
        },
      ),
      this.rideMeterService.updateRideMeterByNameAndRideId(
        rideId,
        RideMeterType.PICKUP_DURATION,
        {
          value: duration ?? 0,
          unit: RideMeterUnit.SECONDS,
        },
      ),
    ]);
  }

  /**
   * Update ride with acceptance details and create lifecycle entry
   */
  private async updateRideWithAcceptance(
    rideId: string,
    driverId: string,
    driverVehicleId: string,
    driverToPickupSpec: any,
  ): Promise<Ride> {
    // Update ride with driver, vehicle, status, and pickup metrics
    const updatedRide = await this.rideRepository.updateById(rideId, {
      driverId,
      driverVehicleId,
      status: RideStatus.ACCEPTED,
      driverToPickupSpec,
    });

    // Create lifecycle entry for acceptance
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.ACCEPTED,
      driverId,
    });

    return updatedRide;
  }

  /**
   * Publish ride matched event to RabbitMQ
   */
  private async publishRideMatchedEvent(
    ride: Ride,
    driverId: string,
    rideId: string,
  ): Promise<void> {
    const matchedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      riderOtp: ride.verificationCode ?? null,
      offerId: `offer_${rideId}_${driverId}_${Date.now()}`,
      acceptedAt: new Date().toISOString(),
      phoneNumber: ride.riderMeta?.phoneNumber || null,
      bookFor: ride.bookFor ?? BookFor.ME,
      createdBy: ride.createdBy ?? null,
    };

    try {
      await this.rabbitmqEventPublisher.publishRideMatched(matchedEventData);
      this.logger.log(`Published ride matched event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride matched event for ride ${rideId}:`,
        error,
      );
      // Don't fail the acceptance if event publishing fails
    }
  }

  /**
   * Get ride with enhanced details including S3 URLs
   */
  private async getRideWithEnhancedDetails(
    rideId: string,
    fallbackRide: Ride,
  ): Promise<Ride> {
    // Fetch the updated ride with rider details
    const rideWithRiderDetails =
      await this.rideRepository.findRideByIdWithRiderDetails(rideId);

    if (!rideWithRiderDetails) {
      this.logger.error(
        `Failed to fetch ride with rider details for ride ${rideId}`,
      );
      return fallbackRide; // Fallback to basic ride data
    }

    // Convert rider profile picture URL to full S3 URL if needed
    if (rideWithRiderDetails.rider?.profilePictureUrl) {
      rideWithRiderDetails.rider.profilePictureUrl =
        await this.fileUploadService.getSignedUrl(
          rideWithRiderDetails.rider.profilePictureUrl,
          3600, // 1 hour expiry
        );
    }

    this.logger.log(`Ride ${rideId} accepted successfully`);
    return rideWithRiderDetails;
  }

  /**
   * Start ride by driver with OTP verification
   */
  async startRide(
    rideId: string,
    driverId: string,
    otp: string,
  ): Promise<Ride> {
    this.logger.log(
      `Driver ${driverId} starting ride ${rideId} with OTP ${otp}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findRideByIdWithDetailsRider(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Check if ride is assigned to the particular driver
    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        `Ride ${rideId} is not assigned to driver ${driverId}`,
      );
    }

    // Check if ride is in correct status to be started
    if (ride.status !== RideStatus.ACCEPTED) {
      throw new BadRequestException(
        `Cannot start ride with status ${ride.status}. Only accepted rides can be started.`,
      );
    }

    // Verify OTP with rider profile rideOtp
    if (!ride.rider?.rideOtp) {
      throw new BadRequestException(
        'No OTP found for this ride. Please contact support.',
      );
    }

    if (ride.rider.rideOtp !== otp) {
      throw new BadRequestException(
        'Invalid OTP. Please verify the OTP with the rider.',
      );
    }

    // Update ride status to PROCESSING and set otpVerifiedAt
    const updatedRide = await this.rideRepository.updateRideStatus(
      rideId,
      RideStatus.TRIP_STARTED,
      {
        otpVerifiedAt: new Date(),
      },
    );

    // Create lifecycle entry for ride started
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId: ride.id,
      status: RideStatus.TRIP_STARTED,
      location: ride.pickupLocation,
      driverId,
      meta: {
        notes: 'Ride started by driver with OTP verification',
        verifiedAt: new Date().toISOString(),
      },
    });

    // Prepare ride started event data for RabbitMQ
    const startedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      driver: ride.driver,
      startedAt: new Date().toISOString(),
      pickupLocation: {
        latitude: ride.pickupLocation?.lat || 0,
        longitude: ride.pickupLocation?.lng || 0,
      },
    };

    // Publish ride started event to RabbitMQ
    try {
      await this.rabbitmqEventPublisher.publishRideStarted(startedEventData);
      this.logger.log(`Published ride started event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride started event for ride ${rideId}:`,
        error,
      );
      // Don't fail the ride start if event publishing fails
    }

    this.logger.log(
      `Ride ${rideId} started successfully by driver ${driverId}`,
    );
    return updatedRide;
  }

  /**
   * Cancel ride by rider
   * Status changes to CANCELLED
   */
  async cancelRideByRider(
    rideId: string,
    riderId: string,
    reason?: string,
  ): Promise<Ride> {
    this.logger.log(`Rider ${riderId} cancelling ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Validate rider owns this ride
    if (ride.riderId !== riderId) {
      throw new BadRequestException('You can only cancel your own rides');
    }

    if (
      ride.status === RideStatus.TRIP_COMPLETED ||
      ride.status === RideStatus.CANCELLED ||
      ride.status === RideStatus.TRIP_STARTED
    ) {
      throw new BadRequestException(
        `Cannot cancel ride with status ${ride.status}`,
      );
    }
    const driverId = ride.driverId || null;
    // console.log(driverId)
    // Update ride status to CANCELLED
    const durationInseconds = ride.otpVerifiedAt
      ? Math.floor((new Date().getTime() - ride.otpVerifiedAt.getTime()) / 1000)
      : 0;
    // console.log({ durationInseconds });
    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.CANCELLED,
      actualDuration: durationInseconds,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.CANCELLED,
      cancelledBy: riderId,
      driverId,
      meta: {
        cancellationReason: reason || 'Cancelled by rider',
        cancelledBy: riderId,
        cancelledByType: 'rider',
        notes: `Ride cancelled by rider: ${reason || 'No reason provided'}`,
      },
    });

    // Publish ride cancelled event
    try {
      const cancelledEventData: any = {
        rideId,
        riderId,
        driverId,
        cancelledBy: 'rider',
        reason: reason || 'Cancelled by rider',
        cancelledAt: new Date().toISOString(),
      };

      await this.rabbitmqEventPublisher.publishRideCancelled(
        cancelledEventData,
      );
      this.logger.log(`Published ride cancelled event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride cancelled event for ride ${rideId}:`,
        error,
      );
      // Don't fail the cancellation if event publishing fails
    }

    this.logger.log(`Ride ${rideId} cancelled by rider ${riderId}`);
    return updatedRide;
  }

  async cancelRideByAdmin(
    rideId: string,
    adminId: string,
    reason?: string,
  ): Promise<Ride> {
    this.logger.log(`Admin ${adminId} cancelling ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    if (
      ride.status === RideStatus.TRIP_COMPLETED ||
      ride.status === RideStatus.CANCELLED ||
      ride.status === RideStatus.TRIP_STARTED
    ) {
      throw new BadRequestException(
        `Cannot cancel ride with status ${ride.status}`,
      );
    }
    const driverId = ride.driverId || null;
    // console.log(driverId)
    // Update ride status to CANCELLED
    const durationInseconds = ride.otpVerifiedAt
      ? Math.floor((new Date().getTime() - ride.otpVerifiedAt.getTime()) / 1000)
      : 0;
    // console.log({ durationInseconds });
    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.CANCELLED,
      actualDuration: durationInseconds,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.CANCELLED,
      driverId,
      cancelledBy: adminId,
      meta: {
        cancellationReason: reason || 'Cancelled by rider',
        cancelledBy: adminId,
        cancelledByType: 'admin',
        notes: `Ride cancelled by admin: ${reason || 'No reason provided'}`,
      },
    });

    // Publish ride cancelled event
    try {
      const cancelledEventData: any = {
        rideId,
        riderId: ride.riderId,
        driverId,
        cancelledBy: 'rider',
        reason: reason || 'Cancelled by rider',
        cancelledAt: new Date().toISOString(),
      };

      await this.rabbitmqEventPublisher.publishRideCancelled(
        cancelledEventData,
      );
      this.logger.log(`Published ride cancelled event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride cancelled event for ride ${rideId}:`,
        error,
      );
      // Don't fail the cancellation if event publishing fails
    }

    this.logger.log(`Ride ${rideId} cancelled by admin ${adminId}`);
    return updatedRide;
  }

  /**
   * Cancel ride by driver
   * Status changes to PROCESSING (to allow reassignment)
   */
  async cancelRideByDriver(
    rideId: string,
    driverId: string,
    reason?: string,
  ): Promise<Ride> {
    this.logger.log(`Driver ${driverId} cancelling ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        'You can only cancel rides assigned to you',
      );
    }

    if (
      ride.status === RideStatus.TRIP_COMPLETED ||
      ride.status === RideStatus.CANCELLED ||
      ride.status === RideStatus.TRIP_STARTED
    ) {
      throw new BadRequestException(
        `Cannot cancel ride with status ${ride.status}`,
      );
    }

    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.REQUESTED,
      driverId: null,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.CANCELLED,
      cancelledBy: driverId,
      driverId,
      meta: {
        cancellationReason: reason || 'Cancelled by driver',
        cancelledBy: driverId,
        cancelledByType: 'driver',
        notes: `Ride cancelled by driver: ${reason || 'No reason provided'}`,
      },
    });

    try {
      const cancelledEventData: any = {
        rideId,
        riderId: ride.riderId,
        driverId,
        cancelledBy: 'driver',
        reason: reason || 'Cancelled by driver',
        cancelledAt: new Date().toISOString(),
      };

      await this.rabbitmqEventPublisher.publishRideCancelled(
        cancelledEventData,
      );
      this.logger.log(`Published ride cancelled event for ride ${rideId}`);
      this.logger.log(
        `Published ride rematch event for ride ${rideId} after driver cancellation`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish ride events for ride ${rideId}:`,
        error,
      );
      // Don't fail the cancellation if event publishing fails
    }

    this.logger.log(
      `Ride ${rideId} cancelled by driver ${driverId}, status changed to PROCESSING for rematching`,
    );
    return updatedRide;
  }

  /**
   * Get rides for driver with status filtering
   */
  async getRidesForDriver(
    driverId: string,
    filters?: RideListFilters,
  ): Promise<Ride[]> {
    this.logger.log(
      `Getting rides for driver ${driverId} with status filter: ${filters}`,
    );

    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException(`Driver with ID ${driverId} not found`);
    }

    const excludeStatuses = filters?.excludeStatuses || [
      RideStatus.TRIP_COMPLETED,
      RideStatus.CANCELLED,
    ];

    return this.rideRepository.findRidesByDriverId(driverId, excludeStatuses);
  }

  /**
   * Get ride lifecycle history
   */
  async getRideLifecycle(rideId: string): Promise<RideLifecycle[]> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideLifecycleRepository.findLifecyclesByRideId(rideId);
  }

  /**
   * End ride by driver
   * - Optionally replace destination with newDestination
   * - If replaced, push previous destination as a stop
   * - Recalculate fare if destination changed
   * - Create payment record
   * - Calculate and update driver earnings
   * - Change ride status to TRIP_COMPLETED, set completedAt, and add lifecycle entry
   */
  async endRide(
    rideId: string,
    driverId: string,
    newDestination?: LocationPoint,
  ): Promise<Ride> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    if (ride.driverId !== driverId) {
      throw new BadRequestException('You can only end rides assigned to you');
    }

    if (ride.status !== RideStatus.TRIP_STARTED) {
      throw new BadRequestException(
        `Cannot end ride with status ${ride.status}. Only started rides can be ended.`,
      );
    }

    if (newDestination) {
      ride.destinationLocation = newDestination;
      await this.updateRideMetersForNewDestination(ride, newDestination);
    }

    const lifecycle =
      await this.rideLifecycleRepository.findLifecyclesByRideId(rideId);

    if (lifecycle && lifecycle.length > 0) {
      // filter only destination reached items
      const filtered = lifecycle.filter(
        (item) => item.status === RideStatus.DESTINATION_REACHED,
      );

      const sorted = filtered.sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
      );

      // get all locations as array ordered by createdAt
      const actualStops = sorted.map((item) => item.location);

      // push all stops to ride stops
      if (actualStops.length > 0) {
        ride.stops = actualStops as LocationPoint[];
      }
    }

    await this.rideRepository.updateById(rideId, ride);

    const actualTripDuration = ride.otpVerifiedAt
      ? Math.floor((new Date().getTime() - ride.otpVerifiedAt.getTime()) / 1000)
      : ride.duration || 0;

    await this.updateActualTripDuration(rideId, actualTripDuration);

    this.logger.log(
      `Calculating final fare for ride ${rideId} using actual RideMeter data`,
    );
    const finalFareSpec = await this.calculateFinalFareFromRideMeters(
      ride,
      ride.destinationLocation!,
    );

    await this.updateRideFareRecord(rideId, finalFareSpec);

    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.TRIP_COMPLETED,
      actualDuration: actualTripDuration,
      completedAt: new Date(),
      fareSpec: finalFareSpec,
    });

    await this.handleRideCompletion(updatedRide, finalFareSpec);

    // Lifecycle entry for completion
    if (newDestination) {
      await this.rideLifecycleRepository.createRideLifecycle({
        rideId,
        status: RideStatus.TRIP_COMPLETED,
        location: newDestination,
        driverId,
        meta: {
          notes: newDestination
            ? 'Ride completed with updated destination'
            : 'Ride completed',
          completedAt: new Date().toISOString(),
        },
      });
    }

    if (ride.driverId) {
      this.userMetaDataService
        .handleRideCompletion(ride.riderId, ride.driverId)
        .catch((error) => {
          this.logger.error(
            `Failed to update metadata after ride completion: ${error.message}`,
          );
        });
    }

    const driver = await this.userProfileRepository.findById(driverId);
    const completedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      driver: driver,
      completedAt: new Date().toISOString(),
      actualFare: updatedRide.fareSpec?.passengerFare ?? 0,
      distance: ride.distance ?? 0,
      duration: ride.duration ?? 0,
      actualDuration: actualTripDuration > 0 ? actualTripDuration / 60 : 0, // in minutes
      createdBy: ride.createdBy ?? null,
      bookFor: ride.bookFor ?? BookFor.ME,
      phoneNumber: ride.riderMeta?.phoneNumber || null,
      finalFareSpec: updatedRide.fareSpec,
    };
    try {
      await this.rabbitmqEventPublisher.publishRideCompleted(
        completedEventData,
      );
      this.logger.log(`Published ride completed event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride completed event for ride ${rideId}:`,
        error,
      );
      // Don't fail the ride completion if event publishing fails
    }

    this.logger.log(`Ride ${rideId} completed successfully`);
    return updatedRide;
  }

  /**
   * Mark destination as reached when driver arrives at a stop
   * @param rideId - The ride ID
   * @param driverId - The driver ID
   * @param location - Current location coordinates
   * @returns Updated ride
   */
  async markDestinationReached(
    rideId: string,
    driverId: string,
    location: { lat: number; lng: number; address?: string },
  ): Promise<Ride> {
    this.logger.log(
      `Marking destination reached for ride ${rideId} at location (${location.lat}, ${location.lng})`,
    );

    const ride = await this.rideRepository.findById(rideId);

    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Verify the driver
    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        'You are not authorized to update this ride',
      );
    }

    // Verify ride status
    if (ride.status !== RideStatus.TRIP_STARTED) {
      throw new BadRequestException(
        `Cannot mark destination reached. Ride status is ${ride.status}`,
      );
    }

    if (!ride.stops || !Array.isArray(ride.stops) || ride.stops.length === 0) {
      throw new BadRequestException('This ride has no stops defined');
    }

    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.DESTINATION_REACHED,
      location,
      driverId,
      meta: {
        notes: 'Driver reached destination stop',
        stopAddress: location.address,
        reachedAt: new Date().toISOString(),
      },
    });

    const destinationReachedData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      stopAddress: location.address || 'Unknown Location',
      reachedAt: new Date().toISOString(),
      location,
    };

    try {
      await this.rabbitmqEventPublisher.publishDestinationReached(
        destinationReachedData,
      );
      this.logger.log(`Published destination reached event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish destination reached event for ride ${rideId}:`,
        error,
      );
    }

    this.logger.log(
      `Successfully marked destination reached for ride ${rideId}`,
    );

    return ride;
  }

  /**
   * Calculate fare for ride request (optimized for speed)
   */
  private async calculateFareForRideRequest(
    cityProduct: any,
    metrics: { duration: number | null; distance: number | null },
    pickup: LocationPoint,
    destination: LocationPoint,
    stops?: LocationPoint[],
  ): Promise<any> {
    this.logger.log(
      `Calculating fare for ride request with cityProduct ${cityProduct.id}`,
    );

    try {
      // Get zones for pickup and destination
      const { pickupZone, destinationZone } =
        await this.getZonesFromCoordinates(pickup, destination);
      // Get zones for stops if provided
      let stopsZones: any[] | null = null;
      if (stops && stops.length > 0) {
        stopsZones = await Promise.all(
          stops.map((stop) => this.getZoneFromLocation(stop)),
        );
      }

      // Calculate fare using fare engine (optimized)
      const fareResult = await this.calculateFareWithEngine(
        null, // No ride object needed for request calculation
        cityProduct,
        pickupZone,
        destinationZone,
        metrics,
        stopsZones,
      );

      if (fareResult) {
        return {
          passengerFare: Number(fareResult.passengerFare.toFixed(2)),
          driverEarnings: Number(fareResult.driverEarnings.toFixed(2)),
          platformRevenue: fareResult.platformRevenue,
          currency: fareResult.currency,
          ...fareResult,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to calculate fare for ride request:`, error);
      throw error;
    }
  }

  /**
   * Create ride meters and fare records asynchronously
   */
  private createRideMetersAndFareAsync(
    ride: Ride,
    cityProduct: any,
    metrics: { duration: number | null; distance: number | null },
    fareSpec: any,
  ): void {
    // Run asynchronously without blocking
    setTimeout(async () => {
      try {
        this.logger.log(
          `Creating ride meters and fare records for ride ${ride.id}`,
        );

        // Create all charge meters
        await this.createAllChargeMeters(ride, metrics);

        // Store fare calculation results if available
        if (fareSpec) {
          // Reconstruct fare result for storage
          const fareResult = {
            ...fareSpec,
            cityProductId: cityProduct.id,
          };

          await this.rideFareService.createRideFareFromCalculation({
            rideId: ride.id,
            fareCalculationResult: fareResult,
          });

          this.logger.log(
            `✓ Ride meters and fare records created for ride ${ride.id}`,
          );
        } else {
          this.logger.log(
            `✓ Ride meters created for ride ${ride.id} (no fare data)`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to create ride meters and fare records for ride ${ride.id}:`,
          error,
        );
        // Don't throw - this is async and shouldn't affect ride creation
      }
    }, 0);
  }

  /**
   * Create all charge meters for a ride
   */
  private async createAllChargeMeters(
    ride: Ride,
    metrics: { duration: number | null; distance: number | null },
  ): Promise<void> {
    const rideMeters = [];

    // Trip distance meter
    if (metrics.distance !== null) {
      rideMeters.push({
        name: ChargeMeter.TRIP_DISTANCE,
        value: metrics.distance / 1000, // Convert meters to kilometers
        unit: 'km',
      });
    }

    // Trip duration meter
    if (metrics.duration !== null) {
      rideMeters.push({
        name: ChargeMeter.TRIP_DURATION,
        value: metrics.duration,
        unit: 'seconds',
      });
    }

    // Pickup distance meter (initially 0, will be updated when driver is assigned)
    rideMeters.push({
      name: ChargeMeter.PICKUP_DISTANCE,
      value: 0,
      unit: 'km',
    });

    // Pickup duration meter (initially 0, will be updated when driver is assigned)
    rideMeters.push({
      name: ChargeMeter.PICKUP_DURATION,
      value: 0,
      unit: 'seconds',
    });

    // Wait duration meters (initially 0)
    rideMeters.push({
      name: ChargeMeter.PICKUP_WAIT_DURATION,
      value: 0,
      unit: 'seconds',
    });

    rideMeters.push({
      name: ChargeMeter.TRIP_WAIT_DURATION,
      value: 0,
      unit: 'seconds',
    });

    // Store all ride meters in bulk
    if (rideMeters.length > 0) {
      await this.rideMeterService.createBulkRideMeters({
        rideId: ride.id,
        meters: rideMeters,
      });
      this.logger.log(
        `Created ${rideMeters.length} charge meters for ride ${ride.id}`,
      );
    }
  }

  /**
   * Get zones from coordinates using H3 indexing
   */
  private async getZonesFromCoordinates(
    pickup: LocationPoint,
    destination: LocationPoint,
  ): Promise<{ pickupZone: any; destinationZone: any }> {
    const H3_RESOLUTION = 8;

    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      pickup.lat,
      pickup.lng,
      H3_RESOLUTION,
    );
    const destinationH3Index = this.h3UtilityService.coordinatesToH3Index(
      destination.lat,
      destination.lng,
      H3_RESOLUTION,
    );

    const [pickupZoneData, destinationZoneData] = await Promise.all([
      this.h3IndexToZoneRepository.findByH3Index(BigInt(`0x${pickupH3Index}`)),
      this.h3IndexToZoneRepository.findByH3Index(
        BigInt(`0x${destinationH3Index}`),
      ),
    ]);

    return {
      pickupZone: pickupZoneData?.zone || null,
      destinationZone: destinationZoneData?.zone || null,
    };
  }

  /**
   * Calculate fare using the fare engine
   */
  private async calculateFareWithEngine(
    ride: Ride | null,
    cityProduct: any,
    pickupZone: any,
    destinationZone: any,
    metrics: { duration: number | null; distance: number | null },
    stops?: any[] | null,
  ): Promise<any> {
    try {
      // Calculate metrics for city product
      const fareMetrics = {
        pickupDistance: 0, // Will be updated when driver is assigned
        pickupDuration: 0, // Will be updated when driver is assigned
        pickupWaitDuration: 0,
        tripDuration: metrics.duration ? metrics.duration / 60 : 0, // Convert to minutes
        tripWaitDuration: 0,
        tripDistance: metrics.distance ? metrics.distance / 1000 : 0, // Convert to km
      };

      // Prepare city product with metrics
      const cityProductWithMetrics: CityProductWithMetrics = {
        ...cityProduct,
        metrics: fareMetrics,
      };

      // Prepare fare context
      const fareContext: FareContext = {
        pickup: pickupZone,
        destination: destinationZone,
        stops: stops || null,
        cityProducts: [cityProductWithMetrics],
        currency: 'INR',
        timestamp: new Date(),
        requestId: `ride-${ride?.id || 'request'}-${Date.now()}`,
      };

      // Call fare engine with options including commission
      const fareOptions: FareCalculationOptions = {
        skipCommission: false,
        skipTaxBreakdown: false,
        skipChargeBreakdown: false,
      };

      const fareResult = await this.fareEngineService.estimateFare(
        fareContext,
        fareOptions,
      );

      if (fareResult && fareResult.status && fareResult.data) {
        const fareCalculationResults =
          fareResult.data.fareCalculationResults || [];
        if (fareCalculationResults.length > 0) {
          return fareCalculationResults[0];
        }
      }

      this.logger.warn(
        `Fare engine returned no valid results for ${ride ? `ride ${ride.id}` : 'ride request'}`,
      );
      return null;
    } catch (error) {
      this.logger.error(
        `Error calculating fare with engine for ${ride ? `ride ${ride.id}` : 'ride request'}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update RideMeters when new destination is provided
   */
  private async updateRideMetersForNewDestination(
    ride: Ride,
    newDestination: LocationPoint,
  ): Promise<void> {
    this.logger.log(
      `Updating RideMeters for ride ${ride.id} with new destination`,
    );

    try {
      // Get current ride meters
      const currentMeters = await this.rideMeterService.getRideMetersByRideId(
        ride.id,
      );
      const meterMap = new Map(
        currentMeters.map((meter: any) => [meter.name, meter]),
      );

      // Get current trip values
      const currentTripDistance =
        meterMap.get(ChargeMeter.TRIP_DISTANCE)?.value || 0; // in km
      const currentTripDuration =
        meterMap.get(ChargeMeter.TRIP_DURATION)?.value || 0; // in seconds

      // Calculate additional distance and duration using Google Maps
      const pickup = ride.pickupLocation!;
      const { distance, duration } =
        await this.rideUtilsService.calculateDistanceAndDuration(
          pickup,
          newDestination,
        );

      if (!distance) {
        this.logger.warn(
          `No distance data returned from Google Maps for ride ${ride.id}`,
        );
        return;
      }

      const distanceKm = distance / 1000; // convert to km

      await this.rideRepository.updateById(ride.id, {
        distance,
        duration,
      });

      // Update the meters
      const tripDistanceMeter = meterMap.get(ChargeMeter.TRIP_DISTANCE);
      const tripDurationMeter = meterMap.get(ChargeMeter.TRIP_DURATION);

      if (tripDistanceMeter) {
        await this.rideMeterService.updateRideMeter(tripDistanceMeter.id, {
          value: distanceKm,
        });
        this.logger.log(
          `Updated trip distance: ${currentTripDistance}km → ${distanceKm}km`,
        );
      }

      if (tripDurationMeter) {
        await this.rideMeterService.updateRideMeter(tripDurationMeter.id, {
          value: duration || 0,
        });
        this.logger.log(
          `Updated trip duration: ${currentTripDuration}s → ${currentTripDuration + duration}s`,
        );
      }

      this.logger.log(`RideMeters updated successfully for ride ${ride.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to update RideMeters for ride ${ride.id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Calculate final fare using current RideMeter data
   */
  private async calculateFinalFareFromRideMeters(
    rideEnity: Ride,
    _finalDestination: LocationPoint,
  ): Promise<any> {
    this.logger.log(
      `Calculating final fare for ride ${rideEnity.id} using RideMeter data`,
    );

    try {
      const ride = await this.rideRepository.findById(rideEnity.id);
      if (!ride) return;
      // Get current ride meters
      const currentMeters = await this.rideMeterService.getRideMetersByRideId(
        ride.id,
      );
      const meterMap = new Map(
        currentMeters.map((meter: any) => [meter.name, meter]),
      );

      // Get actual meter values
      const tripDistance = meterMap.get(ChargeMeter.TRIP_DISTANCE)?.value || 0; // in km
      const tripDuration = meterMap.get(ChargeMeter.TRIP_DURATION)?.value || 0; // in seconds
      const tripWaitDuration =
        meterMap.get(ChargeMeter.TRIP_WAIT_DURATION)?.value || 0; // in seconds
      const pickupDistance =
        meterMap.get(ChargeMeter.PICKUP_DISTANCE)?.value || 0; // in km
      const pickupDuration =
        meterMap.get(ChargeMeter.PICKUP_DURATION)?.value || 0; // in seconds
      const pickupWaitDuration =
        meterMap.get(ChargeMeter.PICKUP_WAIT_DURATION)?.value || 0; // in seconds

      // Get city product for fare calculation
      const cityProduct = await this.cityProductRepository.findById(
        ride.cityProductId!,
      );
      if (!cityProduct) {
        throw new Error(`City product not found: ${ride.cityProductId}`);
      }

      // Get zones
      const [pickupZone, destinationZone] = await Promise.all([
        this.getZoneFromLocation(ride.pickupLocation!),
        this.getZoneFromLocation(ride.destinationLocation!),
      ]);

      // Create fare context using actual RideMeter values
      const fareContext: FareContext = {
        pickup: pickupZone,
        destination: destinationZone,
        stops: ride.stops
          ? await Promise.all(
              ride.stops.map((stop) => this.getZoneFromLocation(stop)),
            )
          : null,
        cityProducts: [
          {
            ...cityProduct,
            metrics: {
              pickupDistance,
              pickupDuration: pickupDuration / 60, // Convert seconds to minutes for fare engine
              pickupWaitDuration: pickupWaitDuration / 60, // Convert seconds to minutes for fare engine
              tripDistance,
              tripDuration: tripDuration / 60, // Convert seconds to minutes for fare engine
              tripWaitDuration: tripWaitDuration / 60, // Convert seconds to minutes for fare engine
            },
          },
        ],
        currency: 'INR',
        timestamp: new Date(),
        requestId: `ride-final-${ride.id}-${Date.now()}`,
      };

      const fareOptions: FareCalculationOptions = {
        skipCommission: false,
        skipTaxBreakdown: false,
        skipChargeBreakdown: false,
      };

      const fareResult = await this.fareEngineService.estimateFare(
        fareContext,
        fareOptions,
      );

      if (fareResult && fareResult.status && fareResult.data) {
        const fareCalculationResults = fareResult.data.fareCalculationResults;
        if (fareCalculationResults && fareCalculationResults.length > 0) {
          const fareData = fareCalculationResults[0];
          return {
            passengerFare: Number(fareData.passengerFare.toFixed(2)),
            driverEarnings: Number(fareData.driverEarnings.toFixed(2)),
            platformRevenue: Number(fareData.platformRevenue.toFixed(2)),
            currency: fareData.currency,
            ...fareData,
          };
        }
      }

      // Fallback to existing fare if calculation fails
      this.logger.warn(
        `Failed to calculate final fare for ride ${ride.id}, using existing fare`,
      );
      return ride.fareSpec;
    } catch (error) {
      this.logger.error(
        `Error calculating final fare for ride ${rideEnity.id}:`,
        error,
      );
      // Fallback to existing fare if calculation fails
      return rideEnity.fareSpec;
    }
  }

  /**
   * Handle ride completion - create payment and update driver account
   */
  private async handleRideCompletion(ride: Ride, fareSpec: any): Promise<void> {
    if (!ride.driverId || !fareSpec) {
      this.logger.warn(
        `Skipping payment/earnings processing for ride ${ride.id} - missing driver or fare data`,
      );
      return;
    }

    try {
      this.logger.log(`Processing payment and earnings for ride ${ride.id}`);

      // Extract fare amount from fare spec
      const fareAmount = fareSpec.passengerFare || fareSpec.grandTotal || 0;

      // Create payment record (initially without payment type)
      const payment = await this.paymentService.createPayment({
        rideId: ride.id,
        riderId: ride.riderId,
        driverId: ride.driverId,
        amount: fareAmount,
      });

      this.logger.log(
        `Created payment record ${payment.id} for ride ${ride.id}`,
      );

      // Calculate driver earnings breakdown
      const earningsBreakdown =
        this.driverAccountService.calculateEarnings(fareSpec);

      this.logger.log(
        `Calculated earnings breakdown for ride ${ride.id}:`,
        earningsBreakdown,
      );

      // Note: Payment type confirmation and driver account balance update
      // will happen when payment is actually confirmed (cash received or online payment processed)
      // This can be done through a separate API endpoint or webhook
    } catch (error) {
      this.logger.error(
        `Failed to process payment/earnings for ride ${ride.id}:`,
        error,
      );
      // Don't fail the ride completion if payment processing fails
    }
  }

  /**
   * Confirm payment type and update driver account balance
   * This method should be called when payment is actually confirmed (cash received or online payment processed)
   */
  async confirmRidePayment(
    rideId: string,
    paymentType: PaymentType,
  ): Promise<{ payment: any; driverAccount: any }> {
    this.logger.log(`Confirming payment for ride ${rideId} as ${paymentType}`);

    try {
      // Get the ride and its fare spec
      const ride = await this.rideRepository.findById(rideId);
      if (!ride) {
        throw new NotFoundException(`Ride with ID ${rideId} not found`);
      }

      if (!ride.fareSpec) {
        throw new BadRequestException(
          `Ride ${rideId} does not have fare information`,
        );
      }

      // Confirm payment type
      const fareAmount =
        ride.fareSpec.passengerFare || ride.fareSpec.grandTotal || 0;
      const payment = await this.paymentService.confirmPayment({
        rideId,
        paymentType,
        amount: fareAmount,
      });

      // Calculate earnings and update driver balance
      const earningsBreakdown = this.driverAccountService.calculateEarnings(
        ride.fareSpec,
      );
      const driverAccount = await this.driverAccountService.updateDriverBalance(
        ride.driverId!,
        rideId,
        paymentType,
        earningsBreakdown,
      );

      // Record earnings in the driver earnings tracking system
      try {
        // Extract tax and commission data from fareSpec
        const totalTaxesOnCharge = this.extractTotalTaxesOnCharge(
          ride.fareSpec,
        );
        const totalCommission = this.extractTotalCommission(ride.fareSpec);
        const totalTaxOnCommission = this.extractTotalTaxOnCommission(
          ride.fareSpec,
        );

        await this.driverEarningsService.recordRideEarnings({
          driverId: ride.driverId!,
          rideId,
          fareAmount,
          completedAt: ride.completedAt || new Date(),
          totalTaxesOnCharge,
          totalCommission,
          totalTaxOnCommission,
        });
        this.logger.log(
          `Recorded earnings for driver ${ride.driverId} for ride ${rideId}`,
        );
      } catch (earningsError) {
        this.logger.error(
          `Failed to record earnings for ride ${rideId}:`,
          earningsError,
        );
        // Don't fail the payment confirmation if earnings recording fails
      }

      this.logger.log(
        `Payment confirmed and driver balance updated for ride ${rideId}`,
      );

      return { payment, driverAccount };
    } catch (error) {
      this.logger.error(`Failed to confirm payment for ride ${rideId}:`, error);
      throw error;
    }
  }

  /**
   * Get zone from location coordinates
   */
  private async getZoneFromLocation(location: LocationPoint): Promise<any> {
    try {
      const h3Index = this.h3UtilityService.coordinatesToH3Index(
        location.lat,
        location.lng,
        8,
      );
      const h3ToZone = await this.h3IndexToZoneRepository.findByH3Index(
        BigInt(`0x${h3Index}`),
      );
      return h3ToZone?.zone || null;
    } catch (error) {
      this.logger.warn(
        `Failed to get zone for location ${location.lat}, ${location.lng}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Update actual trip duration in RideMeter
   */
  private async updateActualTripDuration(
    rideId: string,
    actualDuration: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Updating actual trip duration for ride ${rideId}: ${actualDuration}s`,
      );

      // Get current ride meters
      const currentMeters =
        await this.rideMeterService.getRideMetersByRideId(rideId);
      const meterMap = new Map(
        currentMeters.map((meter: any) => [meter.name, meter]),
      );

      // Update trip duration meter with actual duration
      const tripDurationMeter = meterMap.get(ChargeMeter.TRIP_DURATION);
      if (tripDurationMeter) {
        await this.rideMeterService.updateRideMeter(tripDurationMeter.id, {
          value: actualDuration,
        });
        this.logger.log(
          `Updated trip duration meter: ${tripDurationMeter.value}s → ${actualDuration}s`,
        );
      } else {
        this.logger.warn(`Trip duration meter not found for ride ${rideId}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update actual trip duration for ride ${rideId}:`,
        error,
      );
      // Don't throw - this is not critical for ride completion
    }
  }

  /**
   * Update RideFare table with final fare calculation
   */
  private async updateRideFareRecord(
    rideId: string,
    fareSpec: any,
  ): Promise<void> {
    try {
      this.logger.log(`Updating RideFare record for ride ${rideId}`);

      // Create or update ride fare record with final calculation
      await this.rideFareService.createRideFareFromCalculation({
        rideId,
        fareCalculationResult: fareSpec,
      });

      this.logger.log(
        `RideFare record updated successfully for ride ${rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update RideFare record for ride ${rideId}:`,
        error,
      );
      // Don't throw - this is not critical for ride completion
    }
  }

  /**
   * Extract total taxes on charges from fareSpec
   */
  private extractTotalTaxesOnCharge(fareSpec: any): number {
    if (
      !fareSpec?.chargeBreakdown ||
      !Array.isArray(fareSpec.chargeBreakdown)
    ) {
      return 0;
    }

    return fareSpec.chargeBreakdown.reduce((total: number, charge: any) => {
      const appliedTaxes = charge.appliedTaxes?.[0]?.subcategoryResults || [];
      const chargeTax = appliedTaxes.reduce(
        (sum: number, tax: any) => sum + (tax.taxAmount || 0),
        0,
      );
      return total + chargeTax;
    }, 0);
  }

  /**
   * Extract total commission from fareSpec
   */
  private extractTotalCommission(fareSpec: any): number {
    if (
      !fareSpec?.commissionBreakdown ||
      !Array.isArray(fareSpec.commissionBreakdown)
    ) {
      return 0;
    }

    return fareSpec.commissionBreakdown.reduce(
      (total: number, commission: any) => {
        return total + (commission.netCommissionAmount || 0);
      },
      0,
    );
  }

  /**
   * Extract total tax on commission from fareSpec
   */
  private extractTotalTaxOnCommission(fareSpec: any): number {
    if (
      !fareSpec?.commissionBreakdown ||
      !Array.isArray(fareSpec.commissionBreakdown)
    ) {
      return 0;
    }

    return fareSpec.commissionBreakdown.reduce(
      (total: number, commission: any) => {
        const taxResults = commission.taxCalculation?.subcategoryResults || [];
        const commissionTax = taxResults.reduce(
          (sum: number, tax: any) => sum + (tax.taxAmount || 0),
          0,
        );
        return total + commissionTax;
      },
      0,
    );
  }
}

import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { ROUTE_URLS } from '@/data/route-urls';

export const PAGE_ROUTE_PERMISSIONS = [
   {
      url: ROUTE_URLS.DASHBOARD_DRIVERS,
      pagePermission: RBAC_PERMISSIONS.DRIVER,
   },
   //    /dashboard/drivers/:id
   {
      url: ROUTE_URLS.DASHBOARD_DRIVERS_DETAILS,
      pagePermission: RBAC_PERMISSIONS.DRIVER_DETAILS,
   },
   {
      url: ROUTE_URLS.DASHBOARD_DRIVER_RADAR,
      pagePermission: RBAC_PERMISSIONS.DRIVER_RADAR,
   },
   {
      url: ROUTE_URLS.DASHBOARD_DRIVERS,
      pagePermission: RBAC_PERMISSIONS.DRIVER,
   },
   {
      url: ROUTE_URLS.DASHBOARD_RIDERS,
      pagePermission: RBAC_PERMISSIONS.RIDER,
   },
   //    /dashboard/riders/:id
   {
      url: ROUTE_URLS.DASHBOARD_RIDERS_DETAILS,
      pagePermission: RBAC_PERMISSIONS.RIDER_DETAILS,
   },
   {
      url: ROUTE_URLS.DASHBOARD_CITIES,
      pagePermission: RBAC_PERMISSIONS.CITY,
   },
   //    /dashboard/cities/:id
   {
      url: ROUTE_URLS.DASHBOARD_CITIES_DETAILS,
      pagePermission: RBAC_PERMISSIONS.CITY,
   },
   {
      url: ROUTE_URLS.DASHBOARD_LANGUAGES,
      pagePermission: RBAC_PERMISSIONS.LANGUAGE,
   },
   {
      url: ROUTE_URLS.DASHBOARD_PRODUCTS,
      pagePermission: RBAC_PERMISSIONS.PRODUCT,
   },
   {
      url: ROUTE_URLS.DASHBOARD_PRODUCT_SERVICES,
      pagePermission: RBAC_PERMISSIONS.PRODUCT_SERVICE,
   },
   {
      url: ROUTE_URLS.DASHBOARD_VEHICLE_CATEGORIES,
      pagePermission: RBAC_PERMISSIONS.VEHICLE_CATEGORY,
   },
   {
      url: ROUTE_URLS.DASHBOARD_ZONE_TYPES,
      pagePermission: RBAC_PERMISSIONS.ZONE_TYPE,
   },
   {
      url: ROUTE_URLS.DASHBOARD_ROLES,
      pagePermission: RBAC_PERMISSIONS.ROLES,
   },
   {
      url: ROUTE_URLS.DASHBOARD_ADMINS,
      pagePermission: { ...RBAC_PERMISSIONS.SUB_ADMIN, ...RBAC_PERMISSIONS.CITY_ADMIN },
   },
   {
      url: ROUTE_URLS.DASHBOARD_CHARGE_GROUPS,
      pagePermission: RBAC_PERMISSIONS.CHARGE_GROUPS,
   },
   {
      url: ROUTE_URLS.DASHBOARD_CHARGES,
      pagePermission: RBAC_PERMISSIONS.CHARGE,
   },
   {
      url: ROUTE_URLS.DASHBOARD_RIDES,
      pagePermission: RBAC_PERMISSIONS.RIDES,
   },
   {
      url: ROUTE_URLS.DASHBOARD_TAX_GROUPS,
      pagePermission: RBAC_PERMISSIONS.TAX_GROUP,
   },
   {
      url: ROUTE_URLS.DASHBOARD_COMMISSIONS,
      pagePermission: RBAC_PERMISSIONS.COMMISSION,
   },
   {
      url: ROUTE_URLS.DASHBOARD_DRIVER_EARNINGS,
      pagePermission: RBAC_PERMISSIONS.DRIVER_EARNINGS,
   },
   {
      url: ROUTE_URLS.DASHBOARD_BOOK_RIDE,
      pagePermission: RBAC_PERMISSIONS.BOOK_RIDE,
   },
];

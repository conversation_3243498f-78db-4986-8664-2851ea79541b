import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export enum DriverStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISABLED = 'disabled',
  PENDING = 'pending',
}

export class ChangeDriverStatusDto {
  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.ACTIVE,
    description: 'New status for the driver profile',
  })
  @IsNotEmpty()
  @IsEnum(DriverStatus)
  status!: DriverStatus;
}
